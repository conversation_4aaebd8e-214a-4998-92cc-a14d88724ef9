package com.caidaocloud.hr.service.organization.application.tenant.feign;

import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPageConfigDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.tenant.TenantLogoDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.tenant.TenantRuleDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.tenant.TenantRuleGuideDto;
import com.caidaocloud.hr.service.organization.interfaces.vo.tenant.TenantVo;
import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.hrpaas.paas.match.vo.ConditionDataVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "caidaocloud-hr-paas-service", fallback = TenantFeignFallBack.class, configuration = FeignConfiguration.class)
public interface ITenantFeignClient {
    @PostMapping("/api/hrpaas/metadata/tenant/v1/logo")
    Result logoSet(@RequestBody TenantLogoDto dto);

    @PostMapping("/api/hrpaas/metadata/tenant/v1/orgRule")
    Result orgRuleSet(@RequestBody TenantRuleDto dto);

    @GetMapping("/api/hrpaas/metadata/tenant/v1/getOrg")
    Result<TenantVo> getOrgSet();

    @PostMapping("/api/hrpaas/metadata/tenant/v1/orgRuleGuide")
    Result orgRuleGuide(@RequestBody TenantRuleGuideDto tenantRuleDto);

//  查询加密信息
    @GetMapping("/api/hrpaas/v1/data/encrypt/searchable")
    Result getEncryptSearchable(@RequestParam("str") String str);

//  批量查询加密信息
    @PostMapping("/api/hrpaas/v1/data/encrypt/searchable/batch")
    Result batchSearchableEncrypt(@RequestBody List<String> strList);

    @PostMapping(value = "/api/hrpaas/uiform/data/v1/cityProvinceInfo")
    Result cityProvinceInfo(@RequestParam String tenantId);

    @GetMapping(value = "/api/hrpaas/v1/kv/detail")
    Result<String> getKv(@RequestParam String key);

    @PostMapping(value = "/api/hrpaas/v1/kv/save")
    Result<KvDto> saveKv(@RequestBody KvDto kv);

    @GetMapping({"/api/hrpaas/condition/v1/property"})
    Result<List<ConditionDataVo>> getConditionDataByCode(@RequestParam("code") String code, @RequestParam(value = "showDisable",defaultValue = "false") boolean showDisable);

    @GetMapping("/api/hrpaas/page/v1/detail")
    Result<EmpPageConfigDto> fetchEmpPageConfig(@RequestParam String pageId);


}
