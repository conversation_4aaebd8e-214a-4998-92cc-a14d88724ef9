package com.caidaocloud.hr.service.employee.domain.emp.manage.entity;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpPrivateInfoRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.BeanUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Data
@Service
public class EmpPrivateInfoDo extends DataSimple {
    /**
     * 员工ID
     */
    private String empId;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工英文名
     */
    private String enName;

    /**
     * 性别
     */
    private DictSimple sex;

    /**
     * 国籍
     */
    private DictSimple nationality;

    /**
     * 民族
     */
    private DictSimple nation;

    /**
     * 籍贯
     */
    private Address birthPlace;

    /**
     * 籍贯(弃用)
     */
    private ProvinceCity nativePlace;

    /**
     * 户口类型
     */
    private DictSimple familyType;

    /**
     * 户籍地址
     */
    private String permanentAddress;

    /**
     * 出生日期
     */
    private Long birthDate;

    /**
     * 年龄
     */
    private Integer divisionAge;

    /**
     * 婚姻状态
     */
    private EnumSimple maritalStatus;

    /**
     * 生育状态
     */
    private EnumSimple fertilityStatus;

    /**
     * 政治面貌
     */
    private DictSimple politicalOutlook;

    /**
     * 手机号
     */
    private PhoneSimple phone;

    /**
     * 员工个人邮箱
     */
    private String email;

    /**
     * 通讯地址
     */
    private String postalAddress;

    /**
     * 证件类型
     */
    private EnumSimple cardType;

    /**
     * 证件号
     */
    private String cardNo;

    /**
     * 证件有效日期
     */
    private Long cardEffectiveDate;

    /**
     * 是否残疾
     */
    private Boolean disability;

    /**
     * 监护人姓名
     */
    private String guardianName;

    /**
     * 监护人手机
     */
    private PhoneSimple guardianPhone;

    /**
     * 监护人邮箱
     */
    private String guardianEmail;
    /**
     * 残疾认证等级
     */
    private DictSimple disabilityLevelType;

    private final static String IDENTIFIER = "entity.hr.EmpPrivateInfo";

    @Resource
    private IEmpPrivateInfoRepository empPrivateInfoRepository;

    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    public List<EmpPrivateInfoDo> getListByEmpId(List<String> empIds) {
        return empPrivateInfoRepository.selectBatchIds(empIds, IDENTIFIER);
    }

    public String save(EmpPrivateInfoDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
        data.setTenantId(tenantId);
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setIdentifier(IDENTIFIER);
        data.setDeleted(Boolean.FALSE);
        return empPrivateInfoRepository.insert(data).getBid();
    }

    public void update(EmpPrivateInfoDo data) {
        EmpPrivateInfoDo dbData = getByEmpId(data.getEmpId());
        if (dbData == null || dbData.getEmpId() == null) {
            save(data);
        } else {
            UserInfo userInfo = UserContext.preCheckUser();
            data.setUpdateBy(userInfo.getUserid().toString());
            data.setUpdateTime(System.currentTimeMillis());
            BeanUtil.copyProperties(data, dbData, "bid", "id", "identifier", "entityId", "tenantId", "createTime", "createBy",
                    "deleted", "dataStartTime", "dataEndTime");
            empPrivateInfoRepository.updateById(dbData);
        }
    }

    public void updateDoOnly(EmpPrivateInfoDo data, EmpPrivateInfoDo old){
        if (old == null || old.getEmpId() == null) {
            String bid = save(data);
            data.setBid(bid);
            return;
        }
        UserInfo userInfo = UserContext.preCheckUser();
        data.setUpdateBy(userInfo.getUserid().toString());
        data.setUpdateTime(System.currentTimeMillis());
        BeanUtil.copyProperties(data, old, "bid", "id", "identifier", "entityId", "tenantId", "createTime", "createBy",
                "deleted", "dataStartTime", "dataEndTime");
        empPrivateInfoRepository.updateById(old);
    }

    public void saveBatch(List<EmpPrivateInfoDo> list){
        empPrivateInfoRepository.insertBatch(list, IDENTIFIER);
    }

    public EmpPrivateInfoDo getByEmpId(String empId) {
        List<EmpPrivateInfoDo> list = getListByEmpId(Lists.newArrayList(empId));
        if (CollectionUtils.isEmpty(list)) {
            return new EmpPrivateInfoDo();
        }
        return list.get(0);
    }

    public List<EmpPrivateInfoDo> getAllListByEmpId(List<String> empIds) {
        return empPrivateInfoRepository.selectBatchAllIds(empIds, IDENTIFIER);
    }
    public List<EmpPrivateInfoDo> getAllListByPhones(List<String> phones) {
        return empPrivateInfoRepository.selectBatchAllPhoneS(phones, IDENTIFIER);
    }
    public List<EmpPrivateInfoDo> getAllListByCardNos(List<String> cardNos) {
        return empPrivateInfoRepository.selectBatchAllCardNos(cardNos, IDENTIFIER);
    }

    public PageResult<EmpPrivateInfoDo> selectHasGuardian(BasePage page){
        return empPrivateInfoRepository.selectHasGuardian(IDENTIFIER, page);
    }
    public List<EmpPrivateInfoDo> getAllEmpPrivateInfoByEmpIds(List<String> empIds){
        return empPrivateInfoRepository.getAllEmpPrivateInfoByEmpIds(IDENTIFIER, empIds);
    }

    public void batchDeleteByIds(List<String> bids) {
        empPrivateInfoRepository.batchDeleteByIds(bids, IDENTIFIER);
    }

    public PageResult<EmpPrivateInfoDo> selectPage(BasePage page) {
        UserInfo userInfo = UserContext.preCheckUser();
        EmpPrivateInfoDo data = new EmpPrivateInfoDo();
        data.setIdentifier(IDENTIFIER);
        data.setTenantId(userInfo.getTenantId());
        return empPrivateInfoRepository.selectPage(page, data);
    }
}
