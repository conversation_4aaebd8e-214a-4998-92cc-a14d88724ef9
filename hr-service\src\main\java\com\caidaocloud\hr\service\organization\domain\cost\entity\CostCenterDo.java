package com.caidaocloud.hr.service.organization.domain.cost.entity;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.domain.cost.repository.ICostCenterRepository;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.cost.CostCenterQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.vo.cost.CostCenterSimpleVo;
import com.caidaocloud.hr.service.organization.interfaces.vo.cost.CostCenterVo;
import com.caidaocloud.hr.service.organization.interfaces.vo.workplace.WorkplaceVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 成本中心
 *
 * <AUTHOR>
 * @Date 2021/11/23
 */
@Slf4j
@Data
@Service
public class CostCenterDo extends DataEntity {
    /**
     * 成本中心名称
     */
    private String costCenterName;
    /**
     * 成本中心名称多语言
     */
    private String i18nCostCenterName;

    /**
     * 成本中心编码
     */
    private String costCenterCode;

    /**
     * 上级成本中心
     */
    private TreeParent pid;

    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司名称多语言
     */
    private String i18nCompanyName;

    /**
     * 公司代码
     */
    private String companyCode;

    /**
     * 控制范围名称
     */
    private String controlRangeName;
    /**
     * 控制范围名称多语言
     */
    private String i18nControlRangeName;

    /**
     * 控制范围
     */
    private String controlRange;
    /**
     * 控制范围多语言
     */
    private String i18nControlRange;

    /**
     * 负责人
     */
    private EmpSimple leader;

    private final static String COST_CENTER_IDENTIFIER = "entity.hr.CostCenter";

    @Resource
    private ICostCenterRepository costCenterRepository;

    private void check(CostCenterDo data) {
        PreCheck.preCheckArgument(null == data || null == data.getBid(), "成本中心不存在");
    }

    /**
     * 新增
     *
     * @param data
     * @return
     */
    public String save(CostCenterDo data) {
        DataEntity.initFieldValue(COST_CENTER_IDENTIFIER, BusinessEventTypeEnum.CREATE, data, null);
        return costCenterRepository.insert(data).getBid();
    }

    /**
     * 编辑
     *
     * @param data
     */
    public void update(CostCenterDo data) {
        CostCenterDo dbData = selectById(data.getBid(), data.getDataStartTime());
        DataEntity.initFieldValue(COST_CENTER_IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        costCenterRepository.updateById(data);
    }

    /**
     * 删除
     *
     * @param data
     */
    public void delete(CostCenterDo data) {
        data.setIdentifier(COST_CENTER_IDENTIFIER);
        costCenterRepository.delete(data);
    }

    /**
     * 更新状态
     */
    public void updateStatus(CostCenterDo data, BusinessEventTypeEnum eventTypeEnum, Boolean updateChildren) {
        if (eventTypeEnum != BusinessEventTypeEnum.DISABLE && eventTypeEnum != BusinessEventTypeEnum.ENABLE) {
            return;
        }
        CostCenterDo dbData = selectById(data.getBid(), data.getDataStartTime());
        check(dbData);
        DataEntity.initFieldValue(COST_CENTER_IDENTIFIER, eventTypeEnum, data, dbData);
        // 校验
        if (eventTypeEnum == BusinessEventTypeEnum.DISABLE) {
            // 停用：检查是否存在已启用的下级，如果存在则不允许停用
            List<CostCenterDo> childrenList = selectChildrenList(data.getDataStartTime(), data.getBid(), StatusEnum.ENABLED);
            PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(childrenList), "被停用的成本中心下有启用中的成本中心，请先停用下级成本中心");
            costCenterRepository.updateById(data);
            return;
        }
        // 启用：通过参数判断是否同时启用下级（所有下级），下级数据启用的生效日期和启用的父级生效日期保持一致
        costCenterRepository.updateById(data);
        if (updateChildren == null || !updateChildren) {
            return;
        }
        List<CostCenterDo> childrenList = selectAllChildrenList(data.getBid(), data.getDataStartTime());
        if (CollectionUtils.isEmpty(childrenList)) {
            return;
        }
        childrenList.forEach(children -> {
            children.setUpdateBy(data.getUpdateBy());
            children.setUpdateTime(data.getUpdateTime());
            children.setStatus(data.getStatus());
            children.setDataStartTime(data.getDataStartTime());
            costCenterRepository.updateById(children);
        });
    }

    /**
     * 查看详情
     *
     * @param bid
     * @param dateTime
     * @return
     */
    public CostCenterDo selectById(String bid, Long dateTime) {
        CostCenterDo data = costCenterRepository.selectById(bid, COST_CENTER_IDENTIFIER, DataEntity.getTimeIfNullSetDefault(dateTime));
        if(null == data){
            return data;
        }

        if(null != data.getLeader() && StringUtil.isEmpty(data.getLeader().getEmpId())){
            data.setLeader(null);
        }

        return data;
    }

    /**
     * 分页
     *
     * @param page
     * @return
     */
    public PageResult<CostCenterDo> selectPage(BasePageQueryDto page) {
        UserInfo userInfo = UserContext.preCheckUser();
        CostCenterDo data = new CostCenterDo();
        data.setIdentifier(COST_CENTER_IDENTIFIER);
        data.setTenantId(userInfo.getTenantId());
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(null == page.getStatus() ? null : page.getStatus().toString());
        data.setStatus(enumSimple);
        data.setDataStartTime(DataEntity.getTimeIfNullSetDefault(page.getDateTime()));
        return costCenterRepository.selectPage(page, data);
    }

    /**
     * 查询树列表
     *
     * @return
     */
    public List<TreeData<CostCenterDo>> tree(CostCenterQueryDto queryDto) {
        return costCenterRepository.getTreeList(COST_CENTER_IDENTIFIER, queryDto);
    }

    public PageResult<CostCenterDo> selectFilterPage(CostCenterQueryDto dto) {
        return costCenterRepository.selectFilterPage(COST_CENTER_IDENTIFIER, dto);
    }

    /**
     * 查询树列表（简单信息）
     *
     * @return
     */
    public List<TreeData<LabelData>> simpleTree(Long dateTime, Integer status) {
        return costCenterRepository.getTreeSimpleList(COST_CENTER_IDENTIFIER, DataEntity.getTimeIfNullSetDefault(dateTime), status);
    }

    /**
     * 查询直属下级
     *
     * @param dateTime
     * @param bid
     * @param statusEnum
     * @return
     */
    public List<CostCenterDo> selectChildrenList(Long dateTime, String bid, StatusEnum statusEnum) {
        return costCenterRepository.selectChildrenList(dateTime, bid, COST_CENTER_IDENTIFIER, UserContext.getTenantId(), statusEnum.getIndex());
    }

    /**
     * 查询所有下级
     *
     * @param bid
     * @param dateTime
     * @return
     */
    public List<CostCenterDo> selectAllChildrenList(String bid, Long dateTime) {
        return costCenterRepository.selectAllChildrenList(COST_CENTER_IDENTIFIER, bid, dateTime);
    }

    public List<CostCenterDo> selectByCode(String bid, String code, Long dataTime) {
        return costCenterRepository.selectByCode(COST_CENTER_IDENTIFIER, bid, code, dataTime);
    }

    public static void i18nConvert( CostCenterDo costCenterDo,CostCenterVo costCenterVo) {
        costCenterVo.setI18nCostCenterName(StringUtil.isEmpty(costCenterDo.getI18nCostCenterName()) ?
                ImmutableMap.of("default", Optional.ofNullable(costCenterDo.getCostCenterName()).orElse("")) :
                FastjsonUtil.toObject(costCenterDo.getI18nCostCenterName(), new TypeReference<Map<String, Object>>(){})
        );
        costCenterVo.setI18nCompanyName(StringUtil.isEmpty(costCenterDo.getI18nCompanyName()) ?
                ImmutableMap.of("default", Optional.ofNullable(costCenterDo.getCompanyName()).orElse("")) :
                FastjsonUtil.toObject(costCenterDo.getI18nCompanyName(), new TypeReference<Map<String, Object>>(){})
        );
        costCenterVo.setI18nControlRangeName(StringUtil.isEmpty(costCenterDo.getI18nControlRangeName()) ?
                ImmutableMap.of("default", Optional.ofNullable(costCenterDo.getControlRangeName()).orElse("")) :
                FastjsonUtil.toObject(costCenterDo.getI18nControlRangeName(), new TypeReference<Map<String, Object>>(){})
        );
        costCenterVo.setI18nControlRange(StringUtil.isEmpty(costCenterDo.getI18nControlRange()) ?
                ImmutableMap.of("default", Optional.ofNullable(costCenterDo.getControlRange()).orElse("")) :
                FastjsonUtil.toObject(costCenterDo.getI18nControlRange(), new TypeReference<Map<String, Object>>(){})
        );
    }

    public static void i18nConvert(CostCenterDo costCenterDo, CostCenterSimpleVo costCenterVo) {
        costCenterVo.setI18nCostCenterName(StringUtil.isEmpty(costCenterDo.getI18nCostCenterName()) ?
                ImmutableMap.of("default", Optional.ofNullable(costCenterDo.getCostCenterName()).orElse("")) :
                FastjsonUtil.toObject(costCenterDo.getI18nCostCenterName(), new TypeReference<Map<String, Object>>(){})
        );
    }
}
