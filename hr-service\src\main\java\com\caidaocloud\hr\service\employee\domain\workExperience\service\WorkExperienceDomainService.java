package com.caidaocloud.hr.service.employee.domain.workExperience.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.domain.workExperience.entity.WorkExperienceDo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class WorkExperienceDomainService {
    @Resource
    private WorkExperienceDo workExperienceDo;

    public String save(WorkExperienceDo data) {
        PreCheck.preCheckArgument(Objects.isNull(data.getStartDate()) || Objects.isNull(data.getEndDate()) || data.getStartDate() > data.getEndDate(), "开始日期不能大于结束日期");
        return workExperienceDo.save(data);
    }

    public int update(WorkExperienceDo data) {
        PreCheck.preCheckArgument(Objects.isNull(data.getStartDate()) || Objects.isNull(data.getEndDate()) || data.getStartDate() > data.getEndDate(), "开始日期不能大于结束日期");
        return workExperienceDo.updateById(data);
    }

    public WorkExperienceDo selectById(String bid) {
        return workExperienceDo.selectById(bid);
    }

    public int delete(WorkExperienceDo data) {
        return workExperienceDo.deleteById(data);
    }

    public List<WorkExperienceDo> selectList(String empId) {
        return workExperienceDo.selectList(empId);
    }
}
