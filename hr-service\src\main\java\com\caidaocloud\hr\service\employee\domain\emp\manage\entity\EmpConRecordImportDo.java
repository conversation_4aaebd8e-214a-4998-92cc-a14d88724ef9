package com.caidaocloud.hr.service.employee.domain.emp.manage.entity;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpConRecordImportRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.List;

@Slf4j
@Data
@Service
public class EmpConRecordImportDo {

    /**
     * 合同id
     */
    private String bid;

//    private EmpSimple owner;

//    private String empId;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 工号
     */
    private String workno;

    /**
     * 签订类型
     */
    private EnumSimple signType;

    private String signTypeTxt;

    /**
     * 合同公司
     */
    private String company;

    /**
     * 合同公司代码
     */
    private String companyCode;

    /**
     * 合同公司名称
     */
    private String companyTxt;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同类型
     */
    private DictSimple contractSettingType;

    /**
     * 合同设置Bid
     */
    private String contractTypeSet;

    /**
     * 合同设置名称（合同类型名称/合同名称）
     */
    private String contractTypeSetTxt;

    /**
     * 合同类型Txt
     */
    private String contractSettingTypeTxt;

    /**
     * 合同期限类型
     */
    private EnumSimple periodType;

    private String periodTypeTxt;

    /**
     * 合同开始日期
     */
    private Long startDate;
    private String startDateTxt;

    /**
     * 合同结束日期
     */
    private Long endDate;

    private String endDateTxt;

    /**
     * 试用期
     */
    private Integer probationPeriod;

    /**
     * 试用期截止日期
     */
    private Long probationPeriodEndDate;

    /**
     * 试用期截止日期文字
     */
    private String probationPeriodEndDateTxt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合同签订次数
     */
    private String signTimeTxt;
//    private Integer signTime;

    /**
     * 审批状态
     */
    private EnumSimple approvalStatus;

    private String approvalStatusTxt;

    // ---------------------------------------------------

//    /**
//     * 入职日期
//     */
//    private Long hireDate;

//    /**
//     * 员工状态
//     */
//    private EnumSimple empStatus;

//    /**
//     * 所属组织Id
//     */
//    private String organize;

//    /**
//     * 所属组织名称
//     */
//    private String organizeTxt;

//    /**
//     * 关联的职务ID
//     */
//    private String job;

//    /**
//     * 关联的职务名称
//     */
//    private String jobTxt;

//    /**
//     * 岗位ID
//     */
//    private String post;

//    /**
//     * 岗位名称
//     */
//    private String postTxt;

//    /**
//     * 员工类型
//     */
//    private DictSimple empType;

//    /**
//     * 合同期（月）
//     */
//    private Integer contractPeriod;

//    /**
//     * 状态
//     */
//    private EnumSimple contractStatus;

    // -------------------------------------------------------------

    /**
     * 字段空检验
     */
    private boolean checkEmpty = false;

    /**
     * 字段空检验提示
     */
    private String checkEmptyTips;

    /**
     * 合同类别
     */
    private DictSimple contractType;

    /**
     * 是否需要审批
     */
    private String needApprove;

    @Resource
    private IEmpConRecordImportRepository empConRecordImportRepository;

    public List<EmpConRecordImportDo> getEmpConRecordImportDoFromExcel(InputStream inputStream) {
        return ObjectConverter.convertList(empConRecordImportRepository.getEmpConRecordImportDoFromExcel(inputStream), EmpConRecordImportDo.class);
    }
}
