package com.caidaocloud.hr.service.employee.infrastructure.emp.repository.po;


import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class EmpConRecordImportPo {

    /**
     * 员工姓名
     */
    @Excel(name = "姓名", width = 15, orderNum = "0")
    private String name;

    /**
     * 工号
     */
    @Excel(name = "工号", width = 10, orderNum = "1")
    private String workno;

    /**
     * 签订类型
     */
    @Excel(name = "签订类型", width = 10, orderNum = "2")
    private String signTypeTxt;

    /**
     * 合同公司
     */
    @Excel(name = "合同公司", width = 15, orderNum = "3")
    private String companyTxt;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号", width = 10, orderNum = "4")
    private String contractNo;

    /**
     * 合同类型
     */
    @Excel(name = "合同类型", width = 10, orderNum = "5")
    private String contractSettingTypeTxt;

    /**
     * 合同开始日期
     */
    @Excel(name = "合同开始日期", width = 10, orderNum = "7", importFormat = "yyyy/MM/dd")
    private String startDateTxt;

    /**
     * 合同结束日期
     */
    @Excel(name = "合同结束日期", width = 10, orderNum = "21", importFormat = "yyyy/MM/dd")
    private String endDateTxt;

    @Excel(name = "试用期（月）", width = 10, orderNum = "21", type = 10)
    private Integer probationPeriod;

    @Excel(name = "试用期截止日期", width = 10, orderNum = "21", importFormat = "yyyy/MM/dd")
    private String probationPeriodEndDateTxt;

    @Excel(name = "备注", width = 10, orderNum = "21")
    private String remark;

    /**
     * 审批状态
     */
    @Excel(name = "审批状态", width = 10, orderNum = "24")
    private String approvalStatusTxt;

    /**
     * 字段空检验
     */
    private boolean checkEmpty = false;

    /**
     * 字段空检验提示
     */
    @Excel(name = "错误原因", width = 30, orderNum = "42")
    private String checkEmptyTips;

    /**
     * 是否需要审批
     */
    @Excel(name = "是否需要审批", width = 30, orderNum = "50")
    private String needApprove;
}
