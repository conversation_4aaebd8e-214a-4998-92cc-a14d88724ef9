package com.caidaocloud.hr.service.employee.application.feign.fallback;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.common.application.dto.MappingConfigDto;
import com.caidaocloud.hr.service.employee.application.feign.oboarding.emp.EmpPrivateFeignClient;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpEntryProcessDisableDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpEntryProcessDisableVo;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPrivacyQueryDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class EmpPrivateFallBack implements EmpPrivateFeignClient {

    @Override
    public Result getPrivacyInfoList(EmpPrivacyQueryDto dto) {
        return Result.fail("根据 手机|证件号|姓名 获取onBoarding 个人信息失败");
    }

    @Override
    public Result getEmpProcessInfoList(EmpPrivacyQueryDto dto) {
        return Result.fail("根据 手机|证件号|姓名 获取onBoarding 流程信息 失败");
    }

    @Override
    public Result<PageResult<EmpEntryProcessDisableVo>> getDisableEmpEntryProcessPage(EmpEntryProcessDisableDto disableDto) {
        return Result.fail();
    }

    @Override
    public Result<List<MappingConfigDto>> listByIds(String ids) {
        return Result.fail();
    }


}
