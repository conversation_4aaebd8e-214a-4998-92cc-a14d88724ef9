package com.caidaocloud.hr.service.organization.domain.jobgrade.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.growthrecord.domain.service.GrowthRecordDomainService;
import com.caidaocloud.hr.service.organization.domain.common.event.CommonChangeEvent;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeChannelDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.jobgrade.JobGradeDto;
import com.caidaocloud.hr.service.search.application.event.EntityDataChange;
import com.caidaocloud.hr.service.util.EntityDataUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 职级管理领域服务
 *
 * <AUTHOR>
 * @Date 2021/11/30
 */
@Slf4j
@Service
public class JobGradeDomainService {
    @Resource
    private JobGradeChannelDo jobGradeChannelDo;
    @Resource
    private JobGradeDo jobGradeDo;
    @Resource
    private PostDo postDo;

    /**
     * 保存
     *
     * @param data
     * @return
     */
    public String saveJobGrade(JobGradeDo data) {
        JobGradeChannelDo channelDo = jobGradeChannelDo.selectById(data.getChannelId());
        PreCheck.preCheckArgument(null == channelDo, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30080));
        return jobGradeDo.save(data);
    }

    /**
     * 修改
     *
     * @param data
     */
    public void updateJobGrade(JobGradeDo data) {
        JobGradeChannelDo channelDo = jobGradeChannelDo.selectById(data.getChannelId());
        PreCheck.preCheckArgument(null == channelDo, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30080));
        jobGradeDo.update(data);
    }

    /**
     * 删除
     *
     * @param data
     */
    public void deleteJobGrade(JobGradeDo data) {
        jobGradeDo.delete(data);
    }

    public void updateStatus(JobGradeDo data, BusinessEventTypeEnum eventTypeEnum) {
        jobGradeDo.updateStatus(data, eventTypeEnum);
    }

    /**
     * 查看明细
     *
     * @param bid
     * @return
     */
    public JobGradeDo selectJobGradeById(String bid) {
        return jobGradeDo.selectById(bid);
    }

    /**
     * 根据职级通道ID查询职级列表
     *
     * @return
     */
    public List<JobGradeDo> selectJobGradeList(String channelId, Integer status) {
        return jobGradeDo.selectList(channelId, status);
    }

    /**
     * 查询职级通道下的已经设置的职等集合
     *
     * @param channelId
     * @return
     */
    public List<Integer> getJobGradeLevelList(String channelId) {
        return jobGradeDo.getJobGradeLevelList(channelId);
    }

    public List<JobGradeDo> selectJobGradeListByIds(List<String> bids) {
        return jobGradeDo.selectBatchIds(bids);
    }

    public void dragSort(List<JobGradeDo> dataList) {
        jobGradeDo.dragSort(dataList);
    }

    public List<JobGradeDo> selectAll(Integer status){
        return jobGradeDo.selectAll(status);
    }

    public List<JobGradeDo> selectByCodes(List<String> codes) {
        return jobGradeDo.selectByCodes(codes);
    }

    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    /**
     * *岗位设置-职级、基准岗位-职级、职务管理-职级/职等、员工信息-任职信息-职
     *  * 级、员工信息-成长记录-职级；
     * @param data
     */
    public void sync(List<EntityDataChange> data) {
        long dataTime = DateUtil.getMidnightTimestamp();
        CommonChangeEvent cce = new CommonChangeEvent(6);
        data.forEach(entityData -> {
            EntityDataDto after = entityData.getAfter();
            EntityDataDto before = entityData.getBefore();

            if (after == null || after.isDeleted()) {
                return;
            }
            // 生效的职级信息
            JobGradeDto beforeData = null;
            if (before != null) {
                beforeData = EntityDataUtil.convertEntityData(before, JobGradeDto.class);
            }

            JobGradeDto afterData = EntityDataUtil.convertEntityData(after, JobGradeDto.class);
            if(null == before && null != after){
                // 新增
                cce.setEventObject(afterData);
                cce.setBid(afterData.getBid());
                cce.setName(afterData.getJobGradeName());
                cce.setTenantId(after.getTenantId());
                cce.setDataStartTime(dataTime);
            } else if(after != null && !after.isDeleted()) {
                // 修改
                if (!Objects.equals(beforeData.getJobGradeName(), afterData.getJobGradeName())) {
                    cce.setEventObject(afterData);
                    cce.setBid(afterData.getBid());
                    cce.setName(afterData.getJobGradeName());
                    cce.setTenantId(after.getTenantId());
                    cce.setDataStartTime(dataTime);
                    cce.setChanged(true);
                } else if (StringUtil.isNotEmpty(cce.getBid())
                        && !Objects.equals(cce.getName(), afterData.getJobGradeName())) {
                    cce.setChanged(true);
                }
            }
        });

        if (cce.isChanged()) {
            doJobgradeChange(cce);
        }
    }

    public void doJobgradeChange(CommonChangeEvent event) {
        log.info("do doJobgradeChange event.... data={}", FastjsonUtil.toJson(event));
        try {
            BasePage basePage = new BasePage();
            basePage.setPageSize(5000);
            basePage.setPageNo(1);

            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(event.getTenantId());
            userInfo.setUserId(0L);
            userInfo.setEmpId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);

            UserInfo user = new UserInfo();
            user.setTenantId(event.getTenantId());
            user.setStaffId(0L);
            user.setUserid(0);
            UserContext.setCurrentUser(user);

            // 岗位信息 以及 员工信息 中没有存储 job_grade相关名称；
            // 修改岗位信息
            doPostChangeData(event, basePage);

            // 员工信息
            basePage.setPageNo(1);
            doEmpWorkInfoChangeData(event, basePage);
        } catch (Exception e) {
            log.error("do doJobgradeChange event.... err, {}", e.getMessage());
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    private void doEmpWorkInfoChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
            .andGe("dataEndTime", String.valueOf(event.getDataStartTime()))
            .andEq("jobGrade.channel", event.getBid());
        PageResult<EmpWorkInfoDo> pr = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpWorkInfoDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        GrowthRecordDomainService grdService = SpringUtil.getBean(GrowthRecordDomainService.class);
        pr.getItems().forEach(data -> {
            if(null != data.getEmpStatus() && null != data.getEmpStatus().getValue()
                    && EmpStatusEnum.LEAVE_JOB.realValue().equals(data.getEmpStatus().getValue())){
                // 如果员工已离职，则不处理
                return;
            }

            try {
                EmpWorkInfoDo before = new EmpWorkInfoDo();
                BeanUtils.copyProperties(data, before);
                NestPropertyValue properties = new NestPropertyValue();
                BeanUtils.copyProperties(data.getProperties(), properties);
                before.setProperties(properties);
                if(null != data.getJobGrade()){
                    data.getJobGrade().setChannelName(event.getName());
                    JobGradeRange jobGrade = (JobGradeRange) data.getProperties().get("jobGrade");
                    jobGrade.setChannelName(event.getName());
                    data.getProperties().add("jobGrade", jobGrade);
                }
                if(data.getDataStartTime() < event.getDataStartTime()){
                    data.setDataStartTime(event.getDataStartTime());
                    empWorkInfoDomainService.update(data);
                    //empWorkInfoDomainService.doProcessGrowthRecord(before, data);
                    return;
                }
                empWorkInfoDomainService.update(data);
                grdService.updateGrowthRecord(data.getEmpId(), event, FastjsonUtil.toJson(data.getJobGrade()), data.getDataStartTime(), "jobGrade");
            } catch (Exception e){
                log.error("Failed to modify the job level name associated with employee employment information. dataId={}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpWorkInfoChangeData(event, basePage);
    }

    private void doPostChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
            .andEq("jobGrade.channel", event.getBid())
            .andGe("dataEndTime", String.valueOf(event.getDataStartTime()));
        PageResult<PostDo> pr = DataQuery.identifier("entity.hr.Post")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, PostDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        pr.getItems().forEach(data -> {
            if(null != data.getStatus() && StatusEnum.DEACTIVATED.realValue().equals(data.getStatus().getValue())){
                return;
            }
            data.getJobGrade().setChannelName(event.getName());
            if(data.getDataStartTime() < event.getDataStartTime()){
                data.setDataStartTime(event.getDataStartTime());
            }
            try {
                postDo.update(data);
            } catch (Exception e){
                log.error("Failed to modify the job level name associated with the position. dataId={}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }

        basePage.setPageNo(basePage.getPageNo() + 1);
        doPostChangeData(event, basePage);
    }
}
