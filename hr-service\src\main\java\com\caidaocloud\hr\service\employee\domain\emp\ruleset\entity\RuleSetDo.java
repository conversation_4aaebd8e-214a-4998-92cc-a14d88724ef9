package com.caidaocloud.hr.service.employee.domain.emp.ruleset.entity;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.repository.IRuleSetRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.util.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Service
public class RuleSetDo extends DataSimple {

    /**
     * 工号自动生成
     */
    private Boolean autoCreate;
    /**
     * 工号前缀
     */
    private String prefix;
    /**
     * 工号起始编码
     */
    private Long startValue;
    /**
     * 编码长度
     */
    private Integer length;

    /**
     * 是否启用工作流
     */
    private Boolean openWorkFlow;

    /**
     * 使用场景
     */
    private String scene;

    private Boolean autoStart;

    private final static String IDENTIFIER = "entity.hr.RuleSet";

    @Resource
    private IRuleSetRepository ruleSetRepository;

    /**
     * 保存
     *
     * @param data
     * @return
     */
    public RuleSetDo save(RuleSetDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();

        RuleSetDo dbData = getRuleSet();
        if (dbData == null || dbData.getBid() == null) {
            // 新增
            data.setTenantId(tenantId);
            data.setCreateBy(userId);
            data.setCreateTime(System.currentTimeMillis());
            data.setUpdateBy(userId);
            data.setUpdateTime(data.getCreateTime());
            data.setIdentifier(IDENTIFIER);
            data.setDeleted(Boolean.FALSE);
            String bid = ruleSetRepository.insert(data).getBid();
            data.setBid(bid);
            return data;
        } else {
            // 编辑
            data.setUpdateBy(userId);
            data.setUpdateTime(System.currentTimeMillis());
            BeanUtil.copyProperties(data, dbData, "bid", "id", "identifier", "entityId", "tenantId", "createTime", "createBy",
                    "deleted", "dataStartTime", "dataEndTime");
            ruleSetRepository.updateById(dbData);
            return dbData;
        }
    }

    public RuleSetDo getRuleSet() {
        RuleSetDo query = new RuleSetDo();
        query.setTenantId(UserContext.getTenantId());
        query.setIdentifier(IDENTIFIER);
        List<RuleSetDo> list = ruleSetRepository.selectList(query);
        return CollectionUtils.isEmpty(list) ? new RuleSetDo() : list.get(0);
    }
}
