package com.caidaocloud.hr.service.temination.application.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TerminationRuleDto {
    //主动离职
    private TerminationBaseDto voluntaryTermination;
    //被动离职
    private TerminationBaseDto passiveTermination;
    //取消离职
    private TerminationBaseDto cancelTermination;

    public TerminationRuleDto(TerminationBaseDto VoluntaryTermination,TerminationBaseDto passiveTermination){
          this.setVoluntaryTermination(VoluntaryTermination);
          this.setPassiveTermination(passiveTermination);
    }
}
