package com.caidaocloud.hr.service.employee.interfaces.facade.emp.manage;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.EmpResumeConfigDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpResumeService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 员工履历
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/emp/resume/v1")
@Api(value = "/api/hr/emp/resume/v1", description = "员工履历", tags = "v3.4")
public class EmpResumeController {
    @Resource
    private EmpResumeService empResumeService;

    @ApiOperation("获取模板配置")
    @GetMapping("/configTemplate")
    public Result getConfigTemplate() {
        EmpResumeConfigDto config = empResumeService.getResumeConfig();
        return Result.ok(config);
    }

    @ApiOperation("重置配置")
    @PostMapping("/config")
    public Result saveConfig(@RequestBody EmpResumeConfigDto data) {
        PreCheck.preCheckArgument(StringUtil.isBlank(data.getTemplate()), LangUtil.getLangMsg("caidao.resume.error.check_template"));
        empResumeService.save(data);
        return Result.success();
    }

    @ApiOperation("模板配置")
    @PostMapping("/template")
    public Result saveConfigTemplate(@RequestBody EmpResumeConfigDto data) {
        //PreCheck.preCheckArgument(StringUtil.isBlank(data.getTemplate()), LangUtil.getLangMsg("caidao.resume.error.check_template"));
        empResumeService.saveConfigTemplate(data);
        return Result.success();
    }

    @ApiOperation(value = "履历预览", produces = "application/octet-stream")
    @GetMapping("/preview")
    public void preview(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime, HttpServletResponse response){
        empResumeService.preview(empId, dataTime, response);
    }

    @ApiOperation(value = "履历预览", produces = "application/octet-stream")
    @GetMapping("/pdf")
    public void pdf(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime, HttpServletResponse response){
        empResumeService.pdf(empId, dataTime, response);
    }

    @ApiOperation("履历预览")
    @GetMapping("/word")
    public Result wordFile(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime){
        return Result.ok(empResumeService.wordFile(empId, dataTime, null));
    }
}
