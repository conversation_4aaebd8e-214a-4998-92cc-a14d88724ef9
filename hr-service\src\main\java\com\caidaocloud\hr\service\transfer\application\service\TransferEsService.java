package com.caidaocloud.hr.service.transfer.application.service;

import com.caidaocloud.dto.*;
import com.caidaocloud.em.OpEnum;
import com.caidaocloud.hr.service.common.base.DataScopeService;
import com.caidaocloud.hr.service.dto.auth.AuthRoleScopeFilterDetail;
import com.caidaocloud.hr.service.dto.auth.EsOperate;
import com.caidaocloud.hr.service.dto.auth.EsScopeQuery;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.search.infrastructure.repository.EsDataRepository;
import com.caidaocloud.hr.service.transfer.domain.entity.ChangeDefDo;
import com.caidaocloud.hr.service.transfer.domain.entity.ChangeFieldDef;
import com.caidaocloud.hr.service.transfer.domain.enums.ChangeDefEnum;
import com.caidaocloud.hr.service.transfer.infrastructure.utils.TransferChangeFieldDefContext;
import com.caidaocloud.hr.service.transfer.interfaces.dto.TransferQueryDto;
import com.caidaocloud.hr.service.util.DataSimpleUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.AuthRoleScopeRestriction;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.workflow.enums.WfProcessStatusEnum;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.caidaocloud.em.SortOrder.ASC;
import static com.caidaocloud.hr.service.transfer.infrastructure.constant.TransferConstant.*;

@Slf4j
@Service
public class TransferEsService {
    @Value("${caidaocloud.dataScope.transferList:false}")
    private boolean dataFilter;
    @Resource
    private DataScopeService dataScopeService;
    @Resource
    private EsDataRepository esDataRepository;

    /**
     * 同步异动配置定义对应的ES动态数据列
     *
     * @param changeDef 异动配置
     */
    public void syncTransferDef(ChangeDefDo changeDef) {
        String tenantId = UserContext.getTenantId();
        if (esDataRepository.existsIndex(getIndex(tenantId))) {
            return;
        }
        changeDef.setTenantId(tenantId);
        List<ChangeFieldDef> defList = convertChangeDef(changeDef);
        convertEsDef(defList, tenantId);
    }

    public boolean checkIndexExist() {
        String tenantId = UserContext.getTenantId();
        return esDataRepository.existsIndex(getIndex(tenantId));
    }

    public void deleteTransfer(String dataId) {
        UserInfo userInfo = UserContext.preCheckUser();
        esDataRepository.deleteIndexData(getIndex(userInfo.getTenantId()), dataId);
    }

    public void batchDeleteTransfer(List<String> dataIds) {
        UserInfo userInfo = UserContext.preCheckUser();
        esDataRepository.batchDeleIndex(getIndex(userInfo.getTenantId()), dataIds);
    }

    public Map<String, Object> parseTransferMap(DataSimple dataSimple) {
        UserInfo userInfo = UserContext.preCheckUser();
        dataSimple.setTenantId(userInfo.getTenantId());
        dataSimple.setCreateTime(System.currentTimeMillis());
        dataSimple.setUpdateTime(System.currentTimeMillis());
        dataSimple.setUpdateBy(String.valueOf(userInfo.getUserId()));
        dataSimple.setCreateBy(String.valueOf(userInfo.getUserId()));
        dataSimple.setDeleted(false);
        return dataSimple2Map(dataSimple);
    }

    public void saveTransfer(DataSimple dataSimple) {
        UserInfo userInfo = UserContext.preCheckUser();
        esDataRepository.saveData(getIndex(userInfo.getTenantId()), parseTransferMap(dataSimple));
    }

    public void updateTransfer(DataSimple dataSimple) {
        UserInfo userInfo = UserContext.preCheckUser();
        dataSimple.setTenantId(userInfo.getTenantId());
        dataSimple.setUpdateBy(String.valueOf(userInfo.getUserId()));
        dataSimple.setUpdateTime(System.currentTimeMillis());
        Map<String, Object> dataMap = dataSimple2Map(dataSimple);
        esDataRepository.updateData(getIndex(userInfo.getTenantId()), dataMap);
    }

    public void updateTransferPartData(String dataId, Map<String, Object> dataMap, Map<String, String> query) {
        UserInfo userInfo = UserContext.preCheckUser();
        esDataRepository.updatePartDataByQuery(getIndex(userInfo.getTenantId()), dataId, dataMap, query);
    }

    public void updateTransferPartData(String dataId, Map<String, Object> dataMap,
                                       Map<String, String> query, Map<String, String> mustNot) {
        UserInfo userInfo = UserContext.preCheckUser();
        esDataRepository.updatePartDataByQuery(getIndex(userInfo.getTenantId()), dataId, dataMap, query, mustNot);
    }

    public void updateTransferMapData(Map<String, Object> dataMap) {
        UserInfo userInfo = UserContext.preCheckUser();
        esDataRepository.updateData(getIndex(userInfo.getTenantId()), dataMap);
    }

    /**
     * 获取归档文件
     *
     * @param page
     * @return
     */
    public List<DataSimple> getArchiveData(BasePage page) {
        if (checkIndexExist()) {
            log.error("[archive] transfer es index is not exist");
            return Lists.list();
        } else {
            UserInfo userInfo = UserContext.preCheckUser();
            String tenantId = userInfo.getTenantId();
            SearchRequest searchRequest = new SearchRequest();
            searchRequest.indices(getIndex(tenantId));
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.size(page.getPageSize());
            sourceBuilder.from((page.getPageNo() - 1) * page.getPageSize());
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.matchQuery("approvalStatus", "APPROVE"));
            sourceBuilder.query(boolQuery);
            sourceBuilder.sort("updateTime", SortOrder.DESC);
            searchRequest.source(sourceBuilder);
            SearchHits hits = esDataRepository.searchRequest(searchRequest);
            return hits == null ? Lists.list() : convertEsData(hits);
        }
    }

    public BoolQueryBuilder doDataFilter(List<FilterBean> filterItems) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery().must(QueryBuilders.matchAllQuery());
        Map<String, String> transferParams = Maps.map("status", "approvalStatus");
        if (CollectionUtils.isNotEmpty(filterItems)) {
            filterItems.forEach(it -> {
                OpEnum opEnum = it.getOp();
                String property = transferParams.getOrDefault(it.getProp(), it.getProp());
                Object value = it.getValue();
                switch (opEnum) {
                    case eq:
                        builder.must(QueryBuilders.termQuery(property, value));
                        break;
                    case ne:
                        builder.mustNot(QueryBuilders.termQuery(property, value));
                        break;
                    case in:
                        builder.must(QueryBuilders.termsQuery(property, Objects.toString(value).split(",")));
                        break;
                    case bt:
                        try {
                            String[] rangeValues = Objects.toString(value).split(",");
                            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(property);
                            if (rangeValues.length > 0) {
                                rangeQueryBuilder.gte(rangeValues[0]);
                                rangeQueryBuilder.lte(rangeValues[1]);
                            }
                            if (rangeValues.length > 1) {
                                rangeQueryBuilder.lte(rangeValues[1]);
                            }
                            builder.must(rangeQueryBuilder);
                        } catch (Exception e) {
                            log.error("parse bt filter query params error: {}", e.getMessage());
                        }
                        break;
                }
            });
        }
        return builder;
    }



    public PageResult<DataSimple> getPageList(TransferQueryDto basePage) {
        UserInfo userInfo = UserContext.preCheckUser();
        String tenantId = userInfo.getTenantId();
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(getIndex(tenantId));
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.size(basePage.getPageSize());
        sourceBuilder.from((basePage.getPageNo() - 1) * basePage.getPageSize());
        if (StringUtils.isNotEmpty(basePage.getOrder()) && basePage.getSortOrder() != null) {
            boolean isAsc = basePage.getSortOrder() == ASC;
            FieldSortBuilder fsb = SortBuilders.fieldSort(basePage.getOrder())
                    .order(isAsc ? SortOrder.ASC : SortOrder.DESC);
            fsb.missing(isAsc ? "_first" : "_last");
            sourceBuilder.sort(fsb);
        } else {
            sourceBuilder.sort("updateTime", SortOrder.DESC);
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(doDataFilter(basePage.getFilterList()));
        if (StringUtil.isNotEmpty(basePage.getTypeId())) {
            boolQuery.must(QueryBuilders.termsQuery("typeId", basePage.getTypeId()));
        }

        if (basePage.getCreateTime() != null) {
            boolQuery.must(QueryBuilders.termsQuery("createTime", basePage.getCreateTime().toString()));
        }

        if (StringUtil.isNotEmpty(basePage.getKeywords())) {
            BoolQueryBuilder kwBoolQuery = QueryBuilders.boolQuery().
                    should(QueryBuilders.matchQuery("emp_name", basePage.getKeywords())).
                    should(QueryBuilders.matchQuery("emp_workno", basePage.getKeywords())).
                    should(QueryBuilders.wildcardQuery("emp_name", "*" + basePage.getKeywords() + "*")).
                    should(QueryBuilders.wildcardQuery("emp_workno", "*" + basePage.getKeywords() + "*")).minimumShouldMatch(1);
            boolQuery.must(kwBoolQuery);
        }

        if (StringUtil.isNotEmpty(basePage.getEmpId())) {
            boolQuery.must(QueryBuilders.matchQuery("emp_empId", basePage.getEmpId()));
        }

        if (StringUtil.isNotEmpty(basePage.getOrgId())) {
            boolQuery.must(QueryBuilders.termsQuery("organize", basePage.getOrgId()));
        }

        if (StringUtil.isNotEmpty(basePage.getReason())) {
            boolQuery.must(QueryBuilders.termsQuery("other$reason", basePage.getReason()));
        }

        /**
         * 审批状态
         */
        if (StringUtil.isNotEmpty(basePage.getApprovalStatus())) {
            boolQuery.must(QueryBuilders.matchQuery("approvalStatus", basePage.getApprovalStatus()));
        }

        /**
         * 是否显示已撤销
         */
        if (!basePage.isShowRevoke()) {
            boolQuery.mustNot(QueryBuilders.matchQuery("approvalStatus", WfProcessStatusEnum.REVOKE.value));
        }

        if (null != basePage.getEffectiveDate()) {
            boolQuery.must(QueryBuilders.termsQuery("other$effectiveDate", basePage.getEffectiveDate().toString()));
        }

        //todo : caidao-1787 新参数（仅搜索 组织异动 以及 新组织 orgid）
        if (ObjectUtil.isNotEmpty(basePage.getOrganizeEnable())) {
            //组织异动标识
            boolQuery.must(QueryBuilders.termsQuery("work$organize_enable", basePage.getOrganizeEnable()));
        }
        if (ObjectUtil.isNotEmpty(basePage.getNewOrgId())) {
            //新组织信息；
            boolQuery.must(QueryBuilders.termsQuery("work$organize", basePage.getNewOrgId()));
        }

        //todo : 1787 新参数 （未来生效日期）
        if (ObjectUtil.isNotEmpty(basePage.getEffectiveFutureDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("other$effectiveDate").gt(basePage.getEffectiveFutureDate()));
        }
        List<FilterElement> filters = basePage.getFilters();
        if (CollectionUtils.isNotEmpty(filters)) {
            if (filters.get(0).getProp().equals("effectiveDate")) {
                String value = (String) filters.get(0).getValue();
                String[] time = value.split(",");
                boolQuery.must(QueryBuilders.rangeQuery("other$effectiveDate").from(time[0]).to(time[1]));
            }
        }
        //todo : caidao 1787  岗位 /岗位异动标识；

        if (ObjectUtil.isNotEmpty(basePage.getPostEnable())) {
            //岗位异动标识
            boolQuery.must(QueryBuilders.termsQuery("work$post_enable", basePage.getPostEnable()));
        }

        if (StringUtil.isNotEmpty(basePage.getNewPostId())) {
            //新岗位信息；
            boolQuery.must(QueryBuilders.termsQuery("work$post", basePage.getNewPostId()));
        }

        doDataScope(userInfo.getUserId(), boolQuery, basePage.getTypeId());
        sourceBuilder.query(boolQuery);
        searchRequest.source(sourceBuilder);
        SearchHits hits = esDataRepository.searchRequest(searchRequest);
        if (null == hits) {
            return new PageResult(new ArrayList(), basePage.getPageNo(), basePage.getPageSize(), 0);
        }
        List<DataSimple> itemList = convertEsData(hits);
        return new PageResult(itemList, basePage.getPageNo(), basePage.getPageSize(), (int) hits.getTotalHits().value);
    }

    @NotNull
    private List<DataSimple> convertEsData(SearchHits hits) {
        Map<String, List<ChangeFieldDef>> defMap = new HashMap<>();
        List<DataSimple> itemList = new ArrayList<>();
        hits.forEach(hit -> {
            Map<String, Object> dataMap = hit.getSourceAsMap();
            if (null == dataMap || dataMap.isEmpty()) {
                return;
            }
            String defId = (String) dataMap.get("defId");
            List<ChangeFieldDef> defList = defMap.get(defId);
            if (null == defList || defList.isEmpty()) {
                ChangeDefDo changeDef = ChangeDefDo.getDef(defId);
                defList = convertChangeDef(changeDef);
                defMap.put(defId, defList);
            }
            DataSimple dataSimple = map2DataSimple(dataMap, defList);
            if (dataMap.containsKey("createBy")) {
                dataSimple.setCreateBy(Objects.toString(dataMap.get("createBy"), "0"));
            }
            if (dataMap.containsKey("updateBy")) {
                dataSimple.setUpdateBy(Objects.toString(dataMap.get("updateBy"), "0"));
            }
            itemList.add(dataSimple);
        });
        return itemList;
    }

    private void doDataScope(Long userId, BoolQueryBuilder boolQuery, String typeId) {
        if (!dataFilter) {
            return;
        }
        List<AuthRoleScopeFilterDetail> dataList = dataScopeService.getScopeList("ENTITY_HR_EMP_TRANSFER", userId, typeId);
        Map<AuthRoleScopeRestriction, EsScopeQuery> scopeMap = new HashMap();
        scopeMap.put(AuthRoleScopeRestriction.CREATED_BY_MYSELF, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("createBy"));
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_EMP_TYPE, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empType"));
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_COMPANY, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("company"));
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_HRBP, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_WORKPLACE, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("workplace"));
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_EMP_STATUS, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empStatus"));
        scopeMap.put(AuthRoleScopeRestriction.MY_ORG_AND_BELONGINGS, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        scopeMap.put(AuthRoleScopeRestriction.MY_ORG, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_ORG, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        scopeMap.put(AuthRoleScopeRestriction.SELECTED_ORG_AND_BELONGINGS, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize"));
        dataScopeService.getScopeQuery(dataList, boolQuery, scopeMap);
    }

    public DataSimple getTransfer(String dataId, boolean... isPutContext) {
        String tenantId = UserContext.getTenantId();
        Map<String, Object> dataMap = esDataRepository.searchById(getIndex(tenantId), dataId);
        return map2DataSimple(dataMap, isPutContext);
    }

    public Map<String, Object> getTransferMapData(String dataId) {
        String tenantId = UserContext.getTenantId();
        return esDataRepository.searchById(getIndex(tenantId), dataId);
    }

    private List<ChangeFieldDef> getStandPropsDef() {
        List<ChangeFieldDef> standProps = new ArrayList<>();
        standProps.add(new ChangeFieldDef()
                .setDataType(PropertyDataType.Enum)
                .setProperty("workHour")
                .setEnumDef(FastjsonUtil.toList("[{\"display\":\"标准工时制\",\"value\":\"0\"},{\"display\":\"综合工时制\",\"value\":\"1\"},{\"display\":\"不定时工时制\",\"value\":\"2\"}]", PropertyEnumDefDto.class)));
        standProps.add(new ChangeFieldDef()
                .setProperty("empType")
                .setDataType(PropertyDataType.Dict));
        return standProps;
    }

    private DataSimple map2DataSimple(Map<String, Object> dataMap, List<ChangeFieldDef> defList) {
        DataSimple dataSimple = new DataSimple();
        Map<String, ChangeFieldDef> defMap = defList.stream().collect(Collectors.toMap(it -> String.join("$",
                it.getType(), it.getProperty()), obj -> obj, (a, b) -> a));
        Map<String, String> baseMap = DataSimpleUtil.simpleMap;
        Map<String, ChangeFieldDef> standPropertyMap = getStandPropsDef().stream().collect(Collectors.toMap(ChangeFieldDef::getProperty, Function.identity(),
                (o1, o2) -> o1));
        dataMap.forEach((k, pv) -> {
            ChangeFieldDef fieldDef = defMap.get(k);
            // 标准字段
            if (null != fieldDef) {
                addDataSimple(dataSimple, fieldDef, k, pv, dataMap, StringUtil.EMPTY);
                return;
            } else if (k.endsWith(TXT_FIELD_MARK)) {
                dataSimple.getProperties().add(k, Objects.toString(pv, null));
                return;
            } else if (k.endsWith(ENABLE_MARK)) {
                dataSimple.getProperties().add(k, Objects.toString(pv, null));
                return;
            } else {
                // 其他字段，other以及
                String[] props = k.split(UNDERLINE);
                if (props.length > 1) {
                    if (OLD_FIELD_MARK.equals(props[0])) {
                        String property = props[1];
                        fieldDef = defMap.get(property);
                        addDataSimple(dataSimple, fieldDef, property, pv, dataMap, OLD_FIELD_MARK + UNDERLINE);
                        return;
                    }
                    fieldDef = defMap.get(props[0]);
                    addDataSimple(dataSimple, fieldDef, props[0], pv, dataMap, StringUtil.EMPTY);
                    return;
                }
            }

            if (baseMap.containsKey(k)) {
                addBaseData(dataSimple, k, pv);
                return;
            }

            if (standPropertyMap.containsKey(k)) {
                addDataSimple(dataSimple, standPropertyMap.get(k), k, pv, dataMap, StringUtil.EMPTY);
                return;
            }

            dataSimple.getProperties().add(k, Objects.toString(pv, null));

        });
        return dataSimple;
    }


    private DataSimple map2DataSimple(Map<String, Object> dataMap, boolean... isPutContext) {
        if (null == dataMap) {
            return new DataSimple();
        }
        String defId = (String) dataMap.get("defId");
        ChangeDefDo changeDef = ChangeDefDo.getDef(defId);
        return map2DataSimple(dataMap, changeDef, isPutContext);
    }

    public DataSimple map2DataSimple(Map<String, Object> dataMap, ChangeDefDo def, boolean... isPutContext) {
        if (null == dataMap || dataMap.isEmpty()) {
            return new DataSimple();
        }
        if (log.isDebugEnabled()) {
            log.debug("map2DataSimple dataMap = {}", FastjsonUtil.toJson(dataMap));
        }
        List<ChangeFieldDef> defList = convertChangeDef(def);
        if (ArrayUtils.isNotEmpty(isPutContext) && isPutContext[0]){
            TransferChangeFieldDefContext.set(defList);
        }
        DataSimple dataSimple = map2DataSimple(dataMap, defList);
        if (Objects.isNull(dataSimple.getCreateBy())) {
            dataSimple.setCreateBy(Objects.toString(dataMap.get("createBy"), "0"));
        }
        if (Objects.isNull(dataSimple.getUpdateBy())) {
            dataSimple.setUpdateBy(Objects.toString(dataMap.get("updateBy"), "0"));
        }
        return dataSimple;
    }

    @SneakyThrows
    private void addBaseData(DataSimple dataSimple, String fieldName, Object propValue) {
        Field f = dataSimple.getClass().getSuperclass().getDeclaredField(fieldName);
        f.setAccessible(true);
        f.set(dataSimple, ConvertUtils.convert(propValue, f.getType()));
    }

    private void addDataSimple(DataSimple dataSimple, ChangeFieldDef fieldDef, String prop, Object pv,
                               Map<String, Object> dataMap, String prefix) {
        try {
            QueryInfoCache.init();
            prop = prefix + prop;
            fieldDef = prop.equals("emp") ? new ChangeFieldDef()
                    .setProperty("emp")
                    .setDataType(PropertyDataType.Emp) : fieldDef;
            if (fieldDef == null || dataSimple.getProperties().get(prop) != null) {
                return;
            }
            PropertyDataType dataType = fieldDef.getDataType();
            if (null == dataType) {
                dataSimple.getProperties().add(prop, Objects.toString(pv, null));
                return;
            }
            switch (dataType) {
                case Dict:
                    DictSimple dictSimple = DictSimple.doDictSimple(Objects.toString(pv, null));
                    dataSimple.getProperties().put(prop, dictSimple);
                    break;
                case Enum:
                    EnumSimple enumSimple = new EnumSimple();
                    enumSimple.setValue(Objects.toString(pv, null));
                    doEnumText(enumSimple, fieldDef);
                    dataSimple.getProperties().put(prop, enumSimple);
                    break;
                case Emp:
                    EmpSimple empSimple = new EmpSimple();
                    empSimple.setEnName(Objects.toString(dataMap.get(prop + "_enName"), null));
                    empSimple.setWorkno(Objects.toString(dataMap.get(prop + "_workno"), null));
                    empSimple.setName(Objects.toString(dataMap.get(prop + "_name"), null));
                    empSimple.setEmpId(Objects.toString(dataMap.get(prop + "_empId"), null));
                    dataSimple.getProperties().put(prop, empSimple);
                    break;
                case Attachment:
                    final String namePropertyKey = prop + "_names";
                    final String urlPropertyKey = prop + "_urls";
                    Attachment attachment = new Attachment();
                    Object fileNameValue = dataMap.get(namePropertyKey);
                    Object fileUrlValue = dataMap.get(urlPropertyKey);
                    attachment.setNames(fileNameValue != null ? (List<String>) fileNameValue : null);
                    attachment.setUrls(fileUrlValue != null ? (List<String>) fileUrlValue : null);
                    dataSimple.getProperties().put(prop, attachment);
                    break;
                case Job_Grade_Range:
                    JobGradeRange jgr = new JobGradeRange();
                    jgr.setIsRange((Boolean) dataMap.get(prop + "_isRange"));
                    jgr.setChannel(Objects.toString(dataMap.get(prop + "_channel"), null));
                    jgr.setStartGrade(Objects.toString(dataMap.get(prop + "_startGrade"), null));
                    jgr.setEndGrade(Objects.toString(dataMap.get(prop + "_endGrade"), null));
                    jgr.setStartLevel(Objects.toString(dataMap.get(prop + "_startLevel"), null));
                    jgr.setEndLevel(Objects.toString(dataMap.get(prop + "_endLevel"), null));
                    jgr.setChannelName(Objects.toString(dataMap.get(prop + "_channelName"), null));
                    jgr.setStartGradeName(Objects.toString(dataMap.get(prop + "_startGradeName"), null));
                    jgr.setEndGradeName(Objects.toString(dataMap.get(prop + "_endGradeName"), null));
                    dataSimple.getProperties().put(prop, jgr);
                    break;
                case Address:
                    Address address = new Address();
                    String provinceValue = Objects.toString(dataMap.get(prop + "_province"), null);
                    String cityValue = Objects.toString(dataMap.get(prop + "_city"), null);
                    String areaValue = Objects.toString(dataMap.get(prop + "_area"), null);
                    address.setProvince(StringUtil.isNotEmpty(provinceValue) ? Long.valueOf(provinceValue) : null);
                    address.setCity(StringUtil.isNotEmpty(cityValue) ? Long.parseLong(cityValue) : null);
                    address.setArea(StringUtil.isNotEmpty(areaValue) ? Long.parseLong(areaValue) : null);
                    address.setValue(String.join("/", provinceValue, cityValue, areaValue));
                    Address.doAddress(address);
                    dataSimple.getProperties().put(prop, address);
                    break;
                default:
                    dataSimple.getProperties().add(prop, Objects.toString(pv, null));
                    break;
            }
        } finally {
            QueryInfoCache.clear();
        }
    }

    private void doEnumText(EnumSimple enumSimple, ChangeFieldDef fieldDef) {
        String enumValue = enumSimple.getValue();
        if (StringUtil.isEmpty(enumValue)) {
            return;
        }

        List<PropertyEnumDefDto> enumDef = fieldDef.getEnumDef();
        if (null == enumDef) {
            return;
        }

        for (PropertyEnumDefDto ped : enumDef) {
            if (enumValue.equals(ped.getValue())) {
                enumSimple.setText(ped.getDisplay());
                return;
            }
        }
    }

    private Map<String, Object> dataSimple2Map(DataSimple dataSimple) {
        Map<String, Object> dataMap = new HashMap<>();
        String dataId = null == dataSimple.getId() ? SnowUtil.nextId() : dataSimple.getId();
        dataMap.put("id", dataId);
        dataSimple.setId(dataId);
        dataMap.put("tenantId", dataSimple.getTenantId());
        dataMap.put("createTime", dataSimple.getCreateTime());
        dataMap.put("createBy", dataSimple.getCreateBy());
        dataMap.put("updateTime", dataSimple.getUpdateTime());
        dataMap.put("updateBy", dataSimple.getUpdateBy());
        dataMap.put("deleted", dataSimple.isDeleted());
        // dataMap.put("dataStartTime", dataSimple.getDataStartTime());
        // dataMap.put("dataEndTime", dataSimple.getDataEndTime());
        dataSimple.getProperties().forEach((k, pv) -> {
            //log.info("dataSimple k = {}, pv = {}", k, FastjsonUtil.toJson(pv));
            if (pv instanceof EnumSimple) {
                dataMap.put(k, ((EnumSimple) pv).getValue());
            } else if (pv instanceof DictSimple) {
                dataMap.put(k, ((DictSimple) pv).getValue());
            } else if (pv instanceof EmpSimple) {
                dataMap.put(k + "_empId", ((EmpSimple) pv).getEmpId());
                dataMap.put(k + "_workno", ((EmpSimple) pv).getWorkno());
                dataMap.put(k + "_name", ((EmpSimple) pv).getName());
                dataMap.put(k + "_enName", ((EmpSimple) pv).getEnName());
            } else if (pv instanceof Attachment) {
                dataMap.put(k + "_names", ((Attachment) pv).getNames());
                dataMap.put(k + "_urls", ((Attachment) pv).getUrls());
            } else if (pv instanceof SimplePropertyValue) {
                dataMap.put(k, ((SimplePropertyValue) pv).getValue());
            } else if (pv instanceof JobGradeRange) {
                dataMap.put(k + "_isRange", ((JobGradeRange) pv).getIsRange());
                dataMap.put(k + "_channel", ((JobGradeRange) pv).getChannel());
                dataMap.put(k + "_startGrade", ((JobGradeRange) pv).getStartGrade());
                dataMap.put(k + "_endGrade", ((JobGradeRange) pv).getEndGrade());
                dataMap.put(k + "_startLevel", ((JobGradeRange) pv).getStartLevel());
                dataMap.put(k + "_endLevel", ((JobGradeRange) pv).getEndLevel());
                dataMap.put(k + "_channelName", ((JobGradeRange) pv).getChannelName());
                dataMap.put(k + "_startGradeName", ((JobGradeRange) pv).getStartGradeName());
                dataMap.put(k + "_endGradeName", ((JobGradeRange) pv).getEndGradeName());
            } else if (pv instanceof Address) {
                dataMap.put(k + "_province", ((Address) pv).getProvince());
                dataMap.put(k + "_city", ((Address) pv).getCity());
                dataMap.put(k + "_area", ((Address) pv).getArea());
            }
        });
        return dataMap;
    }

    private String getIndex(String tenantId) {
        String index = String.format("transfer_%s", tenantId);
        return index;
    }

    private void convertEsDef(List<ChangeFieldDef> defList, String tenantId) {
        final Map<String, Object> defMap = new HashMap<>();
        String keyword = "keyword", integerType = "integer", longType = "long";
        defList.forEach(def -> {
            String prop = String.join("$", def.getType(), def.getProperty());
            def.setDataType(null == def.getDataType() ? PropertyDataType.String : def.getDataType());
            // 增加字段展示
            defMap.put(prop + TXT_FIELD_MARK, typeMapping(keyword));
            defMap.put(OLD_MARK + prop + TXT_FIELD_MARK, typeMapping(keyword));
            defMap.put(prop + ENABLE_MARK, typeMapping(keyword));
            switch (def.getDataType()) {
                case Dict:
                    defMap.put(OLD_MARK + prop, typeMapping(keyword));
                    defMap.put(prop, typeMapping(keyword));
                    break;
                case Integer:
                    addDefMap(defMap, integerType, prop);
                    break;
                case Timestamp:
                    addDefMap(defMap, longType, prop);
                    break;
                case Attachment:
                    defMap.put(OLD_MARK + prop + "_names", typeMapping(keyword));
                    defMap.put(prop + "_names", typeMapping(keyword));
                    defMap.put(OLD_MARK + prop + "_urls", typeMapping(keyword));
                    defMap.put(prop + "_urls", typeMapping(keyword));
                    break;
                case Emp:
                    defMap.put(OLD_MARK + prop + "_empId", typeMapping(keyword));
                    defMap.put(prop + "_empId", typeMapping(keyword));
                    defMap.put(OLD_MARK + prop + "_workno", typeMapping(keyword));
                    defMap.put(prop + "_workno", typeMapping(keyword));
                    defMap.put(OLD_MARK + prop + "_name", typeMapping(keyword));
                    defMap.put(prop + "_name", typeMapping(keyword));
                    defMap.put(OLD_MARK + prop + "_enName", typeMapping(keyword));
                    defMap.put(prop + "_enName", typeMapping(keyword));
                    break;
                default:
                    addDefMap(defMap, "keyword", prop);
                    break;
            }
        });
        defMap.put("tenantId", typeMapping(keyword));
        defMap.put("typeId", typeMapping(longType));
        defMap.put("defId", typeMapping(longType));
        defMap.put("createTime", typeMapping(longType));
        defMap.put("updateTime", typeMapping(longType));
        // 员工信息
        defMap.put("emp", typeMapping(keyword));
        // 挂载的表单id
        defMap.put("formId", typeMapping(longType));
        // 挂载的表单对应的数据id
        defMap.put("formDataId", typeMapping(longType));
        // 发起电子签署
        defMap.put("esgin", typeMapping(keyword));
        defMap.put("remark", typeMapping(keyword));
        String mappingJson = esDataRepository.addPropsDynamicTemplate(defMap);
        log.info("update index mappingJson={}", mappingJson);
        esDataRepository.updateDynamicIndex(mappingJson, getIndex(tenantId));
    }

    private void addDefMap(Map<String, Object> defMap, String type, String prop) {
        defMap.put(OLD_MARK + prop, typeMapping(type));
        defMap.put(prop, typeMapping(type));
    }

    private Map typeMapping(String type) {
        Map keyword = new HashMap();
        keyword.put("type", type);
        return keyword;
    }

    private List<ChangeFieldDef> convertChangeDef(ChangeDefDo changeDef) {
        if (null == changeDef) {
            return Lists.list();
        }
        List<ChangeFieldDef> propList = FastjsonUtil.toList(changeDef.getWorkProps(), ChangeFieldDef.class);
        propList.addAll(FastjsonUtil.toList(changeDef.getContractProps(), ChangeFieldDef.class));
        propList.addAll(FastjsonUtil.toList(changeDef.getSalaryProps(), ChangeFieldDef.class));
        propList.addAll(FastjsonUtil.toList(changeDef.getOtherProps(), ChangeFieldDef.class));
        propList.add(getApprovalStatus());
        propList.addAll(getLeaderEmpDef());
        return propList;
    }

    private ChangeFieldDef getApprovalStatus() {
        List<PropertyEnumDefDto> enumDef = new ArrayList<>();
        WfProcessStatusEnum[] values = WfProcessStatusEnum.values();
        for (int i = 0; i < values.length; i++) {
            PropertyEnumDefDto ped = new PropertyEnumDefDto();
            ped.setDisplay(values[i].name);
            ped.setValue(values[i].value);
            enumDef.add(ped);
        }

        ChangeFieldDef approvalStatus = new ChangeFieldDef().setName("审批状态")
                .setProperty("approvalStatus")
                .setType(ChangeDefEnum.OTHER.display())
                .setRequired(true)
                .setWidgetType("EnumSelect")
                .setDataType(PropertyDataType.Enum)
                .setSystem(false)
                .setEnumDef(enumDef);
        return approvalStatus;
    }

    private List<ChangeFieldDef> getLeaderEmpDef() {
        List<ChangeFieldDef> defList = new ArrayList<>();
        ChangeFieldDef leaderOrganize = new ChangeFieldDef().setName("直接上级组织")
                .setProperty("leaderOrganize")
                .setType(ChangeDefEnum.WORK.display())
                .setRequired(true)
                .setWidgetType("ORG")
                .setDataType(PropertyDataType.String)
                .setSystem(false);
        defList.add(leaderOrganize);
        ChangeFieldDef leaderPost = new ChangeFieldDef().setName("直接上级岗位")
                .setProperty("leaderPost")
                .setType(ChangeDefEnum.WORK.display())
                .setRequired(true)
                .setWidgetType("POST")
                .setDataType(PropertyDataType.String)
                .setSystem(false);
        defList.add(leaderPost);
        return defList;
    }

    public void deleteTransferIndex(String indexName) {
        esDataRepository.deleteIndex(indexName);
    }

}
