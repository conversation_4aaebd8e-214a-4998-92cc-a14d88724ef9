package com.caidaocloud.hr.service.employee.domain.emp.manage.entity;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpCertificateRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.NO_DATA_EXIST;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
@Service
public class EmpCertificateDo extends DataSimple {
    /**
     * 员工ID
     */
    private String empId;

    /**
     * 技能名称
     */
    private String skill;

    /**
     * 证书名称
     */
    private String certificate;

    /**
     * 熟练程度
     */
    private DictSimple proficiency;

    /**
     * 证书级别
     */
    private String certificateLevel;

    /**
     * 获得日期
     */
    private Long acquireDate;

    /**
     * 备注
     */
    private String remark;

    private final static String IDENTIFIER = "entity.hr.EmpCertificate";

    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    @Resource
    private IEmpCertificateRepository empCertificateRepository;

    public List<EmpCertificateDo> selectListByEmpId(String empId) {
        return empCertificateRepository.selectListByEmpId(empId, IDENTIFIER, UserContext.getTenantId());
    }

    public EmpCertificateDo selectById(String bid) {
        return empCertificateRepository.selectById(bid, IDENTIFIER);
    }

    public String save(EmpCertificateDo data) {
        DataEntity.initDataSimpleBaseFieldValue(IDENTIFIER, data, null);
        empCertificateRepository.insert(data);
        return data.getBid();
    }

    public void update(EmpCertificateDo data) {
        EmpCertificateDo source = selectById(data.getBid());
        PreCheck.preCheckArgument(source == null || source.isDeleted(), LangUtil.getMsg(NO_DATA_EXIST));
        DataEntity.initDataSimpleBaseFieldValue(IDENTIFIER, data, source);
        empCertificateRepository.updateById(data);
    }

    public void delete(String bid) {
        empCertificateRepository.delete(bid, IDENTIFIER);
    }
}
