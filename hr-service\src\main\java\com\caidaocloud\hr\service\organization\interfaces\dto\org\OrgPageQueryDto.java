package com.caidaocloud.hr.service.organization.interfaces.dto.org;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/12/1
 */
@Data
public class OrgPageQueryDto extends BasePage implements Serializable {
    private static final long serialVersionUID = 1836244076587031661L;
    @ApiModelProperty("日期时间（单位毫秒）")
    private Long dateTime;
    @ApiModelProperty("状态（0 已启用；1 已停用；null 查询全部）")
    private Integer status;
    @ApiModelProperty("架构类型")
    private String schemaType;
    @ApiModelProperty("部门编码")
    private List<String> orgCodeList;

    @ApiModelProperty("组织简称/编码")
    private String nameOrCode;

//    @ApiModelProperty("组织类型")
//    private DictSimple orgType;
//
//    @ApiModelProperty("负责人")
//    private EmpSimple leaderEmp;
//
//    @ApiModelProperty("HRBP")
//    private EmpSimple hrbpEmp;

}
