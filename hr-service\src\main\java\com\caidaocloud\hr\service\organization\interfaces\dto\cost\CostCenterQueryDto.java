package com.caidaocloud.hr.service.organization.interfaces.dto.cost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("成本中心查询参数")
public class CostCenterQueryDto {
    @ApiModelProperty("成本中心名称/编码（模糊匹配）")
    private String costCenterNameOrCode;
    @ApiModelProperty("日期时间（单位毫秒）")
    private Long dateTime;
    @ApiModelProperty("状态（0 已启用 1 已停用）")
    private Integer status;
}
