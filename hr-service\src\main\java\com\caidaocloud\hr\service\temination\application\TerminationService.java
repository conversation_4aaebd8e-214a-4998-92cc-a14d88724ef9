package com.caidaocloud.hr.service.temination.application;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.hr.service.contract.application.enums.SignProcessStatusEnum;
import com.caidaocloud.hr.service.contract.interfaces.vo.TerminationExportVo;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpPrivateInfoService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.base.enums.ResignationStatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpEduInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpOtherContractDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpEduInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpOtherContractDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.hr.service.enums.archive.ArchivePolicy;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.organization.application.tenant.feign.ITenantFeignClient;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.organization.domain.workplace.service.WorkplaceDomainService;
import com.caidaocloud.hr.service.tag.application.tag.service.EmpTagInfoService;
import com.caidaocloud.hr.service.tag.interfaces.vo.EmpTagInfoVo;
import com.caidaocloud.hr.service.tag.interfaces.vo.TagInfoKVVo;
import com.caidaocloud.hr.service.temination.application.dto.*;
import com.caidaocloud.hr.service.temination.application.dto.esign.ContractSignDto;
import com.caidaocloud.hr.service.temination.application.enums.BackTypeEnum;
import com.caidaocloud.hr.service.temination.application.enums.WfTaskActionEnum;
import com.caidaocloud.hr.service.common.application.feign.EsignFeignClient;
import com.caidaocloud.hr.service.temination.application.feign.FormFeignClient;
import com.caidaocloud.hr.service.temination.application.feign.WfOperateFeignClient;
import com.caidaocloud.hr.service.temination.domain.TerminationListVo;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationApplierInfo;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationApply;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationConfig;
import com.caidaocloud.hr.service.temination.domain.enums.*;
import com.caidaocloud.hr.service.temination.interfaces.dto.TerminationApplyQueryDto;
import com.caidaocloud.hr.service.temination.interfaces.dto.TerminationQueryDto;
import com.caidaocloud.hr.service.temination.interfaces.vo.TerminationCancelVo;
import com.caidaocloud.hr.service.temination.interfaces.vo.TerminationTemplateDetailVo;
import com.caidaocloud.hr.service.util.ExtFieldUtil;
import com.caidaocloud.hr.service.util.IdCardUtil;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hr.service.vo.organization.company.org.OrgVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.paas.common.dto.DynamicPageDto;
import com.caidaocloud.hrpaas.paas.common.feign.DynamicFeignClient;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfApproverDto;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfProcessRuDto;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TerminationService {

    @Resource
    private IWfRegisterFeign iWfRegisterFeign;

    @Resource
    private ISessionService sessionService;

    @Resource
    private FormService formService;

    @Resource
    private WfOperateFeignClient wfOperateFeignClient;

    @Resource
    private TerminationBlacklistService blacklistService;

    @Resource
    private TerminationChangeService terminationChangeService;

    @Resource
    private EmpWorkInfoService empWorkInfoService;

    @Resource
    private WorkplaceDomainService workplaceDomainService;

    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private EsignFeignClient esignFeignClient;
    @Resource
    private TerminationConfigService terminationConfigService;
    @Resource
    private FormFeignClient formFeignClient;
    @Resource
    private EmpPrivateInfoService empPrivateInfoService;
    @Resource
    private EmpEduInfoDomainService empEduInfoDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private EmpOtherContractDomainService empOtherContractDomainService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private ITenantFeignClient tenantFeignClient;
    @Resource
    private TerminationWorkflowService terminationWorkflowService;
    @Resource
    private ArchiveEventProducer archiveEventProducer;

    @Resource
    private DynamicFeignClient dynamicFeignClient;
    @Resource
    private EmpTagInfoService empTagInfoService;

    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    // esign离职业务流程
    private final String BUSINESS_PROCESS = "2";

    public String apply(TerminationApplyDto dto) {
        val empId = String.valueOf(sessionService.getUserInfo().getStaffId());
        EmpSimple emp = new EmpSimple();
        emp.setEmpId(empId);
        EmpSimple.doEmpSimple(emp);
        dto.setEmp(emp);
        val apply = new TerminationApply();
        BeanUtils.copyProperties(dto, apply, "applyType", "terminationReason", "empTerminationReason", "blacklistReason");
        apply.setApplyType(TerminationApplyType.BY_EMPLOYEE);
        val applyId = SpringUtil.getBean(TerminationService.class).commonApply(apply, dto.getTerminationReason(), dto.getEmpTerminationReason(), dto.getBlacklistReason(), dto.getFormData(), TerminationApplyType.BY_EMPLOYEE);
        if (apply.isWorkflowEnabled()) {
            beginWorkflow(applyId, apply);
        }
        return applyId;
    }

    public String agentApply(TerminationApplyDto dto) {
        //前端主动被动都传 terminationReason
//        if (TerminationTypeEnum.VOLUNTARY.equals(dto.getType())) {
//            dto.setEmpTerminationReason(dto.getTerminationReason());
//            dto.setTerminationReason(null);
//        }
        val apply = new TerminationApply();
        BeanUtils.copyProperties(dto, apply, "terminationReason", "empTerminationReason", "blacklistReason");
        apply.setApplyType(TerminationApplyType.BY_AGENT);
        TerminationApply previousApply = null;
        if (StringUtils.isNotEmpty(dto.getPreviousApplyId())) {
            previousApply = one(dto.getPreviousApplyId());
            val illegal = !previousApply.getStatus().equals(TerminationStatus.REVOKE) &&
                    !previousApply.getStatus().equals(TerminationStatus.CANCELLED) || previousApply.isRestarted();
            if (illegal) {
                throw new ServerException("单据不可重新发起");
            }
            previousApply.setRestarted(true);
            //previousApply.update();
        }
        val applyId = SpringUtil.getBean(TerminationService.class).commonApply(apply, dto.getTerminationReason(), dto.getEmpTerminationReason(), dto.getBlacklistReason(), dto.getFormData(), TerminationApplyType.BY_AGENT);
        if (apply.isWorkflowEnabled()) {
            beginWorkflow(applyId, apply);
        }
        if (StringUtils.isNotEmpty(dto.getPreviousApplyId())) {
            previousApply.update();
        }
        return applyId;
    }

    @PaasTransactional
    public String commonApply(TerminationApply apply, String terminationReason, String empTerminationReason, String blacklistReason, Map<String, Object> formData, TerminationApplyType type) {
        val workInfo = checkValid(apply.getEmp());
        saveFormData(apply, formData);
        val configId = TerminationConfig.loadEnabled(type).get().getBid();
        apply.setConfigId(configId);
        val applier = TerminationApplierInfo.fromWorkInfoDo(workInfo);
        apply.setApplierInfo(applier);
        val orgId = applier.getOrgId();
        apply.setOrgId(orgId);
        apply.setEmpType(workInfo.getEmpType());
        if (StringUtils.isNotEmpty(blacklistReason)) {
            apply.setBlacklistReason(DictSimple.doDictSimple(blacklistReason));
        }
        if (StringUtils.isNotEmpty(terminationReason)) {
            apply.setTerminationReason(DictSimple.doDictSimple(terminationReason));
        }
        if (StringUtils.isNotEmpty(empTerminationReason)) {
            apply.setEmpTerminationReason(DictSimple.doDictSimple(empTerminationReason));
        }
        if (!apply.isWorkflowEnabled()) {
            apply.setStatus(TerminationStatus.APPROVE);
        } else {
            apply.setStatus(TerminationStatus.IN_PROCESS);
        }
        //var workInfo = empWorkInfoService.getEmpWorkInfo(apply.getEmp().getEmpId(), System.currentTimeMillis());
        if (workInfo != null) {
            apply.setWorkplace(workInfo.getWorkplace());
            apply.setCompany(workInfo.getCompany());
        }
        if (null == apply.getSignProcessStatus() || StringUtils.isBlank(apply.getSignProcessStatus().getValue())) {
            apply.setSignProcessStatus(SignProcessStatusEnum.initiate.convertToEnumSimple());
        }
        var applyId = apply.create();
        updateWorkInfo(workInfo, apply.getTerminationTime(), ResignationStatusEnum.PENDING);
        if (apply.isWorkflowEnabled()) {
            //beginWorkflow(applyId, apply);
        } else {
            terminationChangeService.approved(apply);
        }
        return applyId;
    }

    /**
     * 更新预计离职日期和离职状态
     *
     * @param empWorkInfo 员工任职信息
     */
    public void updateWorkInfo(EmpWorkInfoDo empWorkInfo, Long terminationTime, ResignationStatusEnum resignationStatus) {
        empWorkInfo.setExpectedResignDate(terminationTime);

        // 离职状态
        EnumSimple resignationStauts = new EnumSimple();
        resignationStauts.setValue(resignationStatus == null ? null : resignationStatus.name());
        empWorkInfo.setResignationStatus(resignationStauts);
        empWorkInfoDomainService.update(empWorkInfo);
    }

    /**
     * 清空员工预计离职日期和离职状态
     */
    public void resetWorkInfo(String empId) {
        EmpWorkInfoDo empWorkInfo = empWorkInfoService.getEmpWorkInfo(empId, System.currentTimeMillis());
        updateWorkInfo(empWorkInfo, null, null);
    }

    private EmpWorkInfoDo checkValid(EmpSimple emp) {
        val applyList = TerminationApply.listByEmp(emp.getEmpId());
        applyList.forEach(it -> {
            if (it.getStatus().equals(TerminationStatus.IN_PROCESS)) {
                throw new ServerException("不可重复发起离职");
            }
            if (it.getStatus().equals(TerminationStatus.APPROVE)
                    && it.getTerminationTime() > System.currentTimeMillis()) {
                throw new ServerException("不可重复发起离职");
            }
        });
        val workInfo = empWorkInfoService.getEmpWorkInfo(emp.getEmpId(), System.currentTimeMillis());
        if (workInfo != null && null != workInfo.getEmpStatus() && "1".equals(workInfo.getEmpStatus().getValue())) {
            throw new ServerException("不可重复发起离职");
        }
        return workInfo;
    }

    private void saveFormData(TerminationApply apply, Map<String, Object> formData) {
        // 表单数据保存
        if (StringUtils.isNotEmpty(apply.getFormDefId())) {
            FormDataDto dataDto = new FormDataDto();
            dataDto.setPropertiesMap(formData);
            String formDataId = formService.saveFormData(apply.getFormDefId(), dataDto);
            if (StringUtils.isEmpty(formDataId)) {
                throw new ServerException("Form data saving failed");
            }
            apply.setFormDataId(formDataId);
        }
    }

    private void updateFormData(TerminationApply apply, Map<String, Object> formData, List<String> writableFields) {
        // 表单数据保存
        if (StringUtils.isNotEmpty(apply.getFormDefId()) && StringUtils.isNotEmpty(apply.getFormDataId())) {
            //val def = formService.getFormDefById(apply.getFormDefId());
            val oldFormData = formService.getFormDataMap(apply.getFormDefId(), apply.getFormDataId());
            for (String writableField : writableFields) {
                oldFormData.put(writableField, formData.get(writableField));
            }
            FormDataDto dataDto = new FormDataDto();
            dataDto.setId(apply.getFormDataId());
            dataDto.setPropertiesMap(oldFormData);
            formService.updateFormData(apply.getFormDefId(), dataDto);
        }
    }

    private void beginWorkflow(String applyId, TerminationApply apply) {
        val config = TerminationConfig.loadEnabled(apply.getApplyType());
        WfBeginWorkflowDto beginDto = new WfBeginWorkflowDto();
        beginDto.setFuncCode(TerminationConfigService.WORKFLOW_CODE_PREFIX +
                config.getOrThrow(new ServerException("离职配置未启用")).getBid());
        beginDto.setBusinessId(applyId);
        beginDto.setApplicantId(apply.getEmp().getEmpId());
        beginDto.setApplicantName(apply.getEmp().getName());
        // 业务单据事件时间为离职日期
        beginDto.setEventTime(apply.getTerminationTime());
        log.info("离职流程信息：" + FastjsonUtil.toJson(beginDto));
        Result wfResult = null;
        try {
            wfResult = iWfRegisterFeign.begin(beginDto);
        } catch (Exception e) {
            log.error("流程发起失败", e);
        }
        if (wfResult == null || !wfResult.isSuccess() || StringUtils.isBlank((String) wfResult.getData())) {
            //审批流程发起失败
            apply.delete();
            if (wfResult == null) {
                throw new ServerException("离职流程发起异常");
            } else {
                throw new ServerException(wfResult.getMsg());
            }

        }
    }

    @PaasTransactional
    public void approve(TerminationApplyApprovalDto dto) {
        dto.setBid(StringUtils.substringBefore(dto.getBusinessKey(), "_"));
        TerminationApply apply = TerminationApply.load(dto.getBid());
        dto.getWritableFields().forEach(writableField -> {
            if (TerminationProperty.TERMINATION_TYPE.getCode().equals(writableField)) {
                apply.setType(dto.getType());
            } else if (TerminationProperty.TERMINATION_TIME.getCode().equals(writableField)) {
                apply.setTerminationTime(dto.getTerminationTime());
            } else if (TerminationProperty.TERMINATION_REASON.getCode().equals(writableField)) {
                apply.setTerminationReason(DictSimple.doDictSimple(dto.getTerminationReason()));
            } else if (TerminationProperty.EMP_TERMINATION_REASON.getCode().equals(writableField)) {
                apply.setEmpTerminationReason(DictSimple.doDictSimple(dto.getEmpTerminationReason()));
            } else if (TerminationProperty.INTO_BLACKLIST.getCode().equals(writableField)) {
                apply.setIntoBlacklist(dto.isIntoBlacklist());
                apply.setBlacklistReason(DictSimple.doDictSimple(dto.getBlacklistReason()));
            } else if (TerminationProperty.ATTACHMENT.getCode().equals(writableField)) {
                apply.setAttachment(dto.getAttachment());
            }
        });
        apply.update();
        updateFormData(apply, dto.getFormData(), dto.getWritableFields());
    }

    public void approveWorkflow(TerminationApplyApprovalDto dto, WfTaskActionEnum choice) {
        // 工作流审批拒绝
        WfTaskApproveDTO wfApproveTaskDto = new WfTaskApproveDTO();
        wfApproveTaskDto.setTaskId(dto.getTaskId());
        wfApproveTaskDto.setChoice(choice);
        wfApproveTaskDto.setComment(dto.getComment());
        try {
            // 调用 feign 更新业务单据事件时间
            TerminationApply apply = TerminationApply.load(StringUtils.substringBefore(dto.getBusinessKey(), "_"));
            WfProcessRuDto wfProcessRuDto = new WfProcessRuDto();
            wfProcessRuDto.setBusinessKey(dto.getBusinessKey());
            wfProcessRuDto.setEventTime(apply.getTerminationTime());
            try {
                iWfRegisterFeign.updateEventTime(wfProcessRuDto);
            } catch (Exception e) {
                log.error("Termination updateEventTime err,{}", e.getMessage(), e);
            }
            Result<?> result = wfOperateFeignClient.approveTask(wfApproveTaskDto);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_40131"));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
    }

    public void back(TerminationApplyApprovalDto dto) {
        // 工作流退回
        WfTaskBackDTO wfTaskBackDTO = new WfTaskBackDTO();
        wfTaskBackDTO.setTaskId(dto.getTaskId());
        wfTaskBackDTO.setBackType(BackTypeEnum.START_NODE);
        try {
            Result result = wfOperateFeignClient.backTask(wfTaskBackDTO);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_40131"));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
    }

    public String getSequenceData(String applyId, String seqCode) {
        val apply = TerminationApply.load(applyId);
        if (seqCode.contains("_standard_")) {
            if (seqCode.split("_standard_")[1].equals(TerminationProperty.INTO_BLACKLIST.getCode() + "_REASON")) {
                return TerminationProperty.INTO_BLACKLIST.getValue(apply, true, false);
            }
            return TerminationProperty.valueOf(seqCode.split("_standard_")[1])
                    .getValue(apply, true, false);
        } else {
            //val prefix = TerminationConfigService.WORKFLOW_CODE_PREFIX + apply.getConfigId() + "_";
            val property = StringUtils.substringAfter(StringUtils.substringAfter(seqCode, "_"), "_");
            //seqCode.split(prefix)[1];
            log.info("TerminationService getSequenceData property={}", property);
            if (property.contains("EmpOtherContract")) {
                // 获取离职员工信息
                EmpSimple empSimple = apply.getEmp();
                if (null == empSimple || StringUtil.isEmpty(empSimple.getEmpId())) {
                    log.info("### empId of the transferred employee does not exist ###");
                    return "";
                }
                return getEntityDataSimpleValue(empSimple.getEmpId(), property);
            }
            val valueObj = formService.getFormDataMap(apply.getFormDefId(), apply.getFormDataId()).get(property);
            return String.valueOf(valueObj);
        }
    }

    /**
     * 查看审批人
     *
     * @param businessKey
     * @param approverCode
     * @return
     */
    public String getApproverData(String businessKey, String approverCode) {
        if (StringUtils.isBlank(businessKey) || StringUtils.isBlank(approverCode)) {
            return "";
        }
        var applyId = businessKey.split("_")[0];
        var terminationApply = TerminationApply.load(applyId);
        if (terminationApply == null) {
            return "";
        }
        var split = approverCode.split("-");
        var valueObj = formService.getFormDataMap(terminationApply.getFormDefId(), terminationApply.getFormDataId()).get(split[2]);
        if (valueObj == null) {
            return "";
        }
        var empSimple = FastjsonUtil.convertObject(valueObj, EmpSimple.class);
        return FastjsonUtil.toJson(Lists.list(new WfApproverDto(empSimple.getEmpId(), empSimple.getName(), "")));
    }

    /***
     * 员工信息-其他合同信息下-是否签署竞业协议字段
     * @param empId
     * @param property
     * @return
     */
    private String getEntityDataSimpleValue(String empId, String property) {
        String[] split = property.split("_");
        PageResult<Map<String, String>> result = DataQuery.identifier("entity.hr.EmpOtherContract").filterProperties(DataFilter.eq("empId", empId),
                Lists.list("signAgreement"), System.currentTimeMillis());
        if (result != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(result.getItems())) {
            Map<String, String> stringMap = result.getItems().get(0);
            return stringMap.get("signAgreement");
        }
        return "false";
    }

    public PageResult<TerminationApply> page(TerminationQueryDto queryDto, String keywords, TerminationStatus status,
                                             String org, TerminationType terminationType) {
        return TerminationApply.page(queryDto, keywords, status, org, terminationType);
    }

    public PageResult<TerminationApply> pageWorkplace(TerminationQueryDto queryDto, String keywords, TerminationStatus status,
                                                      String org, TerminationType terminationType) {
        PageResult<TerminationApply> page = TerminationApply.page(queryDto, keywords, status, org, terminationType);
        List<TerminationApply> items = page.getItems();
        List<String> workplaceIds = items.stream().filter(terminationApply -> terminationApply.getWorkplace() != null).map(TerminationApply::getWorkplace).collect(Collectors.toList());

        Map<String, String> workplaceBidToName = new HashMap<>();
        if (!CollectionUtils.isEmpty(workplaceIds)) {
            List<WorkplaceDo> workplaceDos = workplaceDomainService.selectByIds(workplaceIds, UserContext.getTenantId());
            workplaceBidToName = workplaceDos.stream().collect(Collectors.toMap(WorkplaceDo::getBid, WorkplaceDo::getName, (d1, d2) -> d1));
        }
        for (TerminationApply item : items) {
            //CAIDAOM-2513 处理历史数据的工作地信息；
            if (StringUtil.isNotEmpty(item.getWorkplace()) && StringUtil.isEmpty(item.getApplierInfo().getWorkplaceId())) {
                //申请信息中没有工作地 但是 员工信息有工作地
                item.getApplierInfo().setWorkplaceId(item.getWorkplace());
                if (workplaceBidToName.containsKey(item.getWorkplace())) {
                    item.getApplierInfo().setWorkplaceName(workplaceBidToName.get(item.getWorkplace()));
                }
            }
        }
        page.setItems(items);
        return page;
    }

    public DynamicPageDto dynamicPage(TerminationQueryDto queryDto, String keywords, TerminationStatus status,
                                      String org, TerminationType terminationType) {
        DynamicPageDto result = new DynamicPageDto();
        //PageResult<TerminationApply> page = TerminationApply.page(queryDto, keywords, status, org, terminationType);
        val page = terminationAblePage(queryDto, keywords, status, org, terminationType);
        val userDynamicSet = dynamicFeignClient.userDynamicTableLoad("TERMINATION").getData();
        val dynamicSet = dynamicFeignClient.dynamicTableLoad("TERMINATION").getData();
        if (!page.getItems().isEmpty()) {
            val formDefIds = page.getItems().stream()
                    .map(it -> it.getFormDefId()).filter(it -> StringUtils.isNotEmpty(it)).distinct()
                    .collect(Collectors.toList());
            List<MetadataPropertyDto> properties = dynamicSet;
            Map<String, List<MetadataPropertyDto>> mappedProperties = Maps.map();
            for (MetadataPropertyDto property : properties) {
                val split = property.getProperty().split("@");
                val identifier = split[1];
                property.setProperty(split[0]);
                if (mappedProperties.containsKey(identifier)) {
                    mappedProperties.get(identifier).add(property);
                } else {
                    mappedProperties.put(identifier, Lists.list(property));
                }
            }
            val dataList = page.getItems().stream().map(terminationApply -> {
                terminationApply.getBid();
                Map data = Maps.map();
                val empId = terminationApply.getEmp().getEmpId();
                data.put("emp", terminationApply.getEmp());
                data.put("bid", terminationApply.getBid());
                data.put("formDataId", terminationApply.getFormDataId());
                data.put("formDefId", terminationApply.getFormDefId());
                data.put("businessKey", terminationApply.getBusinessKey());
                data.put("status", terminationApply.getStatus());
                data.put("cancelTerminateAble", terminationApply.isCancelTerminateAble());
                data.put("signProcessStatus", terminationApply.getSignProcessStatus());
                data.put("restarted", terminationApply.isRestarted());
                data.put("applyType", terminationApply.getApplyType());
                long time = terminationApply.getCreateTime();
                if (time <= 0) {
                    time = System.currentTimeMillis();
                }
                addDynamicColumnValue(data, "entity.hr.EmpPrivateInfo",
                        mappedProperties, time, empId, "empId");
                addDynamicColumnValue(data, "entity.hr.EmpWorkInfo",
                        mappedProperties, time, empId, "empId");
                addDynamicColumnValue(data, "entity.hr.EmpWorkOverview",
                        mappedProperties, time, empId, "empId");
                addDynamicColumnValue(data, "entity.hr.LastContract",
                        mappedProperties, time, empId, "owner$empId");
                return data;
            }).collect(Collectors.toList());
            for (String formDefId : formDefIds) {
                if (mappedProperties.containsKey("entity.form." + formDefId)) {
                    val formDataIds = page.getItems().stream().filter(it -> formDefId.equals(it.getFormDefId()))
                            .map(it -> it.getFormDataId()).collect(Collectors.toList());
                    val formDatas = DataQuery.identifier("entity.form." + formDefId).limit(formDataIds.size(), 1).filter(DataFilter.in("bid", formDataIds),
                            DataSimple.class, System.currentTimeMillis()).getItems();
                    dataList.forEach(data -> formDatas.stream().filter(formData -> formData.getBid().equals(data.get("formDataId")))
                            .findFirst().ifPresent(formData -> mappedProperties.get("entity.form." + formDefId).forEach(property -> {
                                val value = formData.getProperties().get(property.getProperty());
                                data.put(property.getProperty() + "@entity.form." + formDefId, dynamicValueFormat(value));
                            })));
                }
            }
            val terminations = DataQuery.identifier("entity.hr.TerminationApply").limit(page.getItems().size(), 1)
                    .filter(DataFilter.in("bid", page.getItems().stream().map(it -> it.getBid()).collect(Collectors.toList())),
                            DataSimple.class, System.currentTimeMillis()).getItems();
            dataList.forEach(data -> terminations.stream().filter(termination -> termination.getBid().equals(data.get("bid")))
                    .findFirst().ifPresent(termination -> mappedProperties.get("entity.hr.TerminationApply").forEach(property -> {
                        val value = termination.getProperties().get(property.getProperty());
                        data.put(property.getProperty() + "@entity.hr.TerminationApply", dynamicValueFormat(value));
                    })));
            dataList.forEach(it -> replaceDisplay(it));
            result.setPageData(new PageResult<>(dataList, page.getPageNo(), page.getPageSize(), page.getTotal()));
        }
        result.setUserDynamicConfig(userDynamicSet);
        result.setDynamicConfig(dynamicSet);
        return result;
    }

    private void replaceDisplay(Map data) {
        Map<String, Map<String, String>> replace = Maps.map(
                "<EMAIL>",
                Maps.map(
                        "APPROVE", MessageHandler.getMessage("termination.status.APPROVE", WebUtil.getRequest()),
                        "REFUSE", MessageHandler.getMessage("termination.status.REFUSE", WebUtil.getRequest()),
                        "REVOKE", MessageHandler.getMessage("termination.status.REVOKE", WebUtil.getRequest()),
                        "CANCELLED", MessageHandler.getMessage("termination.status.CANCELLED", WebUtil.getRequest()),
                        "IN_PROCESS", MessageHandler.getMessage("termination.status.IN_PROCESS", WebUtil.getRequest())
                ),
                "<EMAIL>",
                Maps.map(
                        "VOLUNTARY", MessageHandler.getMessage("termination.type.VOLUNTARY", WebUtil.getRequest()),
                        "PASSIVE", MessageHandler.getMessage("termination.type.PASSIVE", WebUtil.getRequest())
                )
        );
        for (String key : replace.keySet()) {
            if (data.containsKey(key)) {
                data.put(key, replace.get(key).get(data.get(key)));
            }
        }
    }

    private Object dynamicValueFormat(PropertyValue propertyValue) {
        if (propertyValue instanceof SimplePropertyValue) {
            if (((SimplePropertyValue) propertyValue).getType().isArray()) {
                return ((SimplePropertyValue) propertyValue).getArrayValues();
            } else {
                return ((SimplePropertyValue) propertyValue).getValue();
            }
        }
        if (propertyValue instanceof DictSimple) {
            return ((DictSimple) propertyValue).getText();
        }
        if (propertyValue instanceof PhoneSimple) {
            return ((PhoneSimple) propertyValue).getValue();
        }
        if (propertyValue instanceof EnumSimple) {
            return ((EnumSimple) propertyValue).getText();
        }
        if (propertyValue instanceof Address) {
            return ((Address) propertyValue).getText();
        }
        if (propertyValue instanceof JobGradeRange) {
            return ((JobGradeRange) propertyValue).getChannelName();
        }
        if (propertyValue instanceof EmpSimple) {
            return ((EmpSimple) propertyValue);
        }
        if (propertyValue instanceof Attachment) {
            return ((Attachment) propertyValue).toText();
        }
        return propertyValue;
    }

    private void addDynamicColumnValue(Map data, String identifier, Map<String, List<MetadataPropertyDto>> mappedProperties, long time, String empId, String empIdProp) {
        if (mappedProperties.containsKey(identifier)) {
            DataQuery.identifier(identifier).limit(1, 1).filter(DataFilter.eq(empIdProp, empId)
                            .andNe("deleted", Boolean.TRUE.toString()),
                    DataSimple.class, time).getItems().stream().findFirst().ifPresent(emp -> {
                mappedProperties.get(identifier).forEach(property -> {
                    val value = emp.getProperties().get(property.getProperty());
                    Object formatValue = dynamicValueFormat(value);

                    if (!"enabled".equals(postTxtShowCode)) {
                        if ("postTxt".equals(property.getProperty())) {
                            val post = (String) formatValue;
                            if (StringUtils.isNotEmpty(post) && post.indexOf("(") >= 0) {
                                formatValue = post.substring(0, post.lastIndexOf("("));
                            }
                        }
                    }
                    data.put(property.getProperty() + "@" + identifier, formatValue);
                });
            });
        }
    }

    /**
     * 归档文件
     *
     * @param page
     */
    public List<TerminationApply> loadArchiveData(BasePage page) {
        List<TerminationApply> terminationApplies = TerminationApply.getArchiveData(page);
        return Optional.ofNullable(terminationApplies).map(it -> it.stream().filter(o1 -> o1.getAttachment() != null
                        && CollectionUtils.isNotEmpty(o1.getAttachment().getUrls())).collect(Collectors.toList()))
                .orElse(Lists.list());
    }

    public PageResult<TerminationListVo> terminationAblePage(TerminationQueryDto queryDto, String keywords, TerminationStatus status,
                                                             String org, TerminationType terminationType) {
        PageResult<TerminationApply> page = pageWorkplace(queryDto, keywords, status, org, terminationType);
        List<TerminationListVo> listVos = ObjectConverter.convertList(page.getItems(), TerminationListVo.class);
        Result<String> kv = tenantFeignClient.getKv(HrConstant.TERMINATION_DATE_RULE + UserContext.getTenantId());
        if (kv.isSuccess() && kv.getData() != null) {
            TerminationRuleDto dto = FastjsonUtil.toObject(kv.getData(), TerminationRuleDto.class);
            if (null == dto) {
                throw new ServerException("人事设置--离职管理未设置");
            }
            if (dto.getCancelTermination() != null) {
                long today = DateUtil.getCurrentTimestamp();
                int day = dto.getCancelTermination().getDay() != null ? dto.getCancelTermination().getDay() : 0;
                listVos.forEach(st -> {
                    Date date = new Date(st.getTerminationTime());
                    DateUtil.addDays(date, (long) day);
                    st.setCancelTerminateAble(!(today >= date.getTime()));
                });
            }
        }
        return new PageResult<>(listVos, page.getPageNo(), page.getPageSize(), page.getTotal());
    }

    public TerminationApply one(String bid) {
        TerminationApply one = TerminationApply.load(bid);
        one.setTags(getTags(one.getConfigId(), one.getApplyEmpId()));
        return one;
    }

    private String getTags(String configId, String applyEmpId) {
        List<TagInfoKVVo> tagProperties = terminationConfigService.one(configId).getTagProperties();
        if (CollectionUtils.isEmpty(tagProperties)) return null;
        List<String> idList = tagProperties.stream().map(o -> o.getValue()).collect(Collectors.toList());
        List<EmpTagInfoVo> allEmpTagInfo = empTagInfoService.getEmpTagInfo(applyEmpId, System.currentTimeMillis(), true);
        if (CollectionUtils.isEmpty(allEmpTagInfo)) return null;
        return allEmpTagInfo.stream().filter(o -> idList.contains(o.getTagBid()))
                .map(o -> o.getTagName()).collect(Collectors.joining(","));
    }

    public TerminationTemplateDetailVo getDetail(String bid) {
        var terminationTemplateDetail = new TerminationTemplateDetailVo();
        TerminationApply one = one(bid);
        terminationTemplateDetail.setTerminationApply(one);
        var values = TerminationProperty.values();
        Map<String, Object> data = Maps.map();
        terminationTemplateDetail.setData(data);
        for (TerminationProperty terminationProperty : values) {
            String terminationPropertyValue = terminationProperty.getValue(one, false, true);
            if (terminationPropertyValue != null) {
                data.put(terminationProperty.name(), terminationPropertyValue);
            }
        }
        if (StringUtils.isNotBlank(one.getFormDefId()) && StringUtils.isNotBlank(one.getFormDataId())) {
            Map formDataMap = formService.getFormDataMap(one.getFormDefId(), one.getFormDataId());
            terminationTemplateDetail.setFormDef(formService.getFormDefById(one.getFormDefId()));
            terminationTemplateDetail.setFormData(formDataMap);
        }
        return terminationTemplateDetail;
    }

    public TerminationApply oneMine(String bid) {
        val apply = TerminationApply.load(bid);
        val mine = String.valueOf(SecurityUserUtil.getSecurityUserInfo().getEmpId())
                .equals(apply.getEmp().getEmpId()) && apply.getApplyType().equals(TerminationApplyType.BY_EMPLOYEE);
        if (mine) {
            return TerminationApply.load(bid);
        } else {
            throw new ServerException("单据不存在");
        }
    }

    public List<TerminationApply> listMine() {
        val empId = SecurityUserUtil.getSecurityUserInfo().getEmpId();
        return TerminationApply.listSelfApplyByEmp(String.valueOf(empId));
    }

    public PageResult<TerminationApply> listByEmps(List<String> empIds, TerminationStatus status, Long terminationTime, int pageNo, int pageSize) {
        return TerminationApply.listByEmps(empIds, status, terminationTime, pageNo, pageSize);
    }

    public void addToBlacklist(String terminationApplyId, String reasonValue) {
        val apply = TerminationApply.load(terminationApplyId);
        apply.setIntoBlacklist(true);
        if (StringUtils.isNotEmpty(reasonValue)) {
            apply.setBlacklistReason(DictSimple.doDictSimple(reasonValue));
        }
        blacklistService.save(apply);
    }

    public TerminationApplierInfo getApplierInfo(String empId) {
        List<Map<String, String>> items = DataQuery.identifier("entity.hr.EmpWorkInfo").decrypt().specifyLanguage()
                .queryInvisible()
                .filterProperties(DataFilter.eq("empId", empId)
                                .andNe("deleted", Boolean.TRUE.toString()),
                        Lists.list("company", "companyTxt",
                                "enName", "post", "postTxt", "name",
                                "organize", "organizeTxt", "empType.dict.value", "hireDate"), System.currentTimeMillis()).getItems();
        if (items.size() == 0) {
            throw new ServerException("员工不存在");
        }
        val info = new TerminationApplierInfo();
        val infoMap = items.get(0);
        info.setCompany(infoMap.get("company"));
        info.setCompanyName(infoMap.get("companyTxt"));
        val empTypeValue = infoMap.get("empType.dict.value");
        val dictSimple = DictSimple.doDictSimple(empTypeValue);
        info.setEmpType(empTypeValue);
        info.setEmpTypeName(dictSimple.getText());
        info.setEngName(infoMap.get("enName"));
        val hireDate = infoMap.get("hireDate");
        val onboardingTime = StringUtils.isNotEmpty(hireDate) ? Long.parseLong(hireDate) : null;
        info.setOnboardingTime(onboardingTime);
        info.setOrgId(infoMap.get("organize"));
        info.setOrgName(infoMap.get("organizeTxt"));
        info.setPost(infoMap.get("post"));
        info.setPostName(infoMap.get("postTxt"));
        info.setName(infoMap.get("name"));
        return info;

    }

    @PaasTransactional
    public TerminationApply cancel(String applyId) {
        TerminationApply apply = TerminationApply.load(applyId);
        PreCheck.preCheckNotNull(apply, ErrorMessage.fromCode("caidao.exception.error_30006"));
        PreCheck.preCheckArgument(!TerminationStatus.APPROVE.equals(apply.getStatus()), "Apply cannot be cancelled");
        apply.cancel();
        terminationChangeService.revoke(apply);
        // 移除归档文件
        archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.TERMINATE, applyId, ArchivePolicy.DELETE);
        return apply;
    }


    public TerminationApply revoke(TerminationRevokeDto dto) {
        WfTaskRevokeDTO wfRevokeDto = new WfTaskRevokeDTO();
        wfRevokeDto.setBusinessKey(dto.getBusinessKey());
        try {
            Result<?> result = wfOperateFeignClient.revokeProcessOfTask(wfRevokeDto);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw new ServerException("撤销失败");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
        val apply = TerminationApply.getByBusinessKey(dto.getBusinessKey());
        apply.setStatus(TerminationStatus.REVOKE);
        apply.update();
        return apply;
    }

    public TerminationCancelVo checkTerminationEsignFile(TerminationApply apply) {
        Result<List<ContractSignDto>> result = esignFeignClient.getContractByEmp(apply.getEmp()
                .getEmpId(), BUSINESS_PROCESS);
        if (!result.isSuccess()) {
            throw new ServerException("Failed to fetch esign file");
        }
        //  离职文件为空
        List<ContractSignDto> list = result.getData();
        if (CollectionUtils.isEmpty(list)) {
            return new TerminationCancelVo();
        }
        // 对应businessKey的离职文件为空
        Option<ContractSignDto> option = Sequences.sequence(list)
                .find(contract -> apply.getBid().equals(contract.getBusinessId()));
        if (option.isEmpty()) {
            return new TerminationCancelVo();
        }

        // 返回合同所属业务流程，类型
        ContractSignDto contract = option.get();
        return new TerminationCancelVo(true, contract.getBusinessProcess()
                .getValue(), contract.getBusinessType(), contract.getProcessStatus().getValue());
    }

    public Map<String, String> getNoticeVar(String businessKey, List<String> codes) {
        Map<String, String> results = Maps.map();
        val apply = TerminationApply.load(StringUtils.substringBefore(businessKey, "_"));
        Map<String, Object> varValueMap = Maps.map();
        for (String code : codes) {
            String value = "";
            if (code.equals(TerminationProperty.INTO_BLACKLIST.getCode() + "_REASON")) {
                value = TerminationProperty.INTO_BLACKLIST.getValue(apply, false, true);
            } else if (code.indexOf(".") > -1) {
                value = terminationWorkflowService.findValueByEventVarCode(varValueMap, code, apply.getApplyEmpId());
            } else {
                try {
                    value = TerminationProperty.valueOf(code)
                            .getValue(apply, false, false);
                } catch (IllegalArgumentException e) {
                    val valueObj = formService.getFormDataMap(apply.getFormDefId(), apply.getFormDataId()).get(code);
                    value = valueObj != null ? String.valueOf(valueObj) : null;
                }
            }
            results.put(code, value);
        }
        return results;
    }

    public List<TerminationExportVo> getExportVoList(TerminationApplyQueryDto queryDto) {
        int pageNo = 1;
        //组织查询一次限制1000
        int pageSize = 1000;
        TerminationQueryDto terminationQueryDto = ObjectConverter.convert(queryDto, TerminationQueryDto.class);
        terminationQueryDto.setPageNo(pageNo);
        terminationQueryDto.setPageSize(pageSize);
        PageResult<TerminationApply> page = page(terminationQueryDto, queryDto.getKeywords(), TerminationStatus.findEnumByValue(queryDto.getStatus()), queryDto.getOrg(), TerminationType.findEnumByValue(queryDto.getTerminationType()));

        Map<String, List<MetadataPropertyVo>> cusMap = new HashMap<>();
        Map<String, Map<String, MetadataPropertyVo>> cusFieldMap = new HashMap<>();

        List<MetadataPropertyVo> empWorkPropList = new ArrayList<>(200);
        Map<String, MetadataPropertyVo> empWorkCusField = empExtFieldService.getCusField("entity.hr.EmpWorkInfo", empWorkPropList);
        cusMap.put("empWork", empWorkPropList);
        cusFieldMap.put("empWork", empWorkCusField);

        List<MetadataPropertyVo> empPrivatePropList = new ArrayList<>(200);
        Map<String, MetadataPropertyVo> empPrivateCusField = empExtFieldService.getCusField("entity.hr.EmpPrivateInfo", empPrivatePropList);
        cusMap.put("empPrivate", empPrivatePropList);
        cusFieldMap.put("empPrivate", empPrivateCusField);

        List<MetadataPropertyVo> orgPropList = new ArrayList<>(200);
        Map<String, MetadataPropertyVo> orgPropListCusField = empExtFieldService.getCusField("entity.hr.Org", orgPropList);
        cusMap.put("org", orgPropList);
        cusFieldMap.put("org", orgPropListCusField);

        List<TerminationExportVo> exportVoList = toTerminationExportVo(page, cusMap, cusFieldMap);
        while (pageNo * pageSize < page.getTotal()) {
            pageNo++;
            terminationQueryDto.setPageNo(pageNo);
            page = page(terminationQueryDto, queryDto.getKeywords(), TerminationStatus.findEnumByValue(queryDto.getStatus()), queryDto.getOrg(), TerminationType.findEnumByValue(queryDto.getTerminationType()));
            List<TerminationExportVo> terminationExportVoList = toTerminationExportVo(page, cusMap, cusFieldMap);
            exportVoList.addAll(terminationExportVoList);
        }

        return exportVoList;
    }

    @NotNull
    public List<TerminationExportVo> toTerminationExportVo(PageResult<TerminationApply> page, Map<String, List<MetadataPropertyVo>> cusMap, Map<String, Map<String, MetadataPropertyVo>> cusFieldMap) {

        List<TerminationExportVo> vos = new ArrayList<>();
        List<TerminationApply> terminationApplyList = page.getItems();

        List<String> workPlaceIds = new ArrayList<>(terminationApplyList.size());
        List<String> empIds = new ArrayList<>(terminationApplyList.size());
        for (TerminationApply apply : terminationApplyList) {
            if (apply.getEmp() != null && StringUtils.isNotEmpty(apply.getEmp().getEmpId())) {
                empIds.add(apply.getEmp().getEmpId());
            }
            if (StringUtils.isNotEmpty(apply.getWorkplace()) && !workPlaceIds.contains(apply.getWorkplace())) {
                workPlaceIds.add(apply.getWorkplace());
            }
        }
//        List<String> empIds = terminationApplyList.stream().map(terminationApply -> terminationApply.getEmp().getEmpId()).filter(StringUtils::isNotEmpty).collect(Collectors.toList());

        Map<String, EmpWorkInfoVo> empWorkInfoDoMap = new HashMap<>();
        Map<String, EmpPrivateInfoVo> empPrivateInfoVoMap = new HashMap<>();
        Map<String, EmpEduInfoDo> empEduInfoDoMap = new HashMap<>();
        Map<String, EmpOtherContractDo> empOtherContractDoMap = new HashMap<>();
        Map<String, OrgVo> orgVoMap = new HashMap<>();
        String tenantName = orgDomainService.getTenantName();

        if (!CollectionUtils.isEmpty(empIds)) {
            //员工任职信息
            List<EmpWorkInfoDo> empWorkInfos = empWorkInfoDomainService.getEmpListByEmpIds(empIds, System.currentTimeMillis());
            List<EmpWorkInfoVo> empWorkInfoVos = ObjectConverter.convertList(empWorkInfos, EmpWorkInfoVo.class);
            empWorkInfoDoMap = empWorkInfoVos.stream().collect(Collectors.toMap(EmpWorkInfoVo::getEmpId, obj -> obj, (A, B) -> A));
            // 自定义字段查询
            for (EmpWorkInfoDo empWorkInfoDo : empWorkInfos) {
                Map<String, Object> ext = new HashMap<>();
                ExtFieldUtil.doExtField(cusMap.get("empWork"), cusFieldMap.get("empWork"), empWorkInfoDo, ext);
                EmpWorkInfoVo empWorkInfoVo = empWorkInfoDoMap.get(empWorkInfoDo.getEmpId());
                empWorkInfoVo.setExt(ext);
            }

            //员工个人信息
            List<EmpPrivateInfoDo> empPrivateInfoDoList = empPrivateInfoService.getAllEmpPrivateInfoListByEmpId(empIds);
            List<EmpPrivateInfoVo> empPrivateInfoVos = ObjectConverter.convertList(empPrivateInfoDoList, EmpPrivateInfoVo.class);
            empPrivateInfoVoMap = empPrivateInfoVos.stream().collect(Collectors.toMap(EmpPrivateInfoVo::getEmpId, obj -> obj, (A, B) -> A));
            for (EmpPrivateInfoDo empPrivateInfoDo : empPrivateInfoDoList) {
                Map<String, Object> ext = new HashMap<>();
                ExtFieldUtil.doExtField(cusMap.get("empPrivate"), cusFieldMap.get("empPrivate"), empPrivateInfoDo, ext);
                EmpPrivateInfoVo empPrivateInfoVo = empPrivateInfoVoMap.get(empPrivateInfoDo.getEmpId());
                empPrivateInfoVo.setExt(ext);
            }

            //教育经历
            List<EmpEduInfoDo> educationInfos = empEduInfoDomainService.getEducationInfos(empIds);
            empEduInfoDoMap = educationInfos.stream().collect(Collectors.toMap(EmpEduInfoDo::getEmpId, obj -> obj, (A, B) -> A));

            //员工合同信息
            List<EmpOtherContractDo> empOtherContractDos = empOtherContractDomainService.getListByEmpIds(empIds);
            empOtherContractDoMap = empOtherContractDos.stream().collect(Collectors.toMap(EmpOtherContractDo::getEmpId, obj -> obj, (A, B) -> A));

            //组织架构信息
            List<String> orgIds = terminationApplyList.stream().map(TerminationApply::getOrgId)
                    .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            List<OrgDo> orgDoList = orgDomainService.selectAllByIds(orgIds, System.currentTimeMillis());
            List<OrgVo> orgVoList = ObjectConverter.convertList(orgDoList, OrgVo.class);
            orgVoMap = orgVoList.stream().collect(Collectors.toMap(OrgVo::getBid, obj -> obj, (A, B) -> A));
            for (OrgDo orgDo : orgDoList) {
                Map<String, Object> ext = new HashMap<>();
                ExtFieldUtil.doExtField(cusMap.get("org"), cusFieldMap.get("org"), orgDo, ext);
                OrgVo orgVo = orgVoMap.get(orgDo.getBid());
                orgVo.setExt(ext);
            }
        }
        List<WorkplaceDo> workplaceDos = workplaceDomainService.selectByIds(workPlaceIds);
        Map<String, WorkplaceDo> collect = workplaceDos.stream().collect(Collectors.toMap(WorkplaceDo::getBid, st -> st, (v1, v2) -> v1));
        //缓存
        Map<String, FormDefDto> formDefDtoMap = new HashMap<>();
        for (TerminationApply terminationApply : terminationApplyList) {
            TerminationExportVo terminationExportVo = new TerminationExportVo();
            if (terminationApply.getWorkplace() != null) {
                terminationExportVo.setWorkplaceName(collect.get(terminationApply.getWorkplace()) == null ? null : collect.get(terminationApply.getWorkplace()).getName());
            }
            EmpSimple emp = terminationApply.getEmp();
            if (emp != null) {
                terminationExportVo.setWorkno(emp.getWorkno());
                terminationExportVo.setName(emp.getName());
                terminationExportVo.setEnName(emp.getEnName());
                //员工任职信息
                EmpWorkInfoVo empWorkInfoVo = empWorkInfoDoMap.getOrDefault(emp.getEmpId(), null);
                if (empWorkInfoVo != null) {
                    if (empWorkInfoVo.getDivisionAgeToYear() != null) {
                        terminationExportVo.setDivisionAgeToYear(String.valueOf(empWorkInfoVo.getDivisionAgeToYear().intValue()));
                    }
                    if (empWorkInfoVo.getLeadEmpId() != null) {
                        terminationExportVo.setLeadEmpId(empWorkInfoVo.getLeadEmpId().getName());
                    }
                    if (empWorkInfoVo.getExt() != null && empWorkInfoVo.getExt().get("empsubtype") != null) {
                        DictSimple dictSimple = (DictSimple) empWorkInfoVo.getExt().get("empsubtype");
                        terminationExportVo.setEmpsubtype(dictSimple.getText());
                    }
                }
                //员工个人信息
                EmpPrivateInfoVo empPrivateInfoVo = empPrivateInfoVoMap.getOrDefault(emp.getEmpId(), null);
                if (empPrivateInfoVo != null) {
                    if (empPrivateInfoVo.getNationality() != null) {
                        terminationExportVo.setNationality(empPrivateInfoVo.getNationality().getText());
                    }
                    terminationExportVo.setCardNo(empPrivateInfoVo.getCardNo());
                    if (empPrivateInfoVo.getPhone() != null) {
                        terminationExportVo.setPhone(empPrivateInfoVo.getPhone().getValue());
                    }
                    terminationExportVo.setEmail(empPrivateInfoVo.getEmail());
                    if (empPrivateInfoVo.getSex() != null) {
                        terminationExportVo.setSex(empPrivateInfoVo.getSex().getText());
                    }
                    if (StringUtils.isNotBlank(empPrivateInfoVo.getCardNo()) || empPrivateInfoVo.getDivisionAge() != null) {
                        try {
                            val age = StringUtils.isNotBlank(empPrivateInfoVo.getCardNo()) ?
                                    String.valueOf(IdCardUtil.getAgeByIdCard(empPrivateInfoVo.getCardNo())) :
                                    String.valueOf(empPrivateInfoVo.getDivisionAge());
                            terminationExportVo.setDivisionAge(age);
                        } catch (Exception e) {
                            log.info("empId={} cardNo is illegal, cardNo={}", Objects.nonNull(empPrivateInfoVo) ? empPrivateInfoVo.getEmpId() : "", Objects.isNull(empPrivateInfoVo) ? "" : empPrivateInfoVo.getCardNo());
                            terminationExportVo.setDivisionAge("");
                        }
                    }
                    if (empPrivateInfoVo.getExt() != null && empPrivateInfoVo.getExt().get("ctalevel") != null) {
                        DictSimple dictSimple = (DictSimple) empPrivateInfoVo.getExt().get("ctalevel");
                        terminationExportVo.setCtalevel(dictSimple.getText());
                    }
                }
                //教育经历
                EmpEduInfoDo empEduInfoDo = empEduInfoDoMap.getOrDefault(emp.getEmpId(), null);
                if (empEduInfoDo != null && empEduInfoDo.getBackground() != null) {
                    terminationExportVo.setBackground(empEduInfoDo.getBackground().getText());
                }

                EmpOtherContractDo empOtherContractDo = empOtherContractDoMap.getOrDefault(emp.getEmpId(), null);
                if (empOtherContractDo != null && empOtherContractDo.getSignAgreement() != null) {
                    terminationExportVo.setSignAgreement(empOtherContractDo.getSignAgreement() ? "是" : "否");
                } else {
                    terminationExportVo.setSignAgreement("否");
                }
            }
            //组织相关信息
            OrgVo orgVo = orgVoMap.getOrDefault(terminationApply.getOrgId(), null);
            if (orgVo != null) {
                terminationExportVo.setCode(orgVo.getCode());
                //任职组织全路径
                terminationExportVo.setOrganizePathTxt(getOrganizeFullPathTxt(tenantName, orgVo));

                if (orgVo.getExt() != null && orgVo.getExt().get("orglevel") != null) {
                    DictSimple dictSimple = (DictSimple) orgVo.getExt().get("orglevel");
                    terminationExportVo.setOrglevel(dictSimple.getText());
                }
                if (orgVo.getExt() != null && orgVo.getExt().get("Function") != null) {
                    DictSimple dictSimple = (DictSimple) orgVo.getExt().get("Function");
                    terminationExportVo.setBusinessLine(dictSimple.getText());
                }
            }
            TerminationApplierInfo applierInfo = terminationApply.getApplierInfo();
            if (applierInfo != null) {
                if (applierInfo.getOnboardingTime() != null) {
                    terminationExportVo.setOnboardingTime(DateUtil.formatDate(applierInfo.getOnboardingTime()));
                }
                terminationExportVo.setEmpTypeName(applierInfo.getEmpTypeName());
                terminationExportVo.setOrgName(applierInfo.getOrgName());
                terminationExportVo.setPostName(applierInfo.getPostName());
                terminationExportVo.setEmpTypeName(applierInfo.getEmpTypeName());
                terminationExportVo.setCompanyName(applierInfo.getCompanyName());
                terminationExportVo.setWorkplaceName(applierInfo.getWorkplaceName());
            }
            terminationExportVo.setTerminationTime(DateUtil.formatDate(terminationApply.getTerminationTime()));
            if (terminationApply.getType() != null) {
                terminationExportVo.setType(terminationApply.getType() == TerminationType.VOLUNTARY ? "主动离职" : "被动离职");
            }
            DictSimple terminationReason = terminationApply.getTerminationReason();
            if (terminationReason != null) {
                terminationExportVo.setReason(terminationReason.getText());
            }
            DictSimple empTerminationReason = terminationApply.getEmpTerminationReason();
            if (empTerminationReason != null) {
                terminationExportVo.setTerminationReason(empTerminationReason.getText());
            }
            TerminationStatus terminationStatus = terminationApply.getStatus();
            if (terminationStatus != null) {
                terminationExportVo.setStatus(terminationStatus.name);
            }
            terminationExportVo.setIntoBlacklist(terminationApply.isIntoBlacklist() ? "是" : "否");
            //表单内嵌字段
            if (terminationApply.getFormDefId() != null && terminationApply.getFormDataId() != null) {
                Map formDataMap = null;
                try {
                    formDataMap = formService.getFormDataMap(terminationApply.getFormDefId(), terminationApply.getFormDataId());
                } catch (Exception e) {
                    log.error("离职申请单：{} 获取表单异常", terminationApply);
                }
                if (formDataMap != null) {
                    //从缓存中取值
                    FormDefDto formDefDto = formDefDtoMap.get(terminationApply.getFormDefId());
                    if (formDefDto != null) {
                        formService.convertFormValue(formDataMap, formDefDto);
                    } else {
                        Result<FormDefDto> formDefResult = formFeignClient.getFormDefById(terminationApply.getFormDefId());
                        if (formDefResult.isSuccess()) {
                            FormDefDto formDef = formDefResult.getData();
                            //放入缓存
                            formDefDtoMap.put(terminationApply.getFormDefId(), formDef);
                            formService.convertFormValue(formDataMap, formDef);
                        }
                    }
                    terminationExportVo.setFieldMap(formDataMap);
                }
            }
            vos.add(terminationExportVo);
        }
        return vos;
    }

    private String getOrganizeFullPathTxt(String tenantName, OrgVo orgVo) {
        String organizeFullPathFormat = "%s/%s/%s",
                organizePathFormat = "%s/%s",
                organizeFullPathTxt = "";
        if (orgVo.getPid() != null && StringUtils.isNotEmpty(orgVo.getPid().getNamePath())) {
            organizeFullPathTxt = orgVo.getPid().getNamePath();
        }
        organizeFullPathTxt = StringUtil.isEmpty(organizeFullPathTxt)
                ? String.format(organizePathFormat, tenantName, orgVo.getFullName())
                : String.format(organizeFullPathFormat, tenantName, organizeFullPathTxt, orgVo.getFullName());
        return organizeFullPathTxt;
    }

    public List<TagProperty> installTerminationExportProperty() {
        List<TagProperty> list = new ArrayList<>();
        int order = 1;
        addTagPropertyToList(list, "workno", "工号", order++);
        addTagPropertyToList(list, "name", "姓名", order++);
        addTagPropertyToList(list, "enName", "英文名", order++);
        addTagPropertyToList(list, "nationality", "国籍", order++);
        addTagPropertyToList(list, "cardNo", "证件号", order++);
        addTagPropertyToList(list, "phone", "手机", order++);
        addTagPropertyToList(list, "email", "个人电子邮箱", order++);
        addTagPropertyToList(list, "sex", "性别", order++);
        addTagPropertyToList(list, "divisionAge", "年龄", order++);
        addTagPropertyToList(list, "background", "学历", order++);
        addTagPropertyToList(list, "organizePathTxt", "任职组织", order++);
        addTagPropertyToList(list, "leadEmpId", "直接上级", order++);
        addTagPropertyToList(list, "orglevel", "组织层级", order++);
        addTagPropertyToList(list, "businessLine", "业务线", order++);
        addTagPropertyToList(list, "code", "组织编码", order++);
        addTagPropertyToList(list, "onboardingTime", "入职日期", order++);
        addTagPropertyToList(list, "empTypeName", "用工类型", order++);
        addTagPropertyToList(list, "empsubtype", "用工子类型", order++);
        addTagPropertyToList(list, "orgName", "所属组织", order++);
        addTagPropertyToList(list, "postName", "岗位", order++);
        addTagPropertyToList(list, "companyName", "合同公司", order++);
        addTagPropertyToList(list, "workplaceName", "工作地", order++);
        addTagPropertyToList(list, "terminationTime", "离职时间", order++);
        addTagPropertyToList(list, "type", "离职类型", order++);
        addTagPropertyToList(list, "reason", "离职原因（LM填写）", order++);
        addTagPropertyToList(list, "terminationReason", "离职原因（员工申请 ）", order++);
        addTagPropertyToList(list, "divisionAgeToYear", "工龄（年）", order++);
        addTagPropertyToList(list, "signAgreement", "是否签署竞业协议", order++);
        addTagPropertyToList(list, "ctalevel", "CTA等级", order++);
        addTagPropertyToList(list, "intoBlacklist", "是否黑名单", order++);
        addTagPropertyToList(list, "status", "审批状态", order++);
        addTagPropertyToList(list, "workplaceName", "工作地", order++);

        List<TerminationConfig> configList = terminationConfigService.list(TerminationApplyType.BY_AGENT, false);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(configList)) {
            Optional<TerminationConfig> first = configList.stream().filter(terminationConfig -> terminationConfig.getStatus() == TerminationConfigStatus.ENABLED).findFirst();
            if (first.isPresent()) {
                TerminationConfig terminationConfig = first.get();
                if (StringUtil.isEmpty(terminationConfig.getFormDefId())) {
                    return list;
                }
                Result<FormDefDto> formDefResult = formFeignClient.getFormDefById(terminationConfig.getFormDefId());
                if (formDefResult.isSuccess()) {
                    FormDefDto formDef = formDefResult.getData();
                    List<FormDefMetadataDto> properties = formDef.getProperties();
                    for (FormDefMetadataDto formDefMetadataDto : properties) {
                        addTagPropertyToList(list, formDefMetadataDto.getProperty(), formDefMetadataDto.getName(), order++);
                    }
                }
            }
        }
        return list;
    }


    public void addTagPropertyToList(List<TagProperty> list, String property, String propertyTxt, int order) {
        list.add(new TagProperty(property, propertyTxt, order));
    }

    public void wash() {
        TerminationQueryDto queryDto = new TerminationQueryDto();
        queryDto.setPageNo(1);
        queryDto.setPageSize(100);
        while (true) {
            PageResult<TerminationApply> pr = page(queryDto, null, null, null, null);
            if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
                return;
            }
            pr.getItems().forEach(ta -> {
                TerminationApplierInfo applierInfo = ta.getApplierInfo();
                ta.setEmpType(DictSimple.doDictSimple(applierInfo.getEmpType()));
                ta.update();
            });
            queryDto.setPageNo(queryDto.getPageNo() + 1);
        }
    }

    @PaasTransactional
    public Result completeApplierInfo(Integer pageNo, Integer pageSize) {
        TerminationQueryDto queryDto = new TerminationQueryDto();
        if (ObjectUtil.isNotEmpty(pageNo) && pageNo > 0) {
            //指定 数目更新；
            queryDto.setPageNo(pageNo);
            queryDto.setPageSize(pageSize);
            PageResult<TerminationApply> pr = page(queryDto, null, null, null, null);
            if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
                return Result.ok("无数据更新");
            }
            List<TerminationApply> items = pr.getItems();
            List<String> workplaceIds = items.stream().filter(terminationApply -> terminationApply.getWorkplace() != null).map(TerminationApply::getWorkplace).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(workplaceIds)) {
                return Result.ok("无数据更新");
            }
            List<WorkplaceDo> workplaceDos = workplaceDomainService.selectByIds(workplaceIds, UserContext.getTenantId());
            Map<String, String> workplaceBidToName = workplaceDos.stream().collect(Collectors.toMap(WorkplaceDo::getBid, WorkplaceDo::getName, (d1, d2) -> d1));
            for (TerminationApply item : items) {
                //CAIDAOM-2513 处理历史数据的工作地信息；
                if (StringUtil.isNotEmpty(item.getWorkplace()) && StringUtil.isEmpty(item.getApplierInfo().getWorkplaceId())) {
                    //申请信息中没有工作地 但是 员工信息有工作地
                    item.getApplierInfo().setWorkplaceId(item.getWorkplace());
                    if (workplaceBidToName.containsKey(item.getWorkplace())) {
                        item.getApplierInfo().setWorkplaceName(workplaceBidToName.get(item.getWorkplace()));
                        item.update();
                    }
                }
            }
            return Result.ok("pageNo:" + pageNo + "   pageSize:" + pageSize + "  数据更新完毕");
        } else {
            //全量更新 可能超时；
            queryDto.setPageNo(1);
            queryDto.setPageSize(1000);
            while (true) {
                PageResult<TerminationApply> pr = page(queryDto, null, null, null, null);
                if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
                    return Result.ok("数据全部更新，已无数据更新");
                }
                List<TerminationApply> items = pr.getItems();
                List<String> workplaceIds = items.stream().filter(terminationApply -> terminationApply.getWorkplace() != null).map(TerminationApply::getWorkplace).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(workplaceIds)) {
                    queryDto.setPageNo(queryDto.getPageNo() + 1);
                    continue;
                }
                List<WorkplaceDo> workplaceDos = workplaceDomainService.selectByIds(workplaceIds, UserContext.getTenantId());
                Map<String, String> workplaceBidToName = workplaceDos.stream().collect(Collectors.toMap(WorkplaceDo::getBid, WorkplaceDo::getName, (d1, d2) -> d1));
                for (TerminationApply item : items) {
                    //CAIDAOM-2513 处理历史数据的工作地信息；
                    if (StringUtil.isNotEmpty(item.getWorkplace()) && StringUtil.isEmpty(item.getApplierInfo().getWorkplaceId())) {
                        //申请信息中没有工作地 但是 员工信息有工作地
                        item.getApplierInfo().setWorkplaceId(item.getWorkplace());
                        if (workplaceBidToName.containsKey(item.getWorkplace())) {
                            item.getApplierInfo().setWorkplaceName(workplaceBidToName.get(item.getWorkplace()));
                            item.update();
                        }
                    }
                }
                queryDto.setPageNo(queryDto.getPageNo() + 1);
            }
        }
    }
}
