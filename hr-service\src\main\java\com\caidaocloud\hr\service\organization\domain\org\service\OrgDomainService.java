package com.caidaocloud.hr.service.organization.domain.org.service;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.domain.base.constant.BaseConstant;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpConcurrentPostLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.metadata.TenantConfigDto;
import com.caidaocloud.hr.service.organization.application.org.dto.OrgLabelData;
import com.caidaocloud.hr.service.organization.domain.cost.entity.CostCenterDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.CustomOrgRoleDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgReportExtendDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgOrPostQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgPageQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织领域服务
 *
 * <AUTHOR>
 * @Date 2021/12/01
 */
@Slf4j
@Service
public class OrgDomainService {
    private final static String TENANT_ROOT_NODE_BID = "0";
    // 根节点默认pid
    public final static String ROOT_PID = "-1";
    @Resource
    private OrgDo orgDo;
    @Resource
    private CostCenterDo costCenterDo;
    @Resource
    private OrgReportExtendDo orgReportExtendDo;
    @Resource
    private CacheService cacheService;
    @Resource
    private PostDo postDo;
    @Resource
    private CustomOrgRoleDo customOrgRoleDo;
    @Resource
    private EmpWorkInfoDo empWorkInfoDo;
    @Resource
    private EmpExtFieldService empExtFieldService;

    public String save(OrgDo data, Map ext) {
        doCostInfo(data);
        doValidPid(data);
        // 验证组织参数
        checkOrg(data);
        doReportSchema(data);
        // 处理责任人（组织、岗位）
        doLeaderOrgAndPost(data);
        doCustomOrgRole(data);

        // 验证
        data.checkReportSchema();

        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setTenantId(userInfo.getTenantId());
        data.setCreateBy(userId);
        data.setCreateTime(0 == data.getCreateTime() ? System.currentTimeMillis() : data.getCreateTime());
        data.setUpdateBy(userId);
        data.setUpdateTime(0 == data.getUpdateTime() ? data.getCreateTime() : data.getUpdateTime());

        List<OrgReportExtendDo> reportSchema = data.getReportSchema();
        List<String> targetIds = new ArrayList<>();
        if (null != reportSchema && !reportSchema.isEmpty()) {
            reportSchema.forEach(report -> {
                report.setTenantId(userInfo.getTenantId());
                report.setCreateBy(userId);
                report.setCreateTime(data.getCreateTime());
                report.setUpdateBy(userId);
                report.setUpdateTime(data.getCreateTime());

                report.setDataStartTime(data.getDataStartTime());
                report.setBid(orgReportExtendDo.save(report));
                targetIds.add(report.getBid());
            });
        }

        empExtFieldService.doCusExtProps(data.getDoIdentifier(), ext, data);

        data.setBid(SnowUtil.nextId());
        customOrgRoleDo.save(data.getBid(), data.getCustomOrgRoles(), data.getDataStartTime());
        orgDo.save(data, targetIds);
//        data.setBid(orgId);

        return data.getBid();
    }

    private void doCustomOrgRole(OrgDo data) {
        List<CustomOrgRoleDo> customOrgRoles = data.getCustomOrgRoles();
        if (customOrgRoles != null) {
            customOrgRoles.forEach(roleDo -> {
                if (StringUtils.isNotEmpty(roleDo.getLeaderPost())) {
                    PostDo postDo = this.postDo.selectById(roleDo.getLeaderPost(), data.getDataStartTime());
                    PreCheck.preCheckNotNull(postDo, "责任人岗位不存在");
                    roleDo.setLeaderPostTxt(postDo.getName());
                    roleDo.setPostOrg(postDo.getOrgId());
                    roleDo.setPostOrgTxt(postDo.getOrgName());
                }
            });
        }
    }

    public void update(OrgDo data, Map ext) {
        // 验证成本中心
        doCostInfo(data);
        // 验证上级组织
        doValidPid(data);
        // 验证组织参数
        checkOrg(data);
        // 处理汇报关系
        doReportSchema(data);
        // 处理责任人（组织、岗位）
        doLeaderOrgAndPost(data);
        // 处理岗位的组织名称
        doCustomOrgRole(data);
        // 验证
        data.checkReportSchema();

        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setTenantId(userInfo.getTenantId());
        data.setUpdateBy(userId);
        data.setUpdateTime(0 == data.getUpdateTime() ? System.currentTimeMillis() : data.getUpdateTime());

        List<OrgReportExtendDo> reportSchema = data.getReportSchema();
        List<String> targetIds = new ArrayList<>();
        if (null != reportSchema && !reportSchema.isEmpty()) {
            reportSchema.forEach(report -> {
                report.setTenantId(userInfo.getTenantId());
                if (StringUtil.isEmpty(report.getBid())) {
                    report.setCreateBy(userId);
                    report.setCreateTime(data.getUpdateTime());
                    report.setUpdateBy(userId);
                    report.setDataStartTime(data.getDataStartTime());
                    report.setBid(orgReportExtendDo.save(report));
                } else {
                    report.setUpdateBy(userId);
                    report.setDataStartTime(data.getDataStartTime());
                    report.setUpdateTime(data.getUpdateTime());
                    orgReportExtendDo.update(report);
                }

                targetIds.add(report.getBid());
            });
        }
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), ext, data);
        //先把角色保存好了，然后更新组织，最后发组织数据变更MQ,这样能保证消费MQ的时候能查到角色信息
        customOrgRoleDo.save(data.getBid(), data.getCustomOrgRoles(), data.getDataStartTime());
        orgDo.update(data, targetIds);
        if (Optional.ofNullable(data.getSyncUpdate()).orElse(false)) {
            updateChild(orgDo);
        }
    }

    private void checkOrg(OrgDo data) {
        // 验证组织名称重复
        orgDo.checkOrgName(data);
    }

    private void updateChild(OrgDo orgDo) {
        List<OrgDo> children = selectChildrenList(orgDo.getBid(), orgDo.getDataStartTime());
        children.forEach(org -> {
            customOrgRoleDo.save(org.getBid(), orgDo.getCustomOrgRoles(), org.getDataStartTime());
        });
    }

    /**
     * 处理岗位名称
     *
     * @param data
     */
    private void doLeaderOrgAndPost(OrgDo data) {
        if (StringUtils.isNotEmpty(data.getLeaderPost())) {
            PostDo postData = this.postDo.selectById(data.getLeaderPost(), data.getDataStartTime());
            PreCheck.preCheckNotNull(postData, "责任人岗位不存在");
            data.setLeaderPostTxt(postData.getName());
        }

        if (StringUtil.isNotEmpty(data.getLeaderOrganize())) {
            OrgDo orgData = this.orgDo.selectById(data.getLeaderOrganize(), data.getDataStartTime());
            PreCheck.preCheckNotNull(orgData, "责任人组织不存在");
            // 考虑兼岗组织
            data.setLeaderOrganizeTxt(orgData.getName());
        }
    }

    public void delete(String bid, Long dataStartTime) {
        // 查询从表数据
        OrgDo data = orgDo.queryRelatedProperties(bid, dataStartTime);
        if (null == data || null == data.getProperties()) {
            return;
        }

        // 存在员工，则不能删除
        if (empWorkInfoDo.getEmpByOrganize(bid, dataStartTime).size() > 0) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ORGANIZE_EXIST_EMPLOYEES));
        }

        // 删除从表数据
        PropertyValue pv = data.getProperties().get("reportExtend.bid");
        if (null != pv && pv instanceof SimplePropertyValue) {
            List<String> values = ((SimplePropertyValue) pv).getArrayValues();
            if (null != values && values.isEmpty()) {
                values.forEach(value -> {
                    orgReportExtendDo.delete(value, dataStartTime);
                });
            }
        }

        // 删除组织数据
        orgDo.delete(bid, dataStartTime);
    }

    public OrgDo getByBid(String bid, Long dataTime) {
        return orgDo.queryRelatedProperties(bid, dataTime);
    }

    public OrgDo selectById(String bid, Long dataTime) {
        OrgDo data = orgDo.queryRelatedProperties(bid, dataTime);
        if (null == data) {
            return data;
        }

        data.setCustomOrgRoles(customOrgRoleDo.selectByOrgId(data.getBid(), data.getDataStartTime()));

        PropertyValue propertyValue = data.getProperties().get("reportExtend.bid");
        if (null == propertyValue || !(propertyValue instanceof SimplePropertyValue)) {
            return data;
        }

        List<String> values = ((SimplePropertyValue) propertyValue).getArrayValues();
        if (null == values || values.isEmpty()) {
            return data;
        }

        List<OrgReportExtendDo> orgReportExtendDos = orgReportExtendDo.selectByIds(values, data.getDataStartTime());
        if (null != orgReportExtendDos && !orgReportExtendDos.isEmpty()) {
            orgReportExtendDos.forEach(ored -> {
                if (ROOT_PID.equals(ored.getOrgId())) {
                    ored.setOrgId(null);
                }
            });
        }
        data.setReportSchema(orgReportExtendDos);
        return data;
    }

    public List<OrgDo> selectByIds(List<String> bids, Long dataTime) {
        return orgDo.selectAllByIds(bids, dataTime);
    }

    public PageResult<OrgDo> selectPage(BasePage page) {
        return orgDo.selectPage(page);
    }

    public List<OrgDo> selectOrgList() {
        return orgDo.selectList();
    }

    public List<OrgDo> getOrgListByLeader(EmpReportLeaderDto empReportLeaderDto, Long dateTime) {
        return orgDo.getOrgListByLeader(empReportLeaderDto, dateTime);
    }

    public List<OrgDo> getEnableOrgListByLeader(EmpReportLeaderDto empReportLeaderDto, Long dateTime) {
        return orgDo.getEnableOrgListByLeader(empReportLeaderDto, dateTime);
    }

    public List<OrgDo> getOrgListByEmpConcurrentPostLeader(EmpConcurrentPostLeaderDto empConcurrentPostLeaderDto, Long dateTime) {
        return orgDo.getOrgListByEmpConcurrentPostLeader(empConcurrentPostLeaderDto, dateTime);
    }

    public void enableOrDisable(String bid, Long dataTime) {
        orgDo.enableOrDisable(bid, dataTime);
    }

    public List<TreeData<OrgDo>> getTreeList(Long dataTime) {
        return orgDo.tree(dataTime);
    }

    public List<OrgDo> getPageList(OrgPageQueryDto queryDto) {
        List<OrgDo> items = orgDo.getAllList(queryDto);
        // TODO: 2022/8/1 前端hrbp暂时只有选人组件
        Map<String, EmpSimple> hrbpMap = customOrgRoleDo.selectAllHrbpRole(queryDto.getDateTime())
                .stream().filter(cor -> null != cor.getLeaderEmp() && StringUtil.isNotEmpty(cor.getLeaderEmp().getEmpId()))
                .collect(Collectors.toMap(CustomOrgRoleDo::getOrgId, CustomOrgRoleDo::getLeaderEmp, (k, v) -> v));
        items.forEach(org -> org.setHrbpEmp(hrbpMap.get(org.getBid())));

        //增加hrbp的校验；
        String hrbpEmpParam = "";
        if (CollectionUtils.isNotEmpty(queryDto.getFilters())) {
            for (FilterElement filter : queryDto.getFilters()) {
                if ("hrbpEmp".equals(filter.getProp())) {
                    hrbpEmpParam = (String) filter.getValue();
                }
            }
        }
        if (StringUtils.isNotEmpty(hrbpEmpParam)) {
            String finalHrbpEmpParam = hrbpEmpParam;
            items = items.stream().filter(orgDo1 -> ObjectUtil.isNotEmpty(orgDo1.getHrbpEmp())
                    && (
                    (StringUtils.isNotEmpty(orgDo1.getHrbpEmp().getWorkno()) && orgDo1.getHrbpEmp().getWorkno().contains(finalHrbpEmpParam))
                            || (StringUtils.isNotEmpty(orgDo1.getHrbpEmp().getName()) && orgDo1.getHrbpEmp().getName().contains(finalHrbpEmpParam))
            )).collect(Collectors.toList());
        }

        pageList(OrgDo.class, items, queryDto);
        return items.stream().distinct().collect(Collectors.toList());
    }

    public List<TreeData<OrgDo>> getTreeList(OrgPageQueryDto queryDto) {
        List<OrgDo> items = orgDo.getAllList(queryDto);
        // TODO: 2022/8/1 前端hrbp暂时只有选人组件
        Map<String, EmpSimple> hrbpMap = customOrgRoleDo.selectAllHrbpRole(queryDto.getDateTime())
                .stream().filter(cor -> null != cor.getLeaderEmp() && StringUtil.isNotEmpty(cor.getLeaderEmp().getEmpId()))
                .collect(Collectors.toMap(CustomOrgRoleDo::getOrgId, CustomOrgRoleDo::getLeaderEmp, (k, v) -> v));
        items.forEach(org -> org.setHrbpEmp(hrbpMap.get(org.getBid())));
        return treeList(OrgDo.class, items, queryDto);
    }

    public List<OrgDo> selectChildrenList(String bid, Long dataTime) {
        return orgDo.selectChildrenList(bid, dataTime);
    }

    public List<TreeData<LabelData>> fetchSimpleTree(Long dataTime, String schemaType) {
        List<OrgDo> items = orgDo.getEnableList(null, dataTime);
        OrgPageQueryDto query = new OrgPageQueryDto();
        query.setDateTime(dataTime);
        query.setSchemaType(schemaType);
        return treeList(LabelData.class, items, query);
    }

    public List<OrgDo> fetchAllOrgDo(List<String> orgCodeList, Long dataTime) {
        return orgDo.getEnableList(orgCodeList, dataTime);
    }

    public PageResult<OrgDo> getOrgPage(OrgOrPostQueryDto queryDto) {
        return orgDo.getOrgPage(queryDto);
    }

    public String getCostCenterNameById(String bid, Long dataTime) {
        CostCenterDo dbData = costCenterDo.selectById(bid, dataTime);

        return Optional.ofNullable(dbData)
                .map(CostCenterDo::getCostCenterName)
                .orElseThrow(() -> new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30039)));
    }

    public void updateStatus(OrgDo data, BusinessEventTypeEnum eventTypeEnum, Boolean updateChildren) {
        orgDo.updateStatus(data, eventTypeEnum, updateChildren);
    }

    private <T extends DataSimple> void pageList(Class<T> clazz, List<OrgDo> items, OrgPageQueryDto query) {
        if (null == items || items.isEmpty()) {
            return;
        }

        // schemaType 如果不为空，则进行汇报关系匹配
        if (StringUtil.isNotEmpty(query.getSchemaType())) {
            List<String> bids = new ArrayList<>();
            items.stream().forEach(item -> {
                PropertyValue pv = item.getProperties().get("reportExtend.bid");
                if (null != item.getUpdateSchema() && item.getUpdateSchema() && null != pv && pv instanceof SimplePropertyValue) {
                    List<String> values = ((SimplePropertyValue) pv).getArrayValues();
                    if (null != values && !values.isEmpty()) {
                        bids.addAll(values);
                    }
                }
            });

            List<OrgReportExtendDo> reportExtendDoList = bids.size() > 0 ? orgReportExtendDo.selectByQuery(bids, query) : null;
            // list 转 map
            Map<String, OrgReportExtendDo> reportExtendMap = null == reportExtendDoList ? new HashMap<>() :
                    reportExtendDoList.stream().collect(Collectors.toMap(OrgReportExtendDo::getBid, re -> re, (k1, k2) -> k1));

            items = items.stream().filter(item -> {
                if (null != item.getSchemaType()
                        && !StringUtil.isEmpty(item.getSchemaType().getValue())
                        && item.getSchemaType().getValue().equals(query.getSchemaType())) {
                    // 组织架构类型符合 schemaType，则直接筛选
                    return true;
                }

                PropertyValue pv = item.getProperties().get("reportExtend.bid");
                // 如果有汇报关系，则查找汇报关系中的 schemaType
                if (null != pv && pv instanceof SimplePropertyValue) {
                    List<String> values = ((SimplePropertyValue) pv).getArrayValues();
                    if (null != values && !values.isEmpty()) {
                        values.forEach(reBid -> {
                            OrgReportExtendDo reData = reportExtendMap.get(reBid);
                            if (null != reData && null != reData.getSchemaType()
                                    && !StringUtil.isEmpty(reData.getSchemaType().getValue())
                                    && reData.getSchemaType().getValue().equals(query.getSchemaType())) {
                                // 如果别名不为空，则替换组织简称和组织全称
                                item.setName(StringUtil.isEmpty(reData.getAlias()) ? item.getName() : reData.getAlias());
                                item.setFullName(StringUtil.isEmpty(reData.getAlias()) ? item.getFullName() : reData.getAlias());

                                // 更新组织类型
                                item.setOrgType(reData.getOrgType());

                                // 设置过滤筛选符合 schemaType 类型的组织 标志flg
                                item.getProperties().put("$flag", null);
                            }
                        });
                    }
                }

                return item.getProperties().containsKey("$flag");
            }).collect(Collectors.toList());
        }
    }

    private <T extends DataSimple> List<TreeData<T>> treeList(Class<T> clazz, List<OrgDo> items, OrgPageQueryDto query) {
        List<TreeData<T>> resultList = Lists.list();
        if (null == items || items.isEmpty()) {
            return resultList;
        }

        // schemaType 如果不为空，则进行汇报关系匹配
        if (StringUtil.isNotEmpty(query.getSchemaType())) {
            List<String> bids = new ArrayList<>();
            items.stream().forEach(item -> {
                item.setName(LangUtil.parseI18nValue(item.getName(), item.getI18nName()));
                item.setFullName(LangUtil.parseI18nValue(item.getFullName(), item.getI18nFullName()));
                PropertyValue pv = item.getProperties().get("reportExtend.bid");
                if (null != item.getUpdateSchema() && item.getUpdateSchema() && null != pv && pv instanceof SimplePropertyValue) {
                    List<String> values = ((SimplePropertyValue) pv).getArrayValues();
                    if (null != values && !values.isEmpty()) {
                        bids.addAll(values);
                    }
                }
            });

            List<OrgReportExtendDo> reportExtendDoList = bids.size() > 0 ? orgReportExtendDo.selectByQuery(bids, query) : null;
            // list 转 map
            Map<String, OrgReportExtendDo> reportExtendMap = null == reportExtendDoList ? new HashMap<>() :
                    reportExtendDoList.stream().collect(Collectors.toMap(OrgReportExtendDo::getBid, re -> re, (k1, k2) -> k1));

            items = items.stream().filter(item -> {
                if (null != item.getSchemaType()
                        && !StringUtil.isEmpty(item.getSchemaType().getValue())
                        && item.getSchemaType().getValue().equals(query.getSchemaType())) {
                    // 组织架构类型符合 schemaType，则直接筛选
                    return true;
                }

                PropertyValue pv = item.getProperties().get("reportExtend.bid");
                // 如果有汇报关系，则查找汇报关系中的 schemaType
                if (null != pv && pv instanceof SimplePropertyValue) {
                    List<String> values = ((SimplePropertyValue) pv).getArrayValues();
                    if (null != values && !values.isEmpty()) {
                        values.forEach(reBid -> {
                            OrgReportExtendDo reData = reportExtendMap.get(reBid);
                            if (null != reData && null != reData.getSchemaType()
                                    && !StringUtil.isEmpty(reData.getSchemaType().getValue())
                                    && reData.getSchemaType().getValue().equals(query.getSchemaType())) {
                                // 如果别名不为空，则替换组织简称和组织全称
                                item.setName(StringUtil.isEmpty(reData.getAlias()) ? item.getName() : reData.getAlias());
                                item.setFullName(StringUtil.isEmpty(reData.getAlias()) ? item.getFullName() : reData.getAlias());

                                // 更新组织类型
                                item.setOrgType(reData.getOrgType());

                                // 设置过滤筛选符合 schemaType 类型的组织 标志flg
                                item.getProperties().put("$flag", null);
                            }
                        });
                    }
                }

                return item.getProperties().containsKey("$flag");
            }).collect(Collectors.toList());
        }

        // list 转 map
        val treeDataMap = clazz.equals(LabelData.class) || clazz.getSuperclass().equals(LabelData.class) ?
                Maps.map(Sequences.sequence(items).map(data -> Pair.pair(data.getBid(), lableTreeData(data, clazz)))) :
                Maps.map(Sequences.sequence(items).map(data -> Pair.pair(data.getBid(), new TreeData(data))));

        for (Map.Entry<String, TreeData> dataEntry : treeDataMap.entrySet()) {
            DataSimple data = dataEntry.getValue().getData();
            String pid = data.fetchPid();
            if (null == pid || ROOT_PID.equals(pid)) {
                // 顶级根节点，直接添加
                resultList.add(dataEntry.getValue());
            } else if (null != treeDataMap.get(pid) && null != treeDataMap.get(pid).getChildren()) {
                // 如果找到关联的父节点，则添加 Children
                treeDataMap.get(pid).getChildren().add(dataEntry.getValue());
            } else {
                if (BooleanUtils.toBooleanDefaultIfNull(AuthScopeFilterUtil.get(), false)) {
                    resultList.add(dataEntry.getValue());
                }
            }

            if (clazz.equals(LabelData.class)) {
                // 如果是 LabelData 则不输出 properties
                data.getProperties().clear();
            }
        }

        return resultList;
    }

    private <T> TreeData lableTreeData(OrgDo data, Class<T> clazz) {
        LabelData labelData = (LabelData) ObjectConverter.convert(data, clazz);
        labelData.setLabel(data.getName());
        return new TreeData(labelData);
    }

    private void doCostInfo(OrgDo data) {
        if (StringUtil.isEmpty(data.getCostCenterId())) {
            return;
        }

        data.setCostCenterName(getCostCenterNameById(data.getCostCenterId(), data.getDataStartTime()));
    }

    private void doValidPid(OrgDo data) {
        if (null == data.getPid() || StringUtil.isEmpty(data.getPid().getPid())) {
            // 设置root 的 根节点 -1
            TreeParent parent = new TreeParent();
            parent.setPid(ROOT_PID);
            data.setPid(parent);
            return;
        }

        // 上级组织限制
        PreCheck.preCheckArgument(Objects.equals(data.getBid(), data.getPid().getPid()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30040));

        OrgDo dbOrg = selectById(data.getPid().getPid(), data.getDataStartTime());
        Optional.ofNullable(dbOrg).map(org -> org.getBid()).orElseThrow(() -> new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30041)));
    }

    private void doReportSchema(OrgDo data) {
        List<OrgReportExtendDo> list = ObjectConverter.convertList(data.getReportSchema(), OrgReportExtendDo.class);
        data.setReportSchema(list);
        if (null == list) {
            return;
        }

        list.stream().forEach(schemaDo -> {
            OrgDo newOrgDo = new OrgDo();
            newOrgDo.setDataStartTime(data.getDataStartTime());
            newOrgDo.setCostCenterId(schemaDo.getCostCenterId());
            doCostInfo(newOrgDo);
            schemaDo.setCostCenterName(newOrgDo.getCostCenterName());

            // 其他架构的责任人岗位
            newOrgDo.setLeaderPost(schemaDo.getLeaderPost());
            doLeaderOrgAndPost(newOrgDo);
            schemaDo.setLeaderPostTxt(newOrgDo.getLeaderPostTxt());

            if (StringUtil.isNotEmpty(schemaDo.getOrgId())) {
                OrgDo dbOrg = selectById(schemaDo.getOrgId(), data.getDataStartTime());
                Optional.ofNullable(dbOrg).map(org -> org.getBid()).orElseThrow(() -> new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30041)));
                schemaDo.setOrgName(dbOrg.getName());
            } else {
                // 设置root 的 根节点 -1
                schemaDo.setOrgId(ROOT_PID);
                schemaDo.setOrgName(null);
            }
        });
    }

    /**
     * 查询组织变动记录
     */
    public List<OrgDo> selectOrgChangeRecord(String bid) {
        return orgDo.selectOrgChangeRecord(bid, BaseConstant.TIMELINE_MIN_STARTTIME, BaseConstant.TIMELINE_MAX_ENDTIME);
    }

    /**
     * 查询组织变动记录
     */
    public List<OrgReportExtendDo> queryReportExtendChangeRecord(List<String> ids, Long dataTime) {
        return orgReportExtendDo.selectByIds(ids, dataTime);
    }

    /**
     * 包含租户公司节点的组织树
     */
    public <T extends LabelData> List<TreeData<T>> getCompanyTree(Long dataTime, String schemaType, StatusEnum status) {
        List<OrgDo> items = getOrgList(dataTime, status, null);
        if(CollectionUtils.isEmpty(items)) {
            return Lists.list();
        }
        OrgPageQueryDto query = new OrgPageQueryDto();
        query.setDateTime(dataTime);
        query.setSchemaType(schemaType);
        val tree = treeList(OrgLabelData.class, items, query);
        // 添加公司节点
        TreeData root = new TreeData();
        root.setChildren(tree);
        LabelData labelData = new LabelData();
        labelData.setBid(TENANT_ROOT_NODE_BID);
        labelData.setLabel(getTenantName());
        root.setData(labelData);
        return Lists.list(root);
    }

    private List<OrgDo> getOrgList(Long dataTime, StatusEnum status, String key) {
        var items = orgDo.getList(null, status, dataTime);
        if (CollectionUtils.isEmpty(items)) {
            return Lists.list();
        }
        Map<String, String> i18nMap = items.stream().filter(e -> StringUtils.isNotBlank(e.getBid()))
                .collect(Collectors.toMap(e -> e.getBid(), e -> LangUtil.parseI18nValue(e.getName(), e.getI18nName()), (v1, v2) -> v2));
        if (StringUtil.isNotBlank(key)) {
            items = items.stream().filter(e -> LangUtil.parseI18nValue(e.getName(), e.getI18nName()).contains(key)).collect(Collectors.toList());
        }
        for (OrgDo item : items) {
            if (Objects.isNull(item.getPid()) || StringUtil.isBlank(item.getPid().getPath())) {
                TreeParent treeParent = item.getPid() == null ? new TreeParent() : item.getPid();
                treeParent.setPName(LangUtil.parseI18nValue(item.getName(), item.getI18nName()));
                item.setPid(treeParent);
                continue;
            }
            item.getPid().setPName(Joiner.on("/").join(Splitter.on("/").splitToList(item.getPid().getPath()).stream().map(e -> i18nMap.getOrDefault(e, "")).collect(Collectors.toList())));
        }
        return items;
    }

    public List<OrgLabelData> getCompanyTreeByKey(Long dataTime, String key) {
        val items = getOrgList(dataTime, null, key);
        if (CollectionUtils.isEmpty(items)) {
            return Lists.list();
        }
        List<OrgLabelData> result = Lists.list();
        for (OrgDo item : items) {
            val convert = ObjectConverter.convert(item, OrgLabelData.class);
            convert.setLabel(item.getFullName());
            result.add(convert);
        }
        return result;
    }

    public String getTenantName() {
        String tenantInfoCacheKey = String.format(BaseConstant.TENANT_INFO_KEY, UserContext.getTenantId());
        String tenantInfo = cacheService.getValue(tenantInfoCacheKey);
        if (StringUtils.isNotBlank(tenantInfo)) {
            TenantConfigDto tenantConfigDto = FastjsonUtil.toObject(tenantInfo, TenantConfigDto.class);
            return tenantConfigDto.getName();
        }

        return UserContext.getTenantId();
    }

    public List<PostDo> getEnablePostByOrg(String bid, Long dateTime) {
        return postDo.getEnablePostByOrg(bid, dateTime);
    }

    public List<EmpWorkInfoDo> getEmpByOrganize(String bid, Long dateTime) {
        return empWorkInfoDo.getEmpByOrganize(bid, dateTime);
    }

    public List<OrgDo> selectAllByIds(List<String> orgIds, long dateTime) {
        return orgDo.selectAllByIds(orgIds, dateTime);
    }

    public List<OrgDo> selectByCostCenterCurrentTime(String costCenterId) {
        return orgDo.selectByCostCenter(costCenterId, System.currentTimeMillis());
    }

    public List<OrgDo> getOrgListByOrgCodes(List<String> codes, Long dateTime) {
        return orgDo.getOrgListByOrgCodes(codes, dateTime);
    }

    public List<OrgDo> getAllOrgByCode(List<String> codes) {
        return orgDo.getAllOrgListByCode(codes);
    }

    /**
     * 迪士尼用组织更新 不验证
     *
     * @param data
     * @param ext
     */
    public void onlyUpdate(OrgDo data, Map ext) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setTenantId(userInfo.getTenantId());
        data.setUpdateBy(userId);
        data.setUpdateTime(0 == data.getUpdateTime() ? System.currentTimeMillis() : data.getUpdateTime());

        List<OrgReportExtendDo> reportSchema = data.getReportSchema();
        List<String> targetIds = new ArrayList<>();
        if (null != reportSchema && !reportSchema.isEmpty()) {
            reportSchema.forEach(report -> {
                report.setTenantId(userInfo.getTenantId());
                if (StringUtil.isEmpty(report.getBid())) {
                    report.setCreateBy(userId);
                    report.setCreateTime(data.getUpdateTime());
                    report.setUpdateBy(userId);
                    report.setDataStartTime(data.getDataStartTime());
                    report.setBid(orgReportExtendDo.save(report));
                } else {
                    report.setUpdateBy(userId);
                    report.setDataStartTime(data.getDataStartTime());
                    report.setUpdateTime(data.getUpdateTime());
                    orgReportExtendDo.update(report);
                }

                targetIds.add(report.getBid());
            });
        }
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), ext, data);
        orgDo.update(data, targetIds);
        customOrgRoleDo.save(data.getBid(), data.getCustomOrgRoles(), data.getDataStartTime());
        if (Optional.ofNullable(data.getSyncUpdate()).orElse(false)) {
            updateChild(orgDo);
        }
    }

    /**
     * 迪士尼用 去除所有验证
     *
     * @param data
     * @param ext
     * @return
     */
    public String onlySave(OrgDo data, Map ext) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setTenantId(userInfo.getTenantId());
        data.setCreateBy(userId);
        data.setCreateTime(0 == data.getCreateTime() ? System.currentTimeMillis() : data.getCreateTime());
        data.setUpdateBy(userId);
        data.setUpdateTime(0 == data.getUpdateTime() ? data.getCreateTime() : data.getUpdateTime());

//        List<OrgReportExtendDo> reportSchema = data.getReportSchema();
        List<String> targetIds = new ArrayList<>();
//        if(null != reportSchema && !reportSchema.isEmpty()){
//            reportSchema.forEach(report -> {
//                report.setTenantId(userInfo.getTenantId());
//                report.setCreateBy(userId);
//                report.setCreateTime(data.getCreateTime());
//                report.setUpdateBy(userId);
//                report.setUpdateTime(data.getCreateTime());
//
//                report.setDataStartTime(data.getDataStartTime());
//                report.setBid(orgReportExtendDo.save(report));
//                targetIds.add(report.getBid());
//            });
//        }

        empExtFieldService.doCusExtProps(data.getDoIdentifier(), ext, data);
        String orgId = orgDo.save(data, targetIds);
        data.setBid(orgId);
//        customOrgRoleDo.save(orgId, data.getCustomOrgRoles(), data.getDataStartTime());
        return orgId;
    }


    public OrgDo getI18nOrg(String bid) {
        OrgDo orgDo = selectById(bid, System.currentTimeMillis());
        return Optional.ofNullable(orgDo).map(it -> {
            OrgDo i18nOrgDo = new OrgDo();
            i18nOrgDo.setBid(it.getBid());
            i18nOrgDo.setName(LangUtil.parseI18nValue(it.getName(), it.getI18nName()));
            i18nOrgDo.setFullName(LangUtil.parseI18nValue(it.getFullName(), it.getI18nFullName()));
            return i18nOrgDo;
        }).orElse(null);
    }

    public List<OrgDo> getBatchI18nOrg(List<String> bids) {
        return Optional.ofNullable(bids).map(it -> it.stream().map(this::getI18nOrg)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .orElse(Lists.list());
    }

    public List<OrgDo> getOrgByLeaderEmpId(String leadEmpId) {
        return orgDo.getOrgByLeaderEmpId(leadEmpId, System.currentTimeMillis());
    }

}
