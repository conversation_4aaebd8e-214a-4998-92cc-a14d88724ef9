package com.caidaocloud.hr.service.transfer.application.feign.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.transfer.application.feign.PayeignClient;
import com.caidaocloud.hr.service.transfer.application.feign.vo.PayFixItemByUpdateTimeVo;
import com.caidaocloud.web.Result;

import java.util.List;

/**
 */
public class PayFeignClientImpl implements PayeignClient {


    @Override
    public Result savePayFixInfoAss(List<PayFixItemByUpdateTimeVo> vos) {
        return Result.fail();
    }

    @Override
    public Result getHfPlicyList(BasePage dto) {
        return Result.fail();
    }

    @Override
    public Result getSocialSecurityPolicies(BasePage dto) {
        return Result.fail();
    }

//    @Override
//    public Result getPrivacyInfoList(Integer id) {
//        return Result.fail();
//    }
//
//    @Override
//    public Result getHfPolicy(Integer id) {
//        return Result.fail();
//    }
}
