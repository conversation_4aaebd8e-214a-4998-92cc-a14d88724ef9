package com.caidaocloud.hr.service.temination.application.dto.bcc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SysParamDictDto {
    @ApiModelProperty("字典id")
    private Long dictId;
    @ApiModelProperty("字段id")
    private Integer typeId;
    @ApiModelProperty("字典编码")
    private String dictCode;
    @ApiModelProperty("字典中文名称")
    private String dictChnName;
    @ApiModelProperty("字典英文名称")
    private String dictEngName;
    @ApiModelProperty("字典是否启用")
    private Boolean isaction;
    @ApiModelProperty("排序")
    private Short sortNum;
    @ApiModelProperty("多语言")
    private Object dictNameLang;
    @ApiModelProperty("是否为系统字段 true:是 false:否")
    private Boolean system;
}
