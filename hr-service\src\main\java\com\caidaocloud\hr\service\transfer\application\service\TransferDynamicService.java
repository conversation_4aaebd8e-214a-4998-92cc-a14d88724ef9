package com.caidaocloud.hr.service.transfer.application.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.common.application.dto.TemporaryDto;
import com.caidaocloud.hr.service.common.infrastructure.enums.ProcesStatusEnum;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.temination.application.feign.FormFeignClient;
import com.caidaocloud.hr.service.transfer.application.constant.TransferConstants;
import com.caidaocloud.hr.service.transfer.application.dto.TransferFormKey;
import com.caidaocloud.hr.service.transfer.domain.entity.ChangeDefDo;
import com.caidaocloud.hr.service.transfer.domain.entity.ChangeFieldDef;
import com.caidaocloud.hr.service.transfer.domain.enums.ChangeDefEnum;
import com.caidaocloud.hr.service.transfer.infrastructure.constant.TransferConstant;
import com.caidaocloud.hr.service.transfer.interfaces.dto.TransferQueryDto;
import com.caidaocloud.hr.service.transfer.interfaces.vo.*;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.ConvertValueUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.I18nUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicColumnConfigDto;
import com.caidaocloud.hrpaas.paas.common.dto.dynamic.DynamicPropertyDto;
import com.caidaocloud.hrpaas.paas.common.feign.DynamicFeignClient;
import com.caidaocloud.hrpaas.paas.common.service.DynamicService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhou
 * @date 2024/7/8
 */
@Service
@Slf4j
public class TransferDynamicService {
    @Resource
    private MetadataService metadataService;
    @Resource
    private TransferDefService transferDefService;
    @Resource
    private TransferService transferService;
    @Resource
    private TransferTypeService transferTypeService;
    @Resource
    private FormFeignClient formFeignClient;
    @Resource
    private DynamicFeignClient dynamicFeignClient;
    @Resource
    private DynamicService dynamicService;
    @Resource
    private ConvertValueUtil convertValueUtil;
    @Resource
    private ChangeDefDo changeDef;
    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    public static String dynamicCode(String transferBid) {
        return (TransferConstants.TRANSFER_DYNAMIC_CODE + "_" + transferBid).toUpperCase();
    }

    /**
     * 获取异动可用字段
     *
     * @param code 格式 TRANSFER_112345
     * @return
     */
    public List<MetadataVo> fetchTransferAvailableColumn(String code) {
        String typeId = StringUtils.substringAfterLast(code, "_");
        List<ChangeDefDo> configs = transferDefService.loadAllByTypeId(typeId);
        List<MetadataVo> list = new ArrayList<>();
        list.addAll(initEmpDynamicColumn());
        list.addAll(initFormDynamicColumn(configs));
        list.addAll(initBusinessDynamicColumn(configs));

        return list;
    }

    private List<MetadataVo> initBusinessDynamicColumn(List<ChangeDefDo> configs) {
        MetadataVo metadataVo = new MetadataVo();
        metadataVo.setIdentifier("Transfer");
        metadataVo.setName("业务字段");
        List<ChangeFieldDef> businessDefList = configs.stream()
                .flatMap(config -> TransferDefService.toFieldDefList(config).stream())
                .filter(ChangeFieldDef::isBusinessFiled)
                .flatMap(before -> {
                    before.setProperty(before.getProperty() + '_' + before.getType());
                    ChangeFieldDef after = ObjectConverter.convert(before, ChangeFieldDef.class);
                    before.setProperty(TransferConstant.OLD_MARK + before.getProperty());
                    before.setName(before.getName() + "（由）");
                    after.setName(after.getName() + "（至）");
                    return Lists.list(before, after).stream();
                })
                .distinct()
                .collect(Collectors.toList());
        businessDefList.add(new ChangeFieldDef("effectiveDate", "生效时间", PropertyDataType.Timestamp));
        businessDefList.add(new ChangeFieldDef("reason", "异动原因", PropertyDataType.Dict));
        businessDefList.add(new ChangeFieldDef("approvalStatus", "审批状态", PropertyDataType.String));
        metadataVo.setStandardProperties(Sequences.sequence(businessDefList).map(fieldDef -> {
            MetadataPropertyVo propertyVo = new MetadataPropertyVo();
            propertyVo.setProperty(fieldDef.getProperty());
            propertyVo.setName(fieldDef.getName());
            propertyVo.setDataType(fieldDef.getDataType());
            return propertyVo;
        }).toList());
        return Lists.list(metadataVo);
    }

    private List<MetadataVo> initFormDynamicColumn(List<ChangeDefDo> configs) {
        val metadataList = configs.stream().map(it ->
                it.getForm()
        ).filter(it -> StringUtils.isNotEmpty(it)).map(it -> {
            MetadataVo metadata = new MetadataVo();
            val formDef = formFeignClient.getFormDefById(it).getData();
            metadata.setIdentifier("entity.form." + formDef.getId());
            metadata.setName(formDef.getName());
            metadata.setCustomProperties(FastjsonUtil.convertList(formDef.getProperties(), MetadataPropertyVo.class));
            return metadata;
        }).collect(Collectors.toList());
        return metadataList;
    }

    private List<MetadataVo> initEmpDynamicColumn() {
        List<MetadataVo> list = new ArrayList<>();
        list.add(metadataService.getMetadata("entity.hr.EmpPrivateInfo"));
        list.add(metadataService.getMetadata("entity.hr.EmpWorkInfo"));
        list.add(metadataService.getMetadata("entity.hr.EmpWorkOverview"));
        list.add(metadataService.getMetadata("entity.hr.LastContract"));
        return list;
    }

    /**
     * 注册动态列默认配置
     *
     * @param transferName
     * @param transferBid
     */
    public void registerDynamicConfig(String transferName, String transferBid) {
        log.info("注册异动动态列配置开始，bid={},name={}", transferBid, transferName);
        DynamicColumnConfigDto dto = new DynamicColumnConfigDto();
        dto.setName(transferName);
        dto.setCode(dynamicCode(transferBid));
        dto.setProperties(defaultDynamicColumn());
        if (log.isDebugEnabled()) {
            log.debug("异动动态列配置,dto={}", dto);
        }
        dynamicFeignClient.dynamicTableSet(dto);
        log.info("注册异动动态列配置结束，bid={},name={}", transferBid, transferName);
    }

    private List<DynamicPropertyDto> defaultDynamicColumn() {
        return Lists.list(
                new DynamicPropertyDto("entity.hr.EmpWorkInfo", "enName", "英文名", "员工任职信息表", PropertyDataType.String),
                new DynamicPropertyDto("entity.hr.EmpWorkInfo", "hireDate", "入职日期", "员工任职信息表", PropertyDataType.Timestamp),
                new DynamicPropertyDto("entity.hr.EmpWorkInfo", "empType", "用工类型", "员工任职信息表", PropertyDataType.Dict),
                new DynamicPropertyDto("entity.hr.EmpWorkInfo", "organizeTxt", "所属组织", "员工任职信息表", PropertyDataType.String),
                new DynamicPropertyDto("entity.hr.EmpWorkInfo", "postTxt", "岗位", "员工任职信息表", PropertyDataType.String),
                new DynamicPropertyDto("entity.hr.EmpWorkInfo", "companyTxt", "合同公司", "员工任职信息表", PropertyDataType.String),
                new DynamicPropertyDto("Transfer", "effectiveDate", "生效时间", "业务字段", PropertyDataType.Timestamp),
                new DynamicPropertyDto("Transfer", "reason", "异动原因", "业务字段", PropertyDataType.Dict),
                new DynamicPropertyDto("Transfer", "approvalStatus", "审批状态", "业务字段", PropertyDataType.String),
                new DynamicPropertyDto("Transfer", "endDate", "结束日期", "业务字段", PropertyDataType.Timestamp),
                new DynamicPropertyDto("Transfer", "terminationDate", "终止日期", "业务字段", PropertyDataType.Timestamp)
        );
    }

    private Pair<List<Map<String, Object>>, Map<TransferFormKey, Map<String, Object>>> convertTransferDynamicData(PageResult<TransferListVo> pageApply, Map<String, List<MetadataPropertyDto>> empDynamicConfig) {
        Map<TransferFormKey, Map<String, Object>> formMap = new HashMap<>();
        List<Map<String, Object>> dataList = new ArrayList<>();

        Map<String, List<MetadataPropertyDto>> cacheMetadataPropList = Maps.map();
        cacheMetadataPropList.put("entity.hr.EmpWorkInfo", FastjsonUtil.convertList(metadataService.getMetadataProperty("entity.hr.EmpWorkInfo"), MetadataPropertyDto.class));
        cacheMetadataPropList.put("entity.hr.Contract", FastjsonUtil.convertList(metadataService.getMetadataProperty("entity.hr.Contract"), MetadataPropertyDto.class));
        cacheMetadataPropList.put("entity.hr.EmpSalaryChange", FastjsonUtil.convertList(metadataService.getMetadataProperty("entity.hr.EmpSalaryChange"), MetadataPropertyDto.class));
        for (TransferListVo item : pageApply.getItems()) {
            Map<String, Object> data = convertPropertyValue(dynamicService.loadDynamicData(empDynamicConfig, item.getEmp()
                    .getEmpId(), item.getCreateTime(), Maps.map("entity.hr.LastContract", "owner$empId")), empDynamicConfig);

            TransferApplyVo vo = transferService.detailVo(item.getId());
            for (TransferFieldVo field : vo.getData()) {
                data.put(field.dynamicKey() + "@" + "Transfer", fetchVal(true, field, empDynamicConfig,cacheMetadataPropList));
                data.put(TransferConstant.OLD_MARK + field.dynamicKey() + "@" + "Transfer", fetchVal(false, field, empDynamicConfig, cacheMetadataPropList));
            }

            if (StringUtils.isNotEmpty(item.getFormId()) && StringUtils.isNotEmpty(item.getFormValueId())) {
                formMap.put(new TransferFormKey(item.getFormId(), item.getFormValueId()), data);
            }
            data.putAll(FastjsonUtil.convertObject(item, Map.class));
            data.put("approvalStatus@Transfer", ProcesStatusEnum.valueOf(item.getApprovalStatus()).name);
            data.put("effectiveDate@Transfer", item.getEffectiveDate());
            if (item.getReason() != null)
                data.put("reason@Transfer", item.getReason().getText());
            dataList.add(data);
        }
        return Pair.pair(dataList, formMap);
    }

    /**
     * @param isAfter          true: after false:before
     * @param field
     * @param empDynamicConfig
     * @param cacheMetadataPropList
     * @return
     */
    private String fetchVal(boolean isAfter, TransferFieldVo field, Map<String, List<MetadataPropertyDto>> empDynamicConfig, Map<String, List<MetadataPropertyDto>> cacheMetadataPropList) {
        if ("other".equalsIgnoreCase(field.getType())) {
            return isAfter ? field.fetchAfterValue() : field.fetchBeforeValue();
        }
        ChangeDefEnum changeDef = ChangeDefEnum.valueOf(field.getType().toUpperCase());
        List<MetadataPropertyDto> metadataPropList = cacheMetadataPropList.get(changeDef.getIdentifier());
        if (!CollectionUtils.isEmpty(metadataPropList)) {
            for (MetadataPropertyDto meta : metadataPropList) {
                if (!meta.getProperty().equalsIgnoreCase(field.getProperty())) {
                    continue;
                }
                if (meta.getDataType().equals(PropertyDataType.Enum)) {
                    val enumDef = meta.getEnumDef().stream()
                            .filter(e -> e.getValue().equals(isAfter ? field.fetchAfterValue() : field.fetchBeforeValue())).findFirst().orElseGet(() -> null);
                    return Objects.nonNull(enumDef) ? I18nUtil.lang(enumDef.getI18nDisplay()) : "";
                }
            }
        }
        return isAfter ? field.fetchAfterValue() : field.fetchBeforeValue();
    }

    private Map<String, Object> convertPropertyValue(Map<String, Object> data, Map<String, List<MetadataPropertyDto>> empDynamicConfig) {
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, List<MetadataPropertyDto>> entry : empDynamicConfig.entrySet()) {
            String identifier = entry.getKey();
            for (MetadataPropertyDto propertyDto : entry.getValue()) {
                MetadataPropertyVo propertyVo = ObjectConverter.convert(propertyDto, MetadataPropertyVo.class);
                propertyVo.setProperty(propertyDto.getProperty() + "@" + identifier);
                PropertyValue value = ((PropertyValue) data.get(propertyVo.getProperty()));
                if (value != null) {
                    map.put(propertyVo.getProperty(), value.toText());
                }
            }
        }
        return map;
    }


    public PageResult<Map<String, Object>> page(TransferQueryDto dto, TemporaryDto... temporary) {
        PageResult<TransferListVo> pageApply = transferService.pageApply(dto);
        String code = TransferDynamicService.dynamicCode(dto.getTypeId());
        Map<String, List<MetadataPropertyDto>> empDynamicConfig = fetchEmpDynamicConfig(code);
        if (ArrayUtils.isNotEmpty(temporary)) {
            val typeIdList = pageApply.getItems().stream().map(e -> e.getTypeId()).filter(e -> StringUtils.isNotBlank(e)).collect(Collectors.toList());
            temporary[0].setTemporaryEntity(Pair.pair(empDynamicConfig, typeIdList));
        }
        Pair<List<Map<String, Object>>, Map<TransferFormKey, Map<String, Object>>> pair = convertTransferDynamicData(pageApply, empDynamicConfig);
        List<Map<String, Object>> dataList = pair.first();
        Map<TransferFormKey, Map<String, Object>> formMap = pair.second();
        convertFormData(pageApply, formMap);
        dataList.forEach(data -> {
            data.keySet().forEach(key -> {
                if (!"enabled".equals(postTxtShowCode)) {
                    if (key.contains("post") || key.contains("Post")) {
                        String postTxt = (String) data.get(key);
                        if (StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0) {
                            data.put(key, postTxt.substring(0, postTxt.lastIndexOf("(")));
                        }
                    }
                }
            });
        });

        return new PageResult<>(dataList, pageApply.getPageNo(), pageApply.getPageSize(), pageApply.getTotal());
    }

    public PageResult<TransferPortalVo> portalPage(TransferQueryDto dto) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        dto.setEmpId(String.valueOf(userInfo.getEmpId()));
        PageResult<TransferListVo> pageApply = transferService.pageApply(dto);
        List<TransferPortalVo> vos = ObjectConverter.convertList(pageApply.getItems(), TransferPortalVo.class);
        return new PageResult<>(vos, pageApply.getPageNo(), pageApply.getPageSize(), pageApply.getTotal());
    }


    private void convertFormData(PageResult<TransferListVo> pageApply, Map<TransferFormKey, Map<String, Object>> formMap) {
        Map<String, List<TransferFormKey>> formGroupMap = Sequences.sequence(pageApply.getItems())
                .filter(item -> StringUtils.isNotEmpty(item.getFormId()) && StringUtils.isNotEmpty(item.getFormValueId()))
                .map(item -> new TransferFormKey(item.getFormId(), item.getFormValueId()))
                .toMap(TransferFormKey::getFormId);
        formGroupMap.forEach((formId, formKeys) -> {
            List<String> formDataIds = Sequences.sequence(formKeys).map(TransferFormKey::getFormValueId).toList();
            val formDatas = DataQuery.identifier("entity.form." + formId).limit(formDataIds.size(), 1)
                    .filter(DataFilter.in("bid", formDataIds),
                            DataSimple.class, System.currentTimeMillis()).getItems();
            for (DataSimple formData : formDatas) {
                String formDataId = formData.getBid();
                val key = new TransferFormKey(formId, formDataId);
                formMap.computeIfPresent(key, (k, v) -> {
                    formData.getProperties().forEach((property, value) -> {
                        v.put(property + "@entity.form." + formId, Objects.isNull(value) ? "" : value.toText());
                    });
                    return v;
                });
            }
        });
    }

    @NotNull
    private Map<String, List<MetadataPropertyDto>> fetchEmpDynamicConfig(String code) {
        Map<String, List<MetadataPropertyDto>> dynamicConfig = dynamicService.dynamicTableConfigLoad(code);
        Map<String, List<MetadataPropertyDto>> empDynamicConfig = new HashMap<>();
        empDynamicConfig.compute("entity.hr.EmpPrivateInfo", (k, v) -> dynamicConfig.remove("entity.hr.EmpPrivateInfo"));
        empDynamicConfig.compute("entity.hr.EmpWorkInfo", (k, v) -> dynamicConfig.remove("entity.hr.EmpWorkInfo"));
        empDynamicConfig.compute("entity.hr.EmpWorkOverview", (k, v) -> dynamicConfig.remove("entity.hr.EmpWorkOverview"));
        empDynamicConfig.compute("entity.hr.LastContract", (k, v) -> dynamicConfig.remove("entity.hr.LastContract"));
        return empDynamicConfig;
    }

    public void init() {
        List<TransferTypeVo> list = transferTypeService.getTypeList();
        for (TransferTypeVo vo : list) {
            registerDynamicConfig(vo.getName(), vo.getBid());
        }
    }
}
