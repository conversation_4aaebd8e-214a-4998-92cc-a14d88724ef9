package com.caidaocloud.hr.service.organization.domain.jobgrade.entity;

import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.domain.jobgrade.repository.IJobGradeRepository;
import com.caidaocloud.hr.service.organization.interfaces.vo.jobgrade.JobGradeVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/11/30
 */
@Slf4j
@Data
@Service
public class JobGradeDo extends DataEntity {
    /**
     * 职级编码
     */
    private String jobGradeCode;

    /**
     * 职级名称
     */
    private String jobGradeName;

    /**
     * 职级名称多语言
     */
    private String i18nJobGradeName;

    /**
     * 职等
     */
    private Integer jobGradeLevel;

    /**
     * 职级通道ID
     */
    private String channelId;

    /**
     * 排序序号
     */
    private Integer sortNum;

    private final static String JOB_GRADE_IDENTIFIER = "entity.hr.JobGrade";

    @Resource
    private IJobGradeRepository jobGradeRepository;

    private void check(JobGradeDo data) {
        PreCheck.preCheckArgument(null == data || null == data.getBid(), "职级不存在");
    }

    /**
     * 新增
     *
     * @param data
     * @return
     */
    public String save(JobGradeDo data) {
        if (data.getSortNum() == null) {
            data.setSortNum(0);
        }
        initFieldValue(JOB_GRADE_IDENTIFIER, BusinessEventTypeEnum.CREATE, data, null);
        return jobGradeRepository.insert(data).getBid();
    }

    /**
     * 更新
     *
     * @param data
     */
    public void update(JobGradeDo data) {
        JobGradeDo dbData = selectById(data.getBid());
        initFieldValue(JOB_GRADE_IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        jobGradeRepository.updateById(data);
    }

    /**
     * 删除
     *
     * @param data
     */
    public void delete(JobGradeDo data) {
        data.setIdentifier(JOB_GRADE_IDENTIFIER);
        jobGradeRepository.delete(data);
    }

    /**
     * 更新状态
     *
     * @param data
     */
    public void updateStatus(JobGradeDo data, BusinessEventTypeEnum eventTypeEnum) {
        JobGradeDo dbData = selectById(data.getBid());
        check(dbData);
        initFieldValue(JOB_GRADE_IDENTIFIER, eventTypeEnum, data, dbData);
        jobGradeRepository.updateById(data);
    }

    /**
     * 查询单条数据
     *
     * @param bid
     * @return
     */
    public JobGradeDo selectById(String bid) {
        JobGradeDo gradeDo = jobGradeRepository.selectById(bid, JOB_GRADE_IDENTIFIER);
        PreCheck.preCheckArgument(null == gradeDo, "职级已不存在");
        return gradeDo;
    }

    /**
     * 列表
     *
     * @return
     */
    public List<JobGradeDo> selectList(String channelId, Integer status) {
        UserInfo userInfo = UserContext.preCheckUser();
        JobGradeDo data = new JobGradeDo();
        data.setIdentifier(JOB_GRADE_IDENTIFIER);
        data.setTenantId(userInfo.getTenantId());
        data.setChannelId(channelId);
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(null == status ? null : status.toString());
        data.setStatus(enumSimple);
        List<JobGradeDo> list = jobGradeRepository.selectList(data);
        list.sort(Comparator.comparing(e -> ObjectUtils.defaultIfNull(e.getJobGradeLevel() , 0)));
        return list;
    }

    /**
     * 查询职级通道下的已经设置的职等集合
     *
     * @param channelId
     * @return
     */
    public List<Integer> getJobGradeLevelList(String channelId) {
        List<JobGradeDo> channelGradeList = selectList(channelId, null);
        if (CollectionUtils.isNotEmpty(channelGradeList)) {
            return channelGradeList.stream().map(JobGradeDo::getJobGradeLevel).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public List<JobGradeDo> selectBatchIds(List<String> bids) {
        return jobGradeRepository.selectBatchIds(bids, JOB_GRADE_IDENTIFIER);
    }

    public void dragSort(List<JobGradeDo> dataList) {
        PreCheck.preCheckArgument(org.apache.commons.collections.CollectionUtils.isEmpty(dataList), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30075));
        List<String> bids = dataList.stream().map(JobGradeDo::getBid).collect(Collectors.toList());
        PreCheck.preCheckArgument(org.apache.commons.collections.CollectionUtils.isEmpty(bids), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30102));
        List<JobGradeDo> dbDataList = selectBatchIds(bids);
        PreCheck.preCheckArgument(org.apache.commons.collections.CollectionUtils.isEmpty(dbDataList), "请求数据不存在");

        Map<String, JobGradeDo> oldDataMap = dbDataList.stream().collect(Collectors.toMap(JobGradeDo::getBid, Function.identity(), (key1, key2) -> key2));
        Optional<Integer> optional = dbDataList.stream().filter(o -> o.getSortNum() != null).map(JobGradeDo::getSortNum).min(Integer::compare);

        int minSortNum = optional.orElse(0);
        int sortNum = 0;
        long currentTime = System.currentTimeMillis();
        dataList = dataList.stream().filter(o -> oldDataMap.containsKey(o.getBid())).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dataList)) {
            return;
        }
        for (JobGradeDo data : dataList) {
            JobGradeDo dbData = oldDataMap.get(data.getBid());
            dbData.setSortNum(minSortNum + sortNum);
            dbData.setUpdateBy(UserContext.getUserId());
            dbData.setUpdateTime(currentTime);
            jobGradeRepository.updateById(dbData);
            sortNum++;
        }
    }

    public List<JobGradeDo> selectAll(Integer status){
        return jobGradeRepository.selectAll(JOB_GRADE_IDENTIFIER, status);
    }

    public List<JobGradeDo> selectByCodes(List<String> codes) {
        return jobGradeRepository.selectByCodes(codes, JOB_GRADE_IDENTIFIER);
    }

    public static  void i18nConvert(JobGradeDo jobGradeDo,JobGradeVo jobGradeVo) {
        jobGradeVo.setI18nJobGradeName(StringUtil.isEmpty(jobGradeDo.getI18nJobGradeName()) ?
                ImmutableMap.of("default", Optional.ofNullable(jobGradeDo.getJobGradeName()).orElse("")) :
                FastjsonUtil.toObject(jobGradeDo.getI18nJobGradeName(), new TypeReference<Map<String, Object>>(){})
        );
    }
}
