package com.caidaocloud.hr.service.search.infrastructure.repository;

import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.zxp.esclientrhl.index.ElasticsearchIndex;
import org.zxp.esclientrhl.repository.ElasticsearchTemplate;
import org.zxp.esclientrhl.repository.PageList;
import org.zxp.esclientrhl.repository.PageSortHighLight;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Repository
public class EsDataRepository {
    @Resource
    private ElasticsearchIndex elasticsearchIndex;
    @Resource
    private RestHighLevelClient client;
    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;

    private Map<String, String> getDefaultSettings() {
        Map<String, String> setting = new HashMap<>();
        setting.put("index.number_of_shards", "3");
        setting.put("index.number_of_replicas", "1");
        return setting;
    }

    public void updateIndex(String mappingJson, String indexName) {
        // 索引分片的设置
        Map<String, String> setting = getDefaultSettings();
        updateIndex(setting, mappingJson, indexName);
    }

    public void updateDynamicIndex(String mappingJson, String indexName) {
        Map<String, String> setting = getDefaultSettings();
        setting.put("index.mapper.dynamic", "true");
        updateIndex(setting, mappingJson, indexName);
    }

    @SneakyThrows
    public void deleteIndexData(String indexName, String dataId) {
        DeleteRequest delRequest = new DeleteRequest(indexName, dataId);
        deleteIndexData(delRequest);
    }

    @SneakyThrows
    public void batchDeleIndex(String indexName, List<String> dataIds) {
        if (StringUtils.isBlank(indexName) || CollectionUtils.isEmpty(dataIds)) {
            return;
        }
        BulkRequest bulkRequest = new BulkRequest();
        for (String dataId : dataIds) {
            DeleteRequest deleteRequest = new DeleteRequest(indexName, dataId);
            bulkRequest.add(deleteRequest);
        }
        bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
        if (bulkResponse.hasFailures()) {
            System.out.println("批量删除操作中有失败项:");
            bulkResponse.forEach(item -> {
                if (item.isFailed()) {
                    log.info("id:  {} error message: {}", item.getId() , item.getFailureMessage());
                }
            });
        }
    }

    @SneakyThrows
    public void deleteIndexData(DeleteRequest dr) {
        dr.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        client.delete(dr, RequestOptions.DEFAULT);
    }

    @SneakyThrows
    public void deleteIndex(String indexName) {
        DeleteIndexRequest request = new DeleteIndexRequest(indexName);
        this.client.indices().delete(request, RequestOptions.DEFAULT);
    }

    public void defaultUpdateDynamicTemplateIndex(String mappingJson, String indexName) {
        Map<String, String> setting = getDefaultSettings();
        mappingJson = defaultDynamicTemplates(mappingJson);
        updateIndex(setting, mappingJson, indexName);
    }

    private String defaultDynamicTemplates(String mappingJson) {
        Map mappingMap = FastjsonUtil.toObject(mappingJson, Map.class);
        return createDynamicTemplate(mappingMap);
    }

    public String createDynamicTemplate(Map mappingMap) {
        mappingMap.put("numeric_detection", true);
        mappingMap.put("date_detection", false);
        mappingMap.put("dynamic_templates", defaultDynamicKeywords());
        return FastjsonUtil.toJson(mappingMap);
    }

    public String addPropsDynamicTemplate(Map propMap) {
        Map mappingMap = new HashMap();
        mappingMap.put("numeric_detection", false);
        mappingMap.put("date_detection", false);
        mappingMap.put("dynamic_templates", defaultDynamicKeywords());
        mappingMap.put("properties", propMap);
        return FastjsonUtil.toJson(mappingMap);
    }

    private List defaultDynamicKeywords() {
        String ddt = "[{\"long_as_keywords\":{\"match\":\"long\",\"mapping\":{\"type\":\"long\"}}},{\"strings_as_keywords\":{\"match_mapping_type\":\"string\",\"mapping\":{\"type\":\"keyword\"}}}]";
        return FastjsonUtil.toObject(ddt, List.class);
    }

    @SneakyThrows
    public void updateIndex(Map<String, String> settings, String mappingJson, String indexName) {
        if (existsIndex(indexName)) {
            return;
        }

        elasticsearchIndex.createIndex(settings, null, mappingJson, indexName);
    }

    @SneakyThrows
    public boolean existsIndex(String indexName) {
        GetIndexRequest request = new GetIndexRequest(indexName);
        return this.client.indices().exists(request, RequestOptions.DEFAULT);
    }

    public void saveData(String indexName, Map<String, Object> dataMap) {
        saveData(indexName, (String) dataMap.get("id"), dataMap);
    }

    public void saveData(String indexName, String dataId, Map<String, Object> dataMap) {
        // index 索引
        IndexRequest indexRequest = new IndexRequest(indexName);
        indexRequest.id(dataId);
        // 插入数据
        indexRequest.source(FastjsonUtil.toJson(dataMap), XContentType.JSON);
        // 手动将缓存区中的数据刷新到磁盘中，但是会影响性能
        indexRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        try {
            client.index(indexRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.info(".........es saveData fail. dataMap={}, errMsg={}",
                    FastjsonUtil.toJson(dataMap), e.getMessage(), e);
        }
    }

    public void updateData(String indexName, Map<String, Object> dataMap) {
        updateData(indexName, (String) dataMap.get("id"), dataMap);
    }

    @SneakyThrows
    public void updateData(String indexName, String dataId, Map<String, Object> dataMap) {
        // index 索引
        IndexRequest indexRequest = new IndexRequest(indexName);
        // 指明这条数据的id 是多少
        indexRequest.id(dataId);
        // 修改数据
        indexRequest.source(FastjsonUtil.toJson(dataMap), XContentType.JSON);
        indexRequest.opType(DocWriteRequest.OpType.INDEX);
        indexRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        IndexResponse response = client.index(indexRequest, RequestOptions.DEFAULT);
        log.info(".........updateData index={}, dataId={}, dataMap={}, response={}",
                indexName, dataId, FastjsonUtil.toJson(dataMap), FastjsonUtil.toJson(response));
    }

    @SneakyThrows
    public void updatePartDataByQuery(String indexName, String dataId, Map<String, Object> dataMap) {
        updatePartDataByQuery(indexName, dataId, dataMap, null, null);
    }

    @SneakyThrows
    public void updatePartDataByQuery(String indexName, String dataId,
                                      Map<String, Object> dataMap,
                                      Map<String, String> must) {
        updatePartDataByQuery(indexName, dataId, dataMap, must, null);
    }

    @SneakyThrows
    public void updatePartDataByQuery(String indexName, String dataId,
                                      Map<String, Object> dataMap,
                                      Map<String, String> must, Map<String, String> mustNot) {
        StringBuilder script = new StringBuilder();
        Set<String> keys = dataMap.keySet();
        for (String key : keys) {
            String appendValue = "";
            Object value = dataMap.get(key);
            if (value instanceof Number) {
                appendValue = value.toString();
            } else if (value instanceof String) {
                appendValue = "'" + value + "'";
            } else if (value instanceof List) {
                appendValue = FastjsonUtil.toJson(value);
            } else {
                appendValue = value.toString();
            }
            script.append("ctx._source.").append(key).append("=").append(appendValue).append(";");
        }
        UpdateByQueryRequest request = new UpdateByQueryRequest(indexName);
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("id", dataId));
        if (null != must && !must.isEmpty()) {
            must.forEach((prop, value) -> boolQuery.must(QueryBuilders.termQuery(prop, value)));
        }
        if (null != mustNot && !mustNot.isEmpty()) {
            mustNot.forEach((prop, value) -> boolQuery.mustNot(QueryBuilders.termQuery(prop, value)));
        }
        request.setQuery(boolQuery);
        request.setScript(new Script(script.toString()));
        // 实时刷盘
        request.setRefresh(true);
        client.updateByQuery(request, RequestOptions.DEFAULT);
    }

    @SneakyThrows
    public Map<String, Object> searchById(String indexName, String dataId) {
        if (StringUtil.isEmpty(dataId)) {
            return new HashMap<>();
        }
        GetRequest getRequest = new GetRequest(indexName, dataId);
        GetResponse getResponse = client.get(getRequest, RequestOptions.DEFAULT);
        return getResponse.getSourceAsMap();
    }

    @SneakyThrows
    public <T> T getById(String indexName, String dataId, Class<T> clazz) {
        GetRequest getRequest = new GetRequest(indexName, dataId);
        GetResponse getResponse = client.get(getRequest, RequestOptions.DEFAULT);
        return FastjsonUtil.toObject(getResponse.getSourceAsString(), clazz);
    }

    @SneakyThrows
    public <T> PageList<T> pageSearch(QueryBuilder query, PageSortHighLight pageParam, Class<T> clazz) {
        return elasticsearchTemplate.search(query, pageParam, clazz);
    }

    public SearchHits searchRequest(SearchRequest searchRequest) {
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            return response.getHits();
        } catch (ElasticsearchStatusException e) {
            log.error("es search status err,{}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("es search err,{}", e.getMessage(), e);
        }
        return null;
    }

    @SneakyThrows
    public SearchHits getByBid(String indexName, String bid) {
        SearchRequest searchRequest = new SearchRequest(indexName);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.termQuery("bid", bid));
        searchRequest.source(searchSourceBuilder);
        return searchRequest(searchRequest);
    }

    @SneakyThrows
    public Map<String, Object> getOne(String indexName, String bid) {
        SearchHits hits = getByBid(indexName, bid);
        if (null == hits) {
            return new HashMap();
        }

        SearchHit[] hitArr = hits.getHits();
        if (null == hitArr || hitArr.length == 0) {
            return new HashMap();
        }

        return hitArr[0].getSourceAsMap();
    }
}
