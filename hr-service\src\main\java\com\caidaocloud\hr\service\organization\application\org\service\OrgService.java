package com.caidaocloud.hr.service.organization.application.org.service;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.handler.inter.IWriter;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.core.feign.IDictFeignClient;
import com.caidaocloud.hr.service.common.infrastructure.utils.ObjectConvertUtil;
import com.caidaocloud.hr.service.common.infrastructure.utils.TreeDataUtil;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationRecordService;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationRecordSearchDto;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationRecordVo;
import com.caidaocloud.hr.service.dto.adapter.OrgInputDto;
import com.caidaocloud.hr.service.dto.emp.OrgReportExtendDto;
import com.caidaocloud.hr.service.dto.emp.RoleInfoDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.application.feign.oboarding.emp.EmpPrivateFeignClient;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpConcurrentPostDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.DictDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpConcurrentPostLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpEntryProcessDisableDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpEntryProcessDisableVo;
import com.caidaocloud.hr.service.organization.application.org.dto.*;
import com.caidaocloud.hr.service.organization.application.org.enums.DisAbleErrorEnum;
import com.caidaocloud.hr.service.organization.domain.org.entity.CustomOrgRoleDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgReportDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgExportDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgOrPostQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgPageQueryDto;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationStatus;
import com.caidaocloud.hr.service.transfer.application.service.TransferService;
import com.caidaocloud.hr.service.transfer.interfaces.dto.TransferQueryDto;
import com.caidaocloud.hr.service.transfer.interfaces.vo.TransferListVo;
import com.caidaocloud.hr.service.vo.adapter.OrgDataOutVo;
import com.caidaocloud.hr.service.vo.organization.company.org.CustomOrgRoleVo;
import com.caidaocloud.hr.service.vo.organization.company.org.OrgVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.util.AuthScopeFilterUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrgService {
    private final static String ORG_CENTER_IDENTIFIER = "entity.hr.Org",
            ADMINISTRATION_DICT_KEY = "ADMINISTRATION_DICT_%s_%s",
            ADMINISTRATION = "Administration",
            ARCHITECTURE_TYPE = "ArchitectureType", DICT_MODEL = "M_ORG", msg = "停用失败";
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private MetadataService metadataService;
    @Resource
    private CacheService cacheService;
    @Resource
    private IDictFeignClient dictFeignClient;
    @Resource
    private OrgCodeRuleService orgCodeRuleService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private OrganizeStructureService organizeStructureService;
    @Resource
    private OrgDo orgDo;
    @Resource
    private PostDomainService postDomainService;

    /**
     * 新增组织
     *
     * @param orgDo
     * @return
     */
    public String saveOrg(OrgDo orgDo, Map ext) {
        // 处理其他组织人员的所属组织
        doCustomOrgRole(orgDo);
        doOrgCode(orgDo);
        val orgId = orgDomainService.save(orgDo, ext);
        organizeStructureService.create(orgId, orgDo.getDataStartTime());
        LogRecordContext.putVariable("orgName", orgDo.getFullName());
        LogRecordContext.putVariable("condition", true);
        return orgId;
    }

    /**
     * 修改组织
     *
     * @param orgDo
     */
    public void updateOrg(OrgDo orgDo, Map ext) {
        // 处理其他组织角色，人员的所属组织
        doCustomOrgRole(orgDo);
        doOrgCode(orgDo);
        orgDomainService.update(orgDo, ext);
        organizeStructureService.update(orgDo.getBid(), orgDo.getDataStartTime());
        LogRecordContext.putVariable("orgName", orgDo.getFullName());
        LogRecordContext.putVariable("condition", true);
    }

    private void doOrgCode(OrgDo data) {
        if (null == data.getBid()) {
            // 处理新增时的组织编码
            data.setCode(orgCodeRuleService.nextOrgCode(data.getCode(), data.getCode()));
            return;
        }

        OrgDo original = getOrgById(data.getBid(), data.getDataStartTime());
        //别的服务用雪花设置bid 实际是新增
        if (original == null) {
            data.setCode(orgCodeRuleService.nextOrgCode(data.getCode(), data.getCode()));
            return;
        }
        if (StringUtils.isEmpty(data.getCode())) {
            // 如果编码为空，则使用 db 中的编码
            data.setCode(original.getCode());
        } else {
            // 如果编码不为空，则根据编码配置策略进行修改或不允许修改
            data.setCode(orgCodeRuleService.nextOrgCode(data.getCode(), original.getCode()));
        }
    }

    /**
     * 删除组织
     *
     * @param bid
     */
    public void deleteOrgById(String bid, Long dataTime) {
        // 查询直属下级
        List<OrgDo> childrenList = orgDomainService.selectChildrenList(bid, dataTime);
        if (CollectionUtils.isNotEmpty(childrenList)) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30038));
        }
        OrgDo orgDo = orgDomainService.selectById(bid, dataTime);
        orgDomainService.delete(bid, dataTime);
        organizeStructureService.delete(bid, dataTime);
        LogRecordContext.putVariable("orgName", orgDo.getFullName());
        LogRecordContext.putVariable("condition", true);
    }

    /**
     * 获取组织详情
     *
     * @param bid
     * @return
     */
    public OrgDo getOrgById(String bid, Long dataTime) {
        return orgDomainService.selectById(bid, dataTime);
    }

    public OrgVo getDetail(String bid, Long dataTime) {
        OrgDo data = getOrgById(bid, dataTime);
        return convertVo(data);
    }

    private OrgVo convertVo(OrgDo data) {
        return Optional.ofNullable(data)
                .map(orgDetail -> {
                    OrgVo orgVo = ObjectConvertUtil.convert(data, OrgVo.class, OrgDo::i18Convert);
                    orgVo.setReportSchema(ObjectConverter.convertList(data.getReportSchema(), OrgReportExtendDto.class));
                    if (null != orgDetail.getCustomOrgRoles() && !orgDetail.getCustomOrgRoles().isEmpty()) {
                        orgVo.setCustomOrgRoles(orgDetail.getCustomOrgRoles().stream().map(roleDo -> {
                            CustomOrgRoleVo roleVo = ObjectConverter.convert(roleDo, CustomOrgRoleVo.class);
                            roleVo.setRole(roleDo.getRole().getValue());
                            return roleVo;
                        }).collect(Collectors.toList()));
                    }
                    if (null != orgVo.getPid() && StringUtil.isEmpty(orgVo.getPid().getPName())) {
                        orgVo.setPid(null);
                    }
                    // 处理组织其他角色到 vo
                    doCustomOrgRoleVo(orgDetail, orgVo);
                    orgVo.setExt(empExtFieldService.getEmpCustomPropertyValue(orgDetail.getDoIdentifier(), orgDetail));
                    return orgVo;
                })
                .orElseThrow(() -> new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30044)));
    }

    public List<OrgReportDo> getOrgReportList() {
        List<OrgReportDo> response = new ArrayList<OrgReportDo>();
        List<OrgDo> orgDos = orgDomainService.selectOrgList();
        for (OrgDo data : orgDos
        ) {
            OrgReportDo reportDo = ObjectConverter.convert(data, OrgReportDo.class);
            //HRbp
            List<CustomOrgRoleDo> hrBps = data.getCustomOrgRoles();
            if (CollectionUtils.isNotEmpty(hrBps)) {
                if (hrBps.get(0).getLeaderEmp() != null) {
                    reportDo.setHrBpEmpName(hrBps.get(0).getLeaderEmp().getName());
                    reportDo.setHrBpWorkNo(hrBps.get(0).getLeaderEmp().getWorkno());
                }
            }
            //获取扩展数据
            Map ext = empExtFieldService.getEmpCustomPropertyValue(data.getDoIdentifier(), data);
            if (ext != null) {
                //AX
                Object axOrg = ext.get("axorg");
                if (axOrg != null) {
                    reportDo.setAxOrg(axOrg.toString());
                }
                //组织层级
                Object orgFunction = ext.get("Function");
                if (orgFunction != null) {
                    DictSimple dic = (DictSimple) ext.get("Function");
                    reportDo.setOrgFunction(dic.getText());
                }
            }
            //对六级组织赋值
            if (data.getPid() != null) {
                if (!StringUtils.isEmpty(data.getPid().getPath())) {
                    String[] split = data.getPid().getPath().split("/");
                    for (int i = 0; i < split.length; i++) {
                        OrgDo ThisBu = getInfo(orgDos, split[i]);
                        if (ThisBu != null) {
                            if (i == 0) {
                                reportDo.setBuGroupCode(ThisBu.getCode());
                                reportDo.setBuGroupName(ThisBu.getFullName());
                                continue;
                            }
                            if (i == 1) {
                                reportDo.setBuCode(ThisBu.getCode());
                                reportDo.setBuName(ThisBu.getFullName());
                                continue;
                            }
                            if (i == 2) {
                                reportDo.setDepCode(ThisBu.getCode());
                                reportDo.setDepName(ThisBu.getFullName());
                                continue;
                            }
                            if (i == 3) {
                                reportDo.setTeamCode(ThisBu.getCode());
                                reportDo.setTeamName(ThisBu.getFullName());
                                continue;
                            }
                            if (i == 4) {
                                reportDo.setSstCode(ThisBu.getCode());
                                reportDo.setSstName(ThisBu.getFullName());
                                continue;
                            }
                            if (i == 5) {
                                reportDo.setSquadCode(ThisBu.getCode());
                                reportDo.setBuGroupCode(ThisBu.getFullName());
                                continue;
                            }
                        }
                    }
                } else {
                    reportDo.setBuGroupCode(data.getCode());
                    reportDo.setBuGroupName(data.getFullName());
                }
            } else {
                reportDo.setBuGroupCode(data.getCode());
                reportDo.setBuGroupName(data.getFullName());
            }
            response.add(reportDo);
        }
        return response;
    }

    private void setOrg(OrgDo data) {

    }

    private OrgDo getInfo(List<OrgDo> AllOrgs, String thisBid) {
        Optional<OrgDo> ThisDetail = AllOrgs.stream().filter(p -> p.getBid().equals(thisBid)).findFirst();
        if (ThisDetail.isPresent()) {
            return ThisDetail.get();
        }
        return null;
    }

    private void doCustomOrgRoleVo(OrgDo orgDetail, OrgVo orgVo) {
        if (null == orgDetail.getCustomOrgRoles() || orgDetail.getCustomOrgRoles().isEmpty()) {
            return;
        }
        List<RoleInfoDto> list = new ArrayList<>();
        for (CustomOrgRoleDo roleDo : orgDetail.getCustomOrgRoles()) {
            RoleInfoDto dto = new RoleInfoDto();
            dto.setEmp(roleDo.getLeaderEmp());
            dto.setPost(roleDo.getLeaderPost());
            dto.setPostTxt(roleDo.getLeaderPostTxt());
            dto.setDictCode(roleDo.getRole().getCode());
            dto.setDictValue(roleDo.getRole().getValue());
            list.add(dto);
        }
        orgVo.setOtherRoles(list);
    }

    private void doCustomOrgRole(OrgDo data) {
        List<CustomOrgRoleDo> customOrgRoleDos = ObjectConverter.convertList(data.getCustomOrgRoles(), CustomOrgRoleDo.class);
        // TODO: 2022/3/18 人员信息查询失败
        if (customOrgRoleDos != null) {
            customOrgRoleDos.forEach(customOrgRoleDo -> {
                EmpSimple emp;
                if ((emp = customOrgRoleDo.getLeaderEmp()) != null) {
                    customOrgRoleDo.setEmpOrgTxt(emp.getDeptDesc());
                }
            });
            data.setCustomOrgRoles(customOrgRoleDos);
        }
    }

    /**
     * 获取组织列表
     */
    public List<OrgDo> getOrgList() {
        return orgDomainService.selectOrgList();
    }

    public List<OrgDo> getOrgListByLeader(EmpReportLeaderDto empReportLeaderDto, Long dateTime) {
        return orgDomainService.getOrgListByLeader(empReportLeaderDto, dateTime);
    }

    public List<OrgDo> getEnableOrgListByLeader(EmpReportLeaderDto empReportLeaderDto, Long dateTime) {
        return orgDomainService.getEnableOrgListByLeader(empReportLeaderDto, dateTime);
    }

    public List<OrgDo> getOrgListByEmpConcurrentPostLeader(EmpConcurrentPostLeaderDto empConcurrentPostLeaderDto, Long dateTime) {
        return orgDomainService.getOrgListByEmpConcurrentPostLeader(empConcurrentPostLeaderDto, dateTime);
    }

    public List<OrgDo> fetchAllOrgDo(OrgOrPostQueryDto queryDto) {
        return orgDomainService.fetchAllOrgDo(queryDto.getOrgCodeList(), System.currentTimeMillis());
    }

    public PageResult<OrgDo> getOrgPage(OrgOrPostQueryDto queryDto) {
        return orgDomainService.getOrgPage(queryDto);
    }


    /**
     * 组织启用或停用
     *
     * @param bid
     */
    public void enableOrDisable(String bid, Long dataTime) {
        orgDomainService.enableOrDisable(bid, dataTime);
    }

    /**
     * 组织树列表
     */
    public List<TreeData<OrgDo>> getTreeList(Long dataTime) {
        return orgDomainService.getTreeList(dataTime);
    }

    public List<OrgTreeData<Map>> getTreeMapList(Long dataTime) {
        List<TreeData<OrgDo>> treeList = this.getTreeList(dataTime);
        if (null == treeList || treeList.isEmpty()) {
            return new ArrayList<>();
        }
        List<OrgTreeData<Map>> treeMapList = convertTreeData(treeList);
        return treeMapList;
    }

    private void addExportOrg(List<OrgExportDto> list, OrgTreeData<Map> org) {
        val sdf = new SimpleDateFormat("yyyy-MM-dd");
        OrgExportDto exportDto = new OrgExportDto();
        exportDto.setCode((String) org.getData().get("code"));
        String path = StringUtils.trimToEmpty((String) org.getData().get("pid_fullpath_txt"));
        if (StringUtils.isNotEmpty(path)) {
            path = path + "/";
        }
        exportDto.setShortName(path + org.getData().get("name"));
        exportDto.setFullName(path + org.getData().get("fullName"));
        exportDto.setStatus((String) org.getData().get("status_txt"));
        val hrbp = (EmpSimple) org.getData().get("hrbpEmp");
        exportDto.setHrbp(null == hrbp ? "" : String.format("%s(%s)", hrbp.getName(), StringUtils.defaultString(hrbp.getWorkno())));
        exportDto.setEffectDate(sdf.format(new Date((Long) org.getData().get("dataStartTime"))));
        val leader = (EmpSimple) org.getData().get("leaderEmp");
        exportDto.setLeader(null == leader ? "" : String.format("%s(%s)", leader.getName(), StringUtils.defaultString(leader.getWorkno())));
        exportDto.setType((String) org.getData().get("orgType_txt"));
        list.add(exportDto);
        if (CollectionUtils.isNotEmpty(org.getChildren())) {
            for (OrgTreeData<Map> child : org.getChildren()) {
                addExportOrg(list, child);
            }
        }
    }

    @SneakyThrows
    public void export(OrgPageQueryDto queryDto, HttpServletResponse response) {
        List<OrgExportDto> result = Lists.newArrayList();
        List orgs = getPageList(queryDto);
        val sdf = new SimpleDateFormat("yyyy-MM-dd");
        for (Object org : orgs) {
            if (org instanceof Map) {
                OrgExportDto exportDto = new OrgExportDto();
                exportDto.setCode((String) ((Map) org).get("code"));
                String path = StringUtils.trimToEmpty((String) ((Map) org).get("pid_fullpath_txt"));
                if (StringUtils.isNotEmpty(path)) {
                    path = path + "/";
                }
                exportDto.setShortName(path + ((Map) org).get("name"));
                exportDto.setFullName(path + ((Map) org).get("fullName"));
                exportDto.setStatus((String) ((Map) org).get("status_txt"));

                val hrbp = (EmpSimple) ((Map) org).get("hrbpEmp");
                exportDto.setHrbp(null == hrbp ? "" : String.format("%s(%s)", hrbp.getName(), StringUtils.defaultString(hrbp.getWorkno())));
                val leader = (EmpSimple) ((Map) org).get("leaderEmp");
                exportDto.setLeader(null == leader ? "" : String.format("%s(%s)", leader.getName(), StringUtils.defaultString(leader.getWorkno())));
                exportDto.setEffectDate(sdf.format(new Date((Long) ((Map) org).get("dataStartTime"))));
                exportDto.setType((String) ((Map) org).get("orgType_txt"));
                result.add(exportDto);
            } else if (org instanceof OrgTreeData) {
                addExportOrg(result, (OrgTreeData<Map>) org);
            }
        }
        IWriter<Workbook> a = ExcelExportUtil.exportBigExcel(
                new ExportParams("组织", "组织"), OrgExportDto.class);
        val workbook = a.write(result).get();
        val os = response.getOutputStream();
        workbook.write(os);
        os.close();
        workbook.close();
    }

    public List getPageList(OrgPageQueryDto queryDto) {
        if (StringUtil.isEmpty(queryDto.getNameOrCode()) && ObjectUtil.isEmpty(queryDto.getFilters())) {
            return getTreeMapList(queryDto);
        }
        return searchPageList(queryDto);
    }

    private List searchPageList(OrgPageQueryDto queryDto) {
        List<OrgDo> dataList = orgDomainService.getPageList(queryDto);
        if (null == dataList || dataList.isEmpty()) {
            return dataList;
        }
        return dataList.stream().map(orgData -> orgData.toMap()).collect(Collectors.toList());
    }

    public List<OrgTreeData<Map>> getTreeMapList(OrgPageQueryDto queryDto) {
//        List<OrgTreeData<Map>> result = new ArrayList<>();
        List<OrgTreeData<Map>> noFilterList = getTreeMapList(queryDto, false);
        List<OrgTreeData<Map>> filterList = getTreeMapList(queryDto, true);
        TreeDataUtil.handleIsHiden(noFilterList, filterList);
//        if (StringUtils.isEmpty(queryDto.getNameOrCode())) {
//            return noFilterList;
//        }
//        // 组织编码/组织简称  模糊
//        // 组织类型、负责人姓名/工号、HRBP姓名/工号  等于
//        for (OrgTreeData<Map> mapOrgTreeData : noFilterList) {
//            if (checkOrgTreeData(mapOrgTreeData, queryDto, 0) > 0) {
//                result.add(mapOrgTreeData);
//            }
//        }
//        return result;
        return noFilterList;
    }

    /**
     * 判断符合条件数据
     *
     * @param noFilterList
     * @param queryDto
     * @param num
     * @return
     */
    private Integer checkOrgTreeData(OrgTreeData<Map> noFilterList, OrgPageQueryDto queryDto, Integer num) {

        Map data = noFilterList.getData();
        //组织简称
        String name = (String) data.get("name");
        //组织编码
        String code = (String) data.get("code");

        if (StringUtils.isNotEmpty(queryDto.getNameOrCode())) {
            if (StringUtils.isNotEmpty(name) && name.contains(queryDto.getNameOrCode())) {
                num++;
            }
            if (StringUtils.isNotEmpty(code) && name.contains(queryDto.getNameOrCode())) {
                num++;
            }
        }

        if (CollectionUtils.isNotEmpty(noFilterList.getChildren())) {
            for (OrgTreeData<Map> child : noFilterList.getChildren()) {
                num = checkOrgTreeData(child, queryDto, num);
            }
        }

        return num;
    }


    private List<OrgTreeData<Map>> getTreeMapList(OrgPageQueryDto queryDto, boolean isFilter) {
        try {
            AuthScopeFilterUtil.put(isFilter);
            List<TreeData<OrgDo>> treeList = orgDomainService.getTreeList(queryDto);
            if (null == treeList || treeList.isEmpty()) {
                return new ArrayList<>();
            }
            List<OrgTreeData<Map>> treeMapList = convertTreeData(treeList);
            return treeMapList;
        } finally {
            AuthScopeFilterUtil.remove();
        }
    }

    private List<OrgTreeData<Map>> convertTreeData(List<TreeData<OrgDo>> children) {
        if (null == children || children.isEmpty()) {
            return new ArrayList<>();
        }

        List<OrgTreeData<Map>> childList = children.stream().map(tree -> {
            OrgTreeData<Map> newData = new OrgTreeData<>();
            OrgDo orgData = tree.getData();
            if (null != orgData) {
                newData.setData(orgData.toMap());

                newData.setChildren(convertTreeData(tree.getChildren()));
            }

            return newData;
        }).collect(Collectors.toList());
        return childList;
    }

    public List<TreeData<LabelDataDto>> fetchSimpleTree(Long dataTime, String schemaType) {
        List<TreeData<LabelData>> noFilterList = fetchSimpleTree(dataTime, schemaType, false);
        List<TreeData<LabelData>> filterList = fetchSimpleTree(dataTime, schemaType, true);
        List<TreeData<LabelDataDto>> noFilter = convertLabelDataDto(noFilterList);
        List<TreeData<LabelDataDto>> filter = convertLabelDataDto(filterList);
        TreeDataUtil.handleIsHiden(noFilter, filter);
        return noFilter;
    }

    private List<TreeData<LabelDataDto>> convertLabelDataDto(List<TreeData<LabelData>> list) {
        List<TreeData<LabelDataDto>> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (TreeData<LabelData> labelDataTreeData : list) {
            var data = labelDataTreeData.getData();
            var labelDataDto = new LabelDataDto();
            if (data != null) {
                BeanUtil.copyProperties(data, labelDataDto);
            }
            var treeData = new TreeData<LabelDataDto>();
            treeData.setData(labelDataDto);
            result.add(treeData);
            if (CollectionUtils.isNotEmpty(labelDataTreeData.getChildren())) {
                var children = convertLabelDataDto(labelDataTreeData.getChildren());
                treeData.setChildren(children);
            }
        }
        return result;
    }

    public List<TreeData<LabelData>> fetchSimpleTree(Long dataTime, String schemaType, boolean isFilter) {
        try {
            AuthScopeFilterUtil.put(isFilter);
            // 默认查询行政架构
            schemaType = doSchemaType(schemaType);
            return orgDomainService.fetchSimpleTree(dataTime, schemaType);
        } finally {
            AuthScopeFilterUtil.remove();
        }
    }


    private String doSchemaType(String schemaType) {
        if (StringUtil.isNotEmpty(schemaType)) {
            return schemaType;
        }

        String schemaTypeKey = String.format(ADMINISTRATION_DICT_KEY, UserContext.getTenantId(), ADMINISTRATION);
        schemaType = cacheService.getValue(schemaTypeKey);
        if (StringUtil.isNotEmpty(schemaType)) {
            return schemaType;
        }

        Result result = dictFeignClient.getEnableDictList(ARCHITECTURE_TYPE, DICT_MODEL);
        if (null == result || !result.isSuccess()) {
            return schemaType;
        }

        Object data = result.getData();
        List<DictDto> list = FastjsonUtil.toArrayList(FastjsonUtil.toJson(data), DictDto.class);
        for (DictDto dict : list) {
            if (ADMINISTRATION.equals(dict.getCode())) {
                cacheService.cacheValue(schemaTypeKey, dict.getValue(), -1);
                return dict.getValue();
            }
        }

        return schemaType;
    }

    public List<TreeData<OrgLabelData>> getCompanyTree(Long dataTime, String schemaType, StatusEnum status) {
        // 默认查询行政架构
        schemaType = doSchemaType(schemaType);
        return orgDomainService.getCompanyTree(dataTime, schemaType, status);
    }

    public List<OrgLabelData> getCompanyTreeByKey(Long dataTime, String key) {
        return orgDomainService.getCompanyTreeByKey(dataTime, key);
    }

    public <T extends LabelData> List<TreeData<T>> getFilterOrg(Long dataTime, String schemaType) {
        // 默认查询行政架构
        schemaType = doSchemaType(schemaType);
        return orgDomainService.getCompanyTree(dataTime, schemaType, StatusEnum.ENABLED);
    }

    /**
     * 获取组织模型元数据字段列表
     */
    public List<MetadataPropertyVo> getOrgMetadataProperty() {
        MetadataVo metadata = metadataService.getMetadata(ORG_CENTER_IDENTIFIER);
        List<MetadataPropertyVo> allProperties = metadataService.getMetadataProperty(metadata);

        List<String> displayStandardProperties = new ArrayList<>(Arrays.asList("name", "fullName", "code", "orgType", "schemaType"));
        // todo displayStandardProperties 将来要从列表配置中读取
        allProperties = allProperties.stream().filter(o -> displayStandardProperties.contains(o.getProperty())).collect(Collectors.toList());
        if (null != metadata.getCustomProperties()) {
            allProperties.addAll(metadata.getCustomProperties());
        }

        if (null != metadata.getInheritedCustomProperties()) {
            allProperties.addAll(metadata.getInheritedCustomProperties());
        }

        return allProperties;
    }

    public void enable(OrgDo data, Boolean updateChildren) {
        orgDomainService.updateStatus(data, BusinessEventTypeEnum.ENABLE, updateChildren);
        organizeStructureService.update(data.getBid(), data.getDataStartTime());
        if (null != updateChildren && updateChildren) {
            val list = orgDo.selectAllChildrenList(data.getBid(), data.getDataStartTime());
            list.forEach(it -> {
                organizeStructureService.update(it.getBid(), data.getDataStartTime());
            });
        }
    }

    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private TransferService transferService;
    @Resource
    private ConfirmationRecordService confirmationRecordService;
    @Resource
    private EmpPrivateFeignClient empPrivateFeignClient;


    public Result disable(OrgDo data, Long dateTime) {
        //1787 在职员工 兼岗 岗位 异动流程（至此组织） 转正流程（至此组织）
        // 入职流程中（指未进入员工信息）的候选人组织 审批通过的未来生效的异动/转正异动（含异动/转正异动后的组织至“该组织”）
        //多条数据默认取 6条数据；

        //1.在职员工校验
        Result<DisAbleErrorDto> empError = checkEmpWorkInfo(data, dateTime);
        if (empError != null) return empError;

        //2.有效兼岗校验；
        Result<DisAbleErrorDto> concurrentError = checkEmpConcurrentPost(data, dateTime);
        if (concurrentError != null) return concurrentError;

        //3.有效岗位校验；
        Result<DisAbleErrorDto> postError = checkPost(data, dateTime);
        if (postError != null) return postError;

        //4. 异动流程（至此组织）
        Result<DisAbleErrorDto> tranaferInProcessError = checkTranaferInProcess(data);
        if (tranaferInProcessError != null) return tranaferInProcessError;

        //5. 转正流程（至此组织）
        Result<DisAbleErrorDto> confirmationInProcessError = checkConfirmationInProcess(data);
        if (confirmationInProcessError != null) return confirmationInProcessError;

        //6. 入职流程中（指未进入员工信息）的候选人组织
        Result<DisAbleErrorDto> empEntityProcessError = checkEmpEntityProcess(data);
        if (empEntityProcessError != null) return empEntityProcessError;

        //7. 审批通过的未来生效的异动（含异动/转正异动后的组织至“该组织”）
        Result<DisAbleErrorDto> tranaferApproveError = checkTranaferApprove(data, System.currentTimeMillis());
        if (tranaferApproveError != null) return tranaferApproveError;

        //8. 审批通过的未来生效的转正异动（含转正异动后的组织至“该组织”）
        Result<DisAbleErrorDto> confirmationApproveError = checkConfirmationApprove(data, System.currentTimeMillis());
        if (confirmationApproveError != null) return confirmationApproveError;


        orgDomainService.updateStatus(data, BusinessEventTypeEnum.DISABLE, false);
        organizeStructureService.update(data.getBid(), data.getDataStartTime());

        return Result.ok(true);
    }

    @Nullable
    private Result<DisAbleErrorDto> checkConfirmationApprove(OrgDo data, Long dateTime) {
        ConfirmationRecordSearchDto searchDto = new ConfirmationRecordSearchDto();
        searchDto.setPageNo(1);
        searchDto.setPageSize(6);
        searchDto.setApprovalStatus("APPROVE");
        searchDto.setNewOrganize(data.getBid());
        searchDto.setConfirmationFutureDate(String.valueOf(dateTime));
        PageResult<ConfirmationRecordVo> page = confirmationRecordService.page(searchDto);
        if (!page.getItems().isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 转正数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            for (ConfirmationRecordVo item : page.getItems()) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getConfirmationDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.CONFIRMATION_APPROVE.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.CONFIRMATION_APPROVE.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkTranaferApprove(OrgDo data, Long dateTime) {
        TransferQueryDto dto = new TransferQueryDto();
        //审批通过
        dto.setApprovalStatus(TerminationStatus.APPROVE.value);
        //默认获取6条
        dto.setPageNo(1);
        dto.setPageSize(6);
        dto.setShowRevoke(false);
        dto.setOrganizeEnable(true);
        dto.setNewOrgId(data.getBid());
        dto.setEffectiveFutureDate(String.valueOf(dateTime));
        PageResult<TransferListVo> pageResult = transferService.pageApply(dto);
        List<TransferListVo> items = pageResult.getItems();

        if (!items.isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 异动审批数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            //生效日期 选取 effectiveDate
            for (TransferListVo item : items) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getEffectiveDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.TRANAFER_APPROVE.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.TRANAFER_APPROVE.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkEmpEntityProcess(OrgDo data) {
        EmpEntryProcessDisableDto dto = new EmpEntryProcessDisableDto();
        dto.setOrganize(data.getBid());
        dto.setPageNo(1);
        dto.setPageSize(6);
        Result<PageResult<EmpEntryProcessDisableVo>> disableEmpEntryProcessPage = empPrivateFeignClient.getDisableEmpEntryProcessPage(dto);
        List<EmpEntryProcessDisableVo> items = disableEmpEntryProcessPage.getData().getItems();
        if (!items.isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 转正数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            for (EmpEntryProcessDisableVo item : items) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getHireDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.EMP_ENTRY_PROCESS.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.EMP_ENTRY_PROCESS.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkConfirmationInProcess(OrgDo data) {
        ConfirmationRecordSearchDto searchDto = new ConfirmationRecordSearchDto();
        searchDto.setPageNo(1);
        searchDto.setPageSize(6);
        searchDto.setApprovalStatus("IN_PROCESS");
        searchDto.setNewOrganize(data.getBid());
        PageResult<ConfirmationRecordVo> page = confirmationRecordService.page(searchDto);
        if (!page.getItems().isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 转正数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            for (ConfirmationRecordVo item : page.getItems()) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getConfirmationDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.CONFIRMATION_IN_PROCESS.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.CONFIRMATION_IN_PROCESS.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkTranaferInProcess(OrgDo data) {
        TransferQueryDto dto = new TransferQueryDto();
        //审批中
        dto.setApprovalStatus(TerminationStatus.IN_PROCESS.value);
        //默认获取6条
        dto.setPageNo(1);
        dto.setPageSize(6);
        dto.setShowRevoke(false);
        dto.setOrganizeEnable(true);
        dto.setNewOrgId(data.getBid());
        PageResult<TransferListVo> pageResult = transferService.pageApply(dto);
        List<TransferListVo> items = pageResult.getItems();

        if (!items.isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 异动审批数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            //生效日期 选取 effectiveDate
            for (TransferListVo item : items) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getEffectiveDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.TRANAFER_IN_PROCESS.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.TRANAFER_IN_PROCESS.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkPost(OrgDo data, Long dateTime) {
        // 组织被引用后是不可以被停用
        // 3.存在岗位，则不能停用
        // 1787 并提示 生效岗位；
        List<PostDo> enablePostByOrg = orgDomainService.getEnablePostByOrg(data.getBid(), dateTime);
        if (enablePostByOrg.size() > 0) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接生效岗位
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            for (PostDo postDo : enablePostByOrg) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(postDo.getName());
                commonErrorDto.setDateStartTime(String.valueOf(postDo.getDataStartTime()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.POST.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.POST.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkEmpConcurrentPost(OrgDo data, Long dateTime) {
        //2. 兼岗
        List<EmpConcurrentPostDo> concurrentPostDos = getEnableConcurrentByOrg(data.getBid(), dateTime);
        if (!concurrentPostDos.isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接在职员工
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            List<String> concurrentEmpIds
                    = concurrentPostDos.stream().map(empConcurrentPostDo -> empConcurrentPostDo.getEmpId()).collect(Collectors.toList());
            Map<String, EmpWorkInfoDo> empIdToEmp = new HashMap<>();
            if (CollectionUtils.isNotEmpty(concurrentEmpIds)) {
                List<EmpWorkInfoDo> empListByEmpIds = empWorkInfoDomainService.getEmpListByEmpIds(concurrentEmpIds, dateTime);
                empIdToEmp = empListByEmpIds.stream().collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, empWorkInfoDo -> empWorkInfoDo));
            }
            //拼接兼岗数据
            for (EmpConcurrentPostDo concurrentPostDo : concurrentPostDos) {
                String empId = concurrentPostDo.getEmpId();
                if (StringUtil.isNotEmpty(empId)) {
                    if (empIdToEmp.containsKey(empId)) {
                        DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                        EmpWorkInfoDo workInfoDo = empIdToEmp.get(empId);
                        if (null == workInfoDo.getName() && null == workInfoDo.getWorkno()) {
                            //问题数据不展示；
                            continue;
                        }
                        commonErrorDto.setName(workInfoDo.getName());
                        commonErrorDto.setWorkNo(workInfoDo.getWorkno());
                        commonErrorDto.setDateStartTime(String.valueOf(concurrentPostDo.getStartDate()));
                        commonErrorDto.setDateEndTime(String.valueOf(concurrentPostDo.getEndDate()));
                        commonErrorDtos.add(commonErrorDto);
                    }
                }
            }
            errorDto.setTypeName(DisAbleErrorEnum.EMP_CONCURRENT_POST.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.EMP_CONCURRENT_POST.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkEmpWorkInfo(OrgDo data, Long dateTime) {
        // 1.存在员工，则不能停用
        //1787 展示 在职员工
        List<EmpWorkInfoDo> emp = getEmpByOrganize(data.getBid(), dateTime);
        if (emp.size() > 0) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接在职员工
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            for (EmpWorkInfoDo empWorkInfoDo : emp) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(empWorkInfoDo.getName());
                commonErrorDto.setWorkNo(empWorkInfoDo.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(empWorkInfoDo.getDataStartTime()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.EMP_WORK_INFO.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.EMP_WORK_INFO.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    private List<EmpWorkInfoDo> getEmpByOrganize(String bid, Long dateTime) {
        return orgDomainService.getEmpByOrganize(bid, dateTime);
    }

    @Resource
    private EmpConcurrentPostDomainService empConcurrentPostDomainService;

    /**
     * 兼岗生效中  的 对应人；
     */
    public List<EmpConcurrentPostDo> getEnableConcurrentByOrg(String orgBid, Long dateTime) {
        // 兼岗
        return empConcurrentPostDomainService.getEnableConcurrentByOrg(orgBid, dateTime);
    }

    /**
     * 获取组织变动记录
     */
    public List<OrgDo> selectOrgChangeRecord(String bid) {
        return orgDomainService.selectOrgChangeRecord(bid);
    }

    /**
     * 获取组织变动记录
     */
    public List queryReportExtendChangeRecord(String recordIds, Long dataTime) {
        if (StringUtil.isEmpty(recordIds)) {
            return Lists.newArrayList();
        }

        List<String> ids = Lists.newArrayList(recordIds.split(","));
        if (ids.isEmpty()) {
            return Lists.newArrayList();
        }

        return orgDomainService.queryReportExtendChangeRecord(ids, dataTime);
    }

    public List<OrgDo> getOrgListByOrgCodes(List<String> codes, Long dateTime) {
        return orgDomainService.getOrgListByOrgCodes(codes, dateTime);
    }

    public List<OrgDo> getAllOrgByCodes(List<String> codes) {
        return orgDomainService.getAllOrgByCode(codes);
    }

    public List<OrgDo> selectByIds(List<String> bids, Long dataTime) {
        return orgDomainService.selectAllByIds(bids, dataTime);
    }

    public Map<String, OrgDo> selectOrgMapByIds(List<String> bids, Long dataTime) {
        return orgDomainService.selectAllByIds(bids, dataTime).stream().collect(Collectors.toMap(OrgDo::getBid, obj -> obj, (A, B) -> A));
    }

    public String saveOrUpdateOrg(OrgInputDto dto) {
        OrgDto orgDto = ObjectConverter.convert(dto, OrgDto.class);
        OrgDo orgDo = ObjectConverter.convert(dto, OrgDo.class);
        long now = DateUtil.getCurrentTimestamp();
        orgDto.setDataStartTime(now);
        initUpdateData(orgDo, now);

        //上级组织
        TreeParent parent = new TreeParent();
        parent.setPid(dto.getPid());
        parent.setPath(dto.getPid());
        parent.setPName(dto.getPidTxt());
        parent.setNamePath(dto.getPidTxt());
        orgDto.setPid(parent);
        orgDo.setPid(parent);

        DictSimple schemaTypeSimple = new DictSimple();
        //组织类型
        if (StringUtils.isNotEmpty(dto.getOrgType())) {
            schemaTypeSimple.setValue(dto.getOrgType());
        } else {
            Result result = dictFeignClient.getEnableDictList(ARCHITECTURE_TYPE, DICT_MODEL);

            Object data = result.getData();
            List<DictDto> list = FastjsonUtil.toArrayList(FastjsonUtil.toJson(data), DictDto.class);
            for (DictDto dict : list) {
                if (ADMINISTRATION.equals(dict.getCode())) {
                    schemaTypeSimple.setValue(dict.getValue());
                }
            }
        }
        orgDto.setSchemaType(schemaTypeSimple);
        orgDo.setSchemaType(schemaTypeSimple);

        //去他组织角色
        if (orgDo.getCustomOrgRoles() == null) {
            orgDo.setCustomOrgRoles(new ArrayList<>());
        }

        if (StringUtils.isNotEmpty(orgDo.getBid())) {
            OrgDo orgById = getOrgById(dto.getBid(), DateUtil.getCurrentTimestamp());
            if (orgById != null && orgById.getBid() != null) {
                if (CollectionUtils.isEmpty(orgDto.getCustomOrgRoles())) {
                    orgDto.setCustomOrgRoles(null);
                }
                BeanUtil.copyProperties(orgDto, orgById, CopyOptions.create().ignoreNullValue().ignoreError());
                onlyUpdateOrg(orgById, null);
            } else {
                onlySaveOrg(orgDo, null);
            }
        } else {
            onlySaveOrg(orgDo, null);
        }

        return orgDo.getBid();
    }

    public void onlyUpdateOrg(OrgDo orgDo, Map ext) {
        // 处理其他组织角色，人员的所属组织
        doCustomOrgRole(orgDo);
        doOrgCode(orgDo);
        orgDomainService.onlyUpdate(orgDo, ext);
    }

    public String onlySaveOrg(OrgDo orgDo, Map ext) {
        // 处理其他组织人员的所属组织
        doCustomOrgRole(orgDo);
        doOrgCode(orgDo);
        return orgDomainService.onlySave(orgDo, ext);
    }

    private void initUpdateData(OrgDo orgDo, Long now) {
        orgDo.setDataStartTime(now);
        orgDo.setDataEndTime(253402185600000L);
        orgDo.setUpdateTime(now);
    }

    public List<OrgDataOutVo> selectAllData() {
        BasePage page = new BasePage();
        int pageNo = 1;
        int pageSize = 500;
        int total = pageSize;
        List<OrgDo> list = new ArrayList<>();
        boolean flag = true;
        while (flag) {
            page.setPageNo(pageNo);
            page.setPageSize(pageSize);
            PageResult<OrgDo> temp = orgDomainService.selectPage(page);
            list.addAll(temp.getItems());
            if (temp.getTotal() <= total) {
                flag = false;
            }
            total += pageSize;
            ++pageNo;
        }
        List<OrgDataOutVo> vos = new ArrayList<>();
        List<String> strings = list.stream().filter(st -> st.getLeaderPost() != null).map(OrgDo::getLeaderPost).distinct().collect(Collectors.toList());
        Map<String, String> postIdCodeMap = postDomainService.selectByIds(strings, DateUtil.getCurrentTimestamp()).stream().collect(Collectors.toMap(PostDo::getBid, PostDo::getCode));

        for (OrgDo data : list) {
            OrgDataOutVo convert = ObjectConverter.convert(data, OrgDataOutVo.class);
            if (data.getLeaderPost() != null) {
                convert.setLeaderEmpPost(postIdCodeMap.get(data.getLeaderPost()));
            }
            vos.add(convert);
        }
        return vos;
    }

    public List<OrgDo> selectRangeByBid(String bid) {
        return orgDo.selectRangeByBid(bid);
    }

    public List<OrgVo> getOrgDetailListByBid(String bid) {
        List<OrgDo> dataList = selectRangeByBid(bid);
        if (null == dataList || dataList.isEmpty()) {
            return new ArrayList<>();
        }

        return dataList.stream().map(this::convertVo).collect(Collectors.toList());
    }
}
