package com.caidaocloud.hr.service.organization.infrastructure.repository.po;

import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/12/3
 */
@Data
public class PostPo extends DataEntity {

    /**
     * 岗位名称
     */
    private String name;

    /**
     * 岗位名称多语言
     */
    private String i18nName;

    /**
     * 岗位编码
     */
    private String code;

    /**
     * 所属组织ID
     */
    private String orgId;

    /**
     * 所属组织名称
     */
    private String orgName;

    /**
     * 工作地ID
     */
    private String workplaceId;

    /**
     * 工作地名称
     */
    private String workplaceName;

    /**
     * 是否关键岗位
     */
    private Boolean keyPost;

    /**
     * 职级关联
     */
    private JobGradeRange jobGrade;

    /**
     * 关联职务或基准岗位，0：未配置，1：职务，2：基准岗位
     */
    private Integer relation;

    /**
     * 关联基准岗位ID
     */
    private String benchmarkPositionId;

    /**
     * 关联基准岗位名称
     */
    private String benchmarkPositionName;

    /**
     * 关联的职务ID
     */
    private String jobId;

    /**
     * 关联的职务名称
     */
    private String jobName;

    /**
     * 岗位说明
     */
    private String desc;

    /**
     * 任职资格
     */
    private String qualification;

    /**
     * 是否更新该岗位下的员工工作地信息
     */
    private Boolean syncEmpPost;

    /**
     * 附件
     */
    private Attachment files;

    /**
     * 岗位说明书
     */
    private Attachment jobDescFiles;

}
