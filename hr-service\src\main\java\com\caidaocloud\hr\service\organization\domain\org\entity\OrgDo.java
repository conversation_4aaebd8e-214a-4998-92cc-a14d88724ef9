package com.caidaocloud.hr.service.organization.domain.org.entity;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpConcurrentPostLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.organization.application.org.dto.OrgDataValueDto;
import com.caidaocloud.hr.service.organization.domain.org.repository.IOrgRepository;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgOrPostQueryDto;
import com.caidaocloud.hr.service.vo.organization.company.org.OrgVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataValueFunction;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.util.DataValueUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.RequestHolder;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant.HR_ERROR_CODE_32006;
import static com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant.HR_ERROR_CODE_32007;
import static com.caidaocloud.hr.service.organization.application.org.enums.OrganizationalRole.HRBP;

/**
 * 组织
 */
@Slf4j
@Data
@Service
public class OrgDo extends DataEntity {
    public final static String ORG_IDENTIFIER = "entity.hr.Org";

    /**
     * 组织全称
     */
    private String fullName;

    /**
     * 组织全称多语言
     */
    private String i18nFullName;

    /**
     * 组织简称
     */
    private String name;

    /**
     * 组织简称多语言
     */
    private String i18nName;

    /**
     * 组织编码
     */
    private String code;
    /**
     * 组织编码全称多语言
     */
    private String i18nCode;

    /**
     * 架构类型
     */
    private DictSimple schemaType;

    /**
     * 上级组织
     */
    private TreeParent pid;

    /**
     * 组织类型
     */
    private DictSimple orgType;

    /**
     * 虚拟组织
     */
    private Boolean virtual;

    /**
     * 成本中心ID
     */
    private String costCenterId;

    /**
     * 成本中心名称
     */
    private String costCenterName;

    /**
     * 是否同步更新子组织[其他组织角色]
     * 同步更新子组织
     */
    private Boolean syncUpdate;

    /**
     * 是否维护其他架构类型汇报关系
     */
    private Boolean updateSchema;

    /**
     * 其他架构类型汇报关系
     */
    private List<OrgReportExtendDo> reportSchema;

    /**
     * 其他架构类型汇报关系
     */
    private List<CustomOrgRoleDo> customOrgRoles;

    /**
     * 责任人
     */
    private EmpSimple leaderEmp;

    /**
     * 责任人岗位id
     */
    private String leaderPost;

    /**
     * 责任人岗位名称
     */
    private String leaderPostTxt;

    /**
     * 责任人组织ID
     */
    private String leaderOrganize;

    /**
     * 责任人组织名称
     */
    private String leaderOrganizeTxt;

    /**
     * 20220801
     * hrbp {@link CustomOrgRoleDo}
     */
    private EmpSimple hrbpEmp;

    @Resource
    private IOrgRepository orgRepository;

    @Resource
    private ApplicationContext applicationContext;

    public String getDoIdentifier() {
        return ORG_IDENTIFIER;
    }

    /**
     * 新增
     *
     * @param data
     * @return
     */
    public String save(OrgDo data) {
        // 验证
        data.checkReportSchema();

        UserInfo userInfo = RequestHolder.getUserInfo();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        data.setTenantId(userInfo.getTenantId());
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setIdentifier(ORG_IDENTIFIER);
        data.setDeleted(Boolean.FALSE);
        return orgRepository.insert(data).getBid();
    }

    public void checkReportSchema() {
        if (null == updateSchema || !updateSchema) {
            return;
        }

        PreCheck.preCheckArgument(null == reportSchema || reportSchema.isEmpty(), "其他架构类型汇报关系不允许为空");
        boolean checkFail = reportSchema.stream().filter(schema -> null == schema.getSchemaType() || StringUtil.isEmpty(schema.getSchemaType().getValue())).findAny().isPresent();
        PreCheck.preCheckArgument(checkFail, "其他架构类型汇报关系(架构类型)不允许为空");

        checkFail = reportSchema.stream().filter(schema -> schema.getSchemaType().getValue().equals(schemaType.getValue()))
                .findAny().isPresent();
        PreCheck.preCheckArgument(checkFail, "其他架构类型汇报关系(架构类型)与当前组织架构类型存在重复");

        checkFail = reportSchema.stream().map(schema -> schema.getSchemaType().getValue()).distinct().count() != reportSchema.size();
        PreCheck.preCheckArgument(checkFail, "其他架构类型汇报关系(架构类型)存在重复");
    }

    public void update(OrgDo data) {
        OrgDo dbData = selectById(data.getBid(), data.getDataStartTime());
        DataEntity.initFieldValue(ORG_IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        orgRepository.updateById(data);
    }

    /**
     * 更新
     *
     * @param data
     */
    public void updateWithNullValue(OrgDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = Optional.ofNullable(userInfo).map(user -> user.getUserId()).map(String::valueOf).orElse(null);
        OrgDo dbData = orgRepository.selectById(data.getId(), ORG_IDENTIFIER, data.getDataStartTime());

        // update field
        dbData.setUpdateBy(userId);
        dbData.setUpdateTime(System.currentTimeMillis());
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(null == dbData.getStatus() ? StatusEnum.ENABLED.getIndex().toString() : dbData.getStatus().toString());
        dbData.setStatus(enumSimple);

        dbData.setName(data.getName());
        dbData.setFullName(data.getFullName());
        dbData.setCode(data.getCode());
        dbData.setPid(data.getPid());
        dbData.setOrgType(data.getOrgType());
        dbData.setVirtual(data.getVirtual());
        dbData.setStatus(data.getStatus());

        dbData.setCostCenterId(data.getCostCenterId());
        dbData.setSchemaType(data.getSchemaType());
        dbData.setReportSchema(data.getReportSchema());
        dbData.setSyncUpdate(data.getSyncUpdate());
        dbData.setDataStartTime(data.getDataStartTime());
        orgRepository.updateById(dbData);
    }

    /**
     * 删除
     *
     * @param bid
     */
    public void delete(String bid, Long dataStartTime) {
        OrgDo data = new OrgDo();
        data.setBid(bid);
        data.setIdentifier(ORG_IDENTIFIER);
        data.setDataStartTime(dataStartTime);
        orgRepository.delete(data);
    }

    /**
     * 查询单条数据
     */
    public OrgDo selectById(String bid, Long dataTime) {
        return orgRepository.selectById(bid, ORG_IDENTIFIER, dataTime);
    }

    /**
     * 分页
     *
     * @param page
     * @return
     */
    public PageResult<OrgDo> selectPage(BasePage page) {
        UserInfo userInfo = UserContext.preCheckUser();
        OrgDo data = new OrgDo();
        data.setIdentifier(ORG_IDENTIFIER);
        data.setTenantId(userInfo.getTenantId());
        return orgRepository.selectPage(page, data);
    }

    /**
     * 列表
     *
     * @return
     */
    public List<OrgDo> selectList() {
        UserInfo userInfo = UserContext.preCheckUser();
        OrgDo data = new OrgDo();
        data.setIdentifier(ORG_IDENTIFIER);
        data.setTenantId(userInfo.getTenantId());
        return orgRepository.selectList(data);
    }

    public List<OrgDo> getOrgListByLeader(EmpReportLeaderDto empReportLeaderDto, Long dateTime) {
        return orgRepository.getOrgListByLeader(ORG_IDENTIFIER, empReportLeaderDto, dateTime);
    }

    public List<OrgDo> getEnableOrgListByLeader(EmpReportLeaderDto empReportLeaderDto, Long dateTime) {
        return orgRepository.getEnableOrgListByLeader(ORG_IDENTIFIER, empReportLeaderDto, dateTime);
    }

    public List<OrgDo> getOrgListByEmpConcurrentPostLeader(EmpConcurrentPostLeaderDto empConcurrentPostLeaderDto, Long dateTime) {
        return orgRepository.getOrgListByEmpConcurrentPostLeader(ORG_IDENTIFIER, empConcurrentPostLeaderDto, dateTime);
    }

    /**
     * 组织启用或停用
     */
    public void enableOrDisable(String bid, Long dataTime) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        OrgDo dbData = orgRepository.selectById(bid, ORG_IDENTIFIER, dataTime);
        PreCheck.preCheckArgument(null == dbData || null == dbData.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30085));
        // update field
        dbData.setUpdateBy(userId);
        dbData.setUpdateTime(System.currentTimeMillis());
        dbData.setDataStartTime(dataTime);

        EnumSimple enumSimple = new EnumSimple();
        if (StatusEnum.ENABLED.getIndex().equals(dbData.getStatus())) {
            enumSimple.setValue(StatusEnum.DEACTIVATED.getIndex().toString());
        } else {
            enumSimple.setValue(StatusEnum.ENABLED.getIndex().toString());
        }

        dbData.setStatus(enumSimple);
        orgRepository.updateById(dbData);
    }

    /**
     * 查询组织树列表
     *
     * @return
     */
    public List<TreeData<OrgDo>> tree(Long dataTime) {
        return orgRepository.getTreeList(ORG_IDENTIFIER, dataTime, null);
    }

    /**
     * 查询组织树列表
     *
     * @return
     */
    public List<TreeData<OrgDo>> tree(Long dataTime, String schemaType) {
        return orgRepository.getTreeList(ORG_IDENTIFIER, dataTime, schemaType);
    }

    /**
     * 查询直属下级
     *
     * @param bid
     * @return
     */
    public List<OrgDo> selectChildrenList(String bid, Long dataTime) {
        UserInfo userInfo = UserContext.preCheckUser();
        return orgRepository.selectChildrenList(bid, ORG_IDENTIFIER, userInfo.getTenantId(), dataTime);
    }

    /**
     * 查询树列表（简单信息）
     *
     * @return
     */
    public List<TreeData<LabelData>> simpleTree(Long dataTime, String schemaType) {
        return orgRepository.getTreeSimpleList(ORG_IDENTIFIER, dataTime, schemaType);
    }

    public Map toMap() {
        Map orgMap = new HashMap();

        orgMap.put("name", this.getName());
        // 组织全称
        orgMap.put("fullName", this.getFullName());
        orgMap.put("status", this.getStatus());
        orgMap.put("virtual", this.getVirtual());
        orgMap.put("orgType", this.getOrgType());
        orgMap.put("pid", this.getPid());
        orgMap.put("code", this.getCode());
        orgMap.put("bid", this.getBid());
        orgMap.put("costCenterId", this.getCostCenterId());
        orgMap.put("reportSchema", this.getReportSchema());
        orgMap.put("schemaType", this.getSchemaType());
        orgMap.put("syncUpdate", this.getSyncUpdate());
        orgMap.put("updateSchema", this.getUpdateSchema());
        orgMap.put("dataStartTime", this.getDataStartTime());
        orgMap.put("hrbpEmp", this.getHrbpEmp());
        // 列表展示负责人（岗位）名称
        orgMap.put("leaderEmp", this.getLeaderEmp());
        orgMap.put("leaderPost", this.getLeaderPost());
        orgMap.put("leaderPostTxt", this.getLeaderPostTxt());
        orgMap.put("leaderOrganize", this.getLeaderOrganize());
        orgMap.put("leaderOrganizeTxt", this.getLeaderOrganizeTxt());

        NestPropertyValue npv = this.getProperties();
        Map<String, Map> joinValues = new HashMap<>();
        List<DataValueFunction<Object>> dataVals = new ArrayList(npv.size());
        npv.forEach((dField, dataVal) -> {
            OrgDataValueDto<Object> dataValue = new OrgDataValueDto();
            dataValue.setProp(dField);

            // 组装数据
            DataValueUtil.loadDataValue(dataValue, dataVal, joinValues, dataVals);

            dataVals.add(dataValue);
        });

        if (!joinValues.isEmpty()) {
            joinValues.forEach((joinKey, joinVal) -> {
                OrgDataValueDto dataValue = new OrgDataValueDto();
                dataValue.setProp(joinKey);
                dataValue.setValue(joinVal);
                dataVals.add(dataValue);
            });
        }

        dataVals.forEach(dataVal -> {
            if (!orgMap.containsKey(dataVal.loadDataProp())) {
                orgMap.put(dataVal.loadDataProp(), dataVal.loadDataValue());
            }
        });

        return orgMap;
    }

    /**
     * 更新状态
     */
    public void updateStatus(OrgDo data, BusinessEventTypeEnum eventTypeEnum, Boolean updateChildren) {
        if (eventTypeEnum != BusinessEventTypeEnum.DISABLE && eventTypeEnum != BusinessEventTypeEnum.ENABLE) {
            return;
        }

        OrgDo dbData = selectById(data.getBid(), data.getDataStartTime());
        PreCheck.preCheckArgument(null == data || null == data.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30085));
        DataEntity.initFieldValue(ORG_IDENTIFIER, eventTypeEnum, data, dbData);

        // 校验
        if (eventTypeEnum == BusinessEventTypeEnum.DISABLE) {
            // 停用：检查是否存在已启用的下级，如果存在则不允许停用
            List<OrgDo> childrenList = selectChildrenList(data.getDataStartTime(), data.getBid(), StatusEnum.ENABLED);
            PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(childrenList), "被停用的组织下有启用中的组织，请先停用下级组织");
            orgRepository.updateById(data);
            return;
        }

        // 启用：通过参数判断是否同时启用下级（所有下级），下级数据启用的生效日期和启用的父级生效日期保持一致
        orgRepository.updateById(data);
        if (updateChildren == null || !updateChildren) {
            return;
        }

        List<OrgDo> childrenList = selectAllChildrenList(data.getBid(), data.getDataStartTime());
        if (null == childrenList || childrenList.isEmpty()) {
            return;
        }

        childrenList.forEach(children -> {
            children.setUpdateBy(data.getUpdateBy());
            children.setUpdateTime(data.getUpdateTime());
            children.setStatus(data.getStatus());
            children.setDataStartTime(data.getDataStartTime());
            orgRepository.updateById(children);
        });
    }

    /**
     * 新增
     */
    public String save(OrgDo data, List<String> targetIds) {
        data.setIdentifier(ORG_IDENTIFIER);
        data.setDeleted(Boolean.FALSE);
        DataEntity.initFieldValue(ORG_IDENTIFIER, BusinessEventTypeEnum.CREATE, data, null);
        return orgRepository.insert(data, targetIds).getBid();
    }

    public void update(OrgDo data, List<String> targetIds) {
        OrgDo dbData = selectById(data.getBid(), data.getDataStartTime());
        DataEntity.initFieldValue(ORG_IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        data.setReportSchema(null);
        orgRepository.updateById(data, targetIds);

        if (!Objects.equals(dbData.getName(), data.getName())) {
            /*applicationContext.publishEvent(new OrgChangeEvent(data.getBid(), data.getBid(),
                    data.getName(), dbData.getTenantId(), data.getDataStartTime()));*/
        }
    }

    /**
     * 查询单条数据
     */
    public OrgDo queryRelatedProperties(String bid, Long dataTime) {
        return orgRepository.queryRelatedProperties(bid, ORG_IDENTIFIER, dataTime);
    }

    /**
     * 查询已启用的组织树信息
     */
    public List<OrgDo> getEnableList(List<String> orgCodeList, Long dataTime) {
        return orgRepository.getAllList(ORG_IDENTIFIER, dataTime, StatusEnum.ENABLED, orgCodeList);
    }

    public List<OrgDo> getList(List<String> orgCodeList, StatusEnum status, Long dataTime) {
        return orgRepository.getAllList(ORG_IDENTIFIER, dataTime, status, orgCodeList);
    }

    public PageResult<OrgDo> getOrgPage(OrgOrPostQueryDto queryDto) {
        return orgRepository.getOrgPage(ORG_IDENTIFIER, queryDto);
    }

    /**
     * 查询所有组织树信息
     */
    public List<OrgDo> getAllList(BasePage basePage) {
        return orgRepository.getAllList(ORG_IDENTIFIER, basePage, null);
    }

    /**
     * 查询直属下级
     */
    public List<OrgDo> selectChildrenList(Long dateTime, String bid, StatusEnum statusEnum) {
        return orgRepository.selectChildrenList(dateTime, bid, ORG_IDENTIFIER, UserContext.getTenantId(), statusEnum.getIndex());
    }

    /**
     * 查询所有下级
     */
    public List<OrgDo> selectAllChildrenList(String bid, Long dateTime) {
        return orgRepository.selectAllChildrenList(ORG_IDENTIFIER, bid, dateTime);
    }

    /**
     * 查询组织变动记录
     */
    public List<OrgDo> selectOrgChangeRecord(String bid, long startTime, long endTime) {
        return orgRepository.selectOrgChangeRecord(ORG_IDENTIFIER, bid, startTime, endTime);
    }

    public CustomOrgRoleDo getHrbp() {
        if (null == customOrgRoles || customOrgRoles.isEmpty()) {
            return null;
        }

        // 获取该租户HRBP字典对应的value
        DictSimple dictHrbp = DictSimple.code2Dict(HRBP.getCode());
        String dictValue = dictHrbp.getValue();
        if (StringUtil.isEmpty(dictValue)) {
            return null;
        }

        for (CustomOrgRoleDo cus : customOrgRoles) {
            if (null != cus.getRole() && dictValue.equals(cus.getRole().getValue())) {
                return cus;
            }
        }

        return null;
    }

    public void checkOrgName(OrgDo data) {
        List<OrgDo> orgList = orgRepository.selectDuplicateOrgName(ORG_IDENTIFIER, data);
        if (orgList.isEmpty()) {
            return;
        }
        OrgDo dbOrg = orgList.get(0);
        PreCheck.preCheckArgument(dbOrg.getName().equals(data.getName()), LangUtil.getMsg(HR_ERROR_CODE_32007));
        PreCheck.preCheckArgument(dbOrg.getFullName().equals(data.getFullName()), LangUtil.getMsg(HR_ERROR_CODE_32006));
        log.warn("find org with different name");
    }

    public List<OrgDo> selectAllByIds(List<String> orgIds, long dateTime) {
        return orgRepository.selectAllByIds(ORG_IDENTIFIER, orgIds, dateTime);
    }

    public List<OrgDo> selectByCostCenter(String costCenterId, long dateTime) {
        return orgRepository.selectByCostCenter(ORG_IDENTIFIER, costCenterId, dateTime);
    }

    public List<OrgDo> getOrgListByOrgCodes(List<String> codes, Long dateTime) {
        return orgRepository.getOrgListByOrgCodes(ORG_IDENTIFIER, codes, dateTime);
    }

    public List<OrgDo> getAllOrgListByCode(List<String> codes) {
        return orgRepository.getAllOrgByCodes(ORG_IDENTIFIER, codes);
    }

    public List<OrgDo> selectRangeByBid(String bid) {
        return orgRepository.selectRangeByBid(ORG_IDENTIFIER, bid);
    }


    public List<OrgDo> getOrgByLeaderEmpId(String leadEmpId, Long dateTime) {
        return orgRepository.getOrgByLeaderEmpId(ORG_IDENTIFIER, leadEmpId, dateTime);
    }


    public static void i18Convert(OrgDo data, OrgVo it) {
        it.setI18nName(StringUtils.isEmpty(data.getI18nName()) ?
                ImmutableMap.of("default", Optional.ofNullable(data.getName()).orElse("")) :
                FastjsonUtil.toObject(data.getI18nName(), Map.class));
        it.setI18nFullName(StringUtils.isEmpty(data.getI18nFullName()) ?
                ImmutableMap.of("default", Optional.ofNullable(data.getFullName()).orElse("")) :
                FastjsonUtil.toObject(data.getI18nFullName(), Map.class));
        it.setI18nCode(StringUtils.isEmpty(data.getI18nCode()) ?
                ImmutableMap.of("default", Optional.ofNullable(data.getCode()).orElse("")) :
                FastjsonUtil.toObject(data.getI18nCode(), Map.class));
    }
}
