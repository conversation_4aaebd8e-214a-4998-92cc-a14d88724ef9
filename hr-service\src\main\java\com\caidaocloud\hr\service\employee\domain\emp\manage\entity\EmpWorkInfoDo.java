package com.caidaocloud.hr.service.employee.domain.emp.manage.entity;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpWorkInfoRepository;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpConcurrentPostLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPageQueryDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpSearchColumnsDto;
import com.caidaocloud.hr.service.enums.system.BaseEmpType;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.caidaocloud.hr.service.contract.application.constant.MsgCodeConstant.MISSING_EXCEPT_GRADUATE_DATE;

@Slf4j
@Data
@Service
public class EmpWorkInfoDo extends DataSimple {
    /**
     * 员工ID
     */
    private String empId;

    /**
     * 员工工号
     */
    private String workno;

    /**
     * 员工姓名（个人信息冗余字段
     */
    private String name;

    /**
     * 员工英文名（个人信息冗余字段
     */
    private String enName;

    /**
     * 入职日期
     */
    private Long hireDate;

    /**
     * 员工状态
     */
    private EnumSimple empStatus;

    /**
     * 员工头像
     */
    private Attachment photo;

    /**
     * 直接上级
     */
    private EmpSimple leadEmpId;

    /**
     * 所属组织Id
     */
    private String organize;

    /**
     * 所属组织名称
     */
    private String organizeTxt;

    /**
     * 职级职等
     */
    private JobGradeRange jobGrade;

    /**
     * 关联的职务ID
     */
    private String job;

    /**
     * 关联的职务名称
     */
    private String jobTxt;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 岗位名称
     */
    private String postTxt;

    /**
     * 试用期期限
     */
    private EnumSimple probation;

    /**
     * 转正日期
     */
    private Long confirmationDate;

    /**
     * 转正状态
     */
    private EnumSimple confirmationStatus;

    /**
     * 用工类型\员工类型
     */
    private DictSimple empType;

    /**
     * 离职日期
     */
    private Long leaveDate;

    /**
     * 工时制
     */
    private EnumSimple workHour;

    /**
     * 司龄
     */
    private BigDecimal divisionAge;

    /**
     * 司龄调整
     */
    private BigDecimal divisionAgeAdjust;

    /**
     * 员工公司邮箱
     */
    private String companyEmail;

    /**
     * 工作地ID
     */
    private String workplace;

    /**
     * 工作地名称
     */
    private String workplaceTxt;

    /**
     * 入司途径
     */
    private DictSimple joinCompanyWay;

    /**
     * 成本中心
     */
    private String costCenters;

    /**
     * 合同公司ID
     */
    private String company;

    /**
     * 合同公司名称
     */
    private String companyTxt;

    /**
     * 预计毕业日期
     */
    private Long expectGraduateDate;

    /**
     * 岗位英文
     */
    // private String enPostTxt;

    /**
     * 通讯地址邮编
     */
    private String mailAddressZipCode;

    /**
     * 工时制英文
     */
    // private String enWorkHour;

    /**
     * 学号
     */
    // private String studentNumber;


    /**
     * 预计离职日期
     */
    private Long expectedResignDate;

    /**
     * 社保缴纳地
     */
    private Address socialSecurity;

    /**
     * 公积金缴纳地
     */
    private Address providentFund;

    /**
     * 直接上级组织ID
     */
    private String leaderOrganize;

    /**
     * 直接上级组织名称
     */
    private String leaderOrganizeTxt;

    /**
     * 直接上级岗位ID
     */
    private String leaderPost;

    /**
     * 直接上级岗位名称
     */
    private String leaderPostTxt;

    /**
     * 离职类型
     */
    private DictSimple resignType;

    /**
     * 离职原因
     */
    private DictSimple resignReason;

    /**
     * 离职原因（员工申请）
     */
    private DictSimple empResignReason;

    /**
     * 司龄（至年底）
     */
    private BigDecimal divisionAgeToYear;

    /**
     * 试用期截止日期
     */
    private Long probationPeriodEndDate;

    /**
     * 合同类型
     */
    private DictSimple contractType;

    /**
     * 离职状态
     */
    private EnumSimple resignationStatus;

    /**
     * 退休日期
     */
    private Long retireDate;
    // 招聘顾问
    private EmpSimple recruitment;

    /**
     * 实习日期
     */
    private Long internshipDate;
    /**
     * 基准岗位txt
     */
    private String benchPostTxt;
    /**
     * 基准岗位id
     */
    private String benchPost;

    public final static String IDENTIFIER = "entity.hr.EmpWorkInfo", DICT_KEY = "DICT_%s";

    @Resource
    private IEmpWorkInfoRepository empWorkInfoRepository;
    @Resource
    private CacheService cacheService;

    public String getDoIdentifier() {
        return IDENTIFIER;
    }

    public boolean ifTermination() {
        return null != empStatus && EmpStatusEnum.LEAVE_JOB.getIndex().toString().equals(empStatus.getValue());
    }

    public EmpWorkInfoDo getEmpWorkInfo(String empId, Long dataTime) {
        EmpWorkInfoDo workInfo = empWorkInfoRepository.selectByEmpId(empId, IDENTIFIER, dataTime);
        doCalCorpAge(workInfo);
        doCalCorpAgeToYear(workInfo);
        return workInfo;
    }

//    public EmpWorkInfoDo getEmpWorkInfoHistory(String empId, Long dataTime) {
//        EmpWorkInfoDo workInfo = empWorkInfoRepository.selectByEmpId(empId, IDENTIFIER, dataTime);
//        log.info("参数：{}，{}，员工信息：{}", empId, dataTime, workInfo);
//        if(workInfo != null) {
//            doCalCorpAge(empWorkInfoDo);
//            doCalCorpAgeToYear(empWorkInfoDo);
//        }
//        return workInfo;
//    }

    public EmpWorkInfoDo getEmpByWorkno(String workno, Long dataTime) {
        EmpWorkInfoDo workInfo = empWorkInfoRepository.selectByWorkno(workno, dataTime, IDENTIFIER);
        doCalCorpAge(workInfo);
        doCalCorpAgeToYear(workInfo);
        return workInfo;
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByTime(Long dateTime) {
        List<EmpWorkInfoDo> empWorkInfoDos = empWorkInfoRepository.selectByTime(dateTime);
        return empWorkInfoDos;
    }

    public List<EmpWorkInfoDo> getEmpMainPostForLeader(String leaderEmpId, Long dateTime) {
        return empWorkInfoRepository.getEmpMainPostForLeader(leaderEmpId, IDENTIFIER, dateTime);
    }

    /**
     * 计算司龄
     *
     * @param workInfoDo
     */
    private void doCalCorpAge(EmpWorkInfoDo workInfoDo) {
        if (workInfoDo == null) {
            return;
        }
        BigDecimal corpAgeDec = new BigDecimal(0);
        if (workInfoDo.getHireDate() != null) {
            // 计算当前日期距离入职日期相差的月份
            Long empOnCorpEndDate = DateUtil.getCurrentTimestamp();
            if (workInfoDo.getEmpStatus() != null
                    && EmpStatusEnum.LEAVE_JOB.realValue().equals(workInfoDo.getEmpStatus().getValue())
                    && null != workInfoDo.getLeaveDate()) {
                empOnCorpEndDate = workInfoDo.getLeaveDate();
            }
            FastDateFormat dateFormat = FastDateFormat.getInstance("yyyy-MM-dd");
            long dayDiff = ChronoUnit.DAYS.between(LocalDate.parse(dateFormat.format(workInfoDo.getHireDate())), LocalDate.parse(dateFormat.format(empOnCorpEndDate)));
            if (dayDiff > 0) {
                corpAgeDec = new BigDecimal(dayDiff).divide(new BigDecimal(365), 1, RoundingMode.HALF_UP);
            }
        }

        BigDecimal ageAdjust = workInfoDo.getDivisionAgeAdjust();
        ageAdjust = ageAdjust == null ? BigDecimal.ZERO : ageAdjust;

        BigDecimal corpAge = corpAgeDec.add(ageAdjust).setScale(1, RoundingMode.HALF_UP);
        workInfoDo.setDivisionAge(corpAge);
    }

    /**
     * 计算司龄(至年底)
     *
     * @param workInfoDo
     */
    private void doCalCorpAgeToYear(EmpWorkInfoDo workInfoDo) {
        if (workInfoDo == null) {
            return;
        }
        BigDecimal corpAgeDec = new BigDecimal(0);
        if (workInfoDo.getHireDate() != null) {
            // 计算当前年底距离入职日期相差的天数
            FastDateFormat dateFormat = FastDateFormat.getInstance("yyyy-MM-dd");
            long dayDiff = ChronoUnit.DAYS.between(LocalDate.parse(dateFormat.format(workInfoDo.getHireDate())), getCurrentLastOfYear());
            if (dayDiff > 0) {
                corpAgeDec = new BigDecimal(dayDiff).divide(new BigDecimal(365), 2, RoundingMode.HALF_UP);
            }
            if (Objects.nonNull(workInfoDo.getDivisionAgeAdjust())) {
                corpAgeDec = corpAgeDec.add(workInfoDo.getDivisionAgeAdjust());
            }
        }
        workInfoDo.setDivisionAgeToYear(corpAgeDec);
    }

    /**
     * 获取当年的最后一天
     */
    public static LocalDate getCurrentLastOfYear() {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, Calendar.getInstance().get(Calendar.YEAR));
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoList(String empId) {
        EmpWorkInfoDo query = new EmpWorkInfoDo();
        query.setIdentifier(IDENTIFIER);
        query.setTenantId(UserContext.getTenantId());
        query.setEmpId(empId);
        return empWorkInfoRepository.selectList(query);
    }

    public EmpWorkInfoDo save(EmpWorkInfoDo data) {
        DataEntity.initDataSimpleBaseFieldValue(IDENTIFIER, data, null);
        checkGraduateDate(data, true);
        empWorkInfoRepository.insert(data);
        return data;
    }

    public void update(EmpWorkInfoDo data) {
        updateEmpWorkInfo(data, true);
    }

    public void updateEmpWorkInfo(EmpWorkInfoDo data, boolean checkGraduateDate) {
        data.setIdentifier(IDENTIFIER);
        checkGraduateDate(data, checkGraduateDate);
        empWorkInfoRepository.updateById(data);
    }

    public void delete(EmpWorkInfoDo data) {
        data.setIdentifier(IDENTIFIER);
        empWorkInfoRepository.delete(data, data.getDataStartTime());
    }

//    public void deleteAll(EmpWorkInfoDo data) {
//        EmpWorkInfoDo query = new EmpWorkInfoDo();
//        query.setIdentifier(IDENTIFIER);
//        query.setTenantId(UserContext.getTenantId());
//        query.setEmpId(data.getEmpId());
//        List<EmpWorkInfoDo> datas = empWorkInfoRepository.selectListNoDataTime(query);
//        datas.forEach(o-> empWorkInfoRepository.delete(o, o.getDataStartTime()));
//    }

    public void deleteAll(EmpWorkInfoDo data) {
        List<EmpWorkInfoDo> datas = getEmpTimeLineListByEmpId(data.getEmpId());
        datas.forEach(o -> empWorkInfoRepository.delete(o, o.getDataStartTime()));
    }

    public void checkGraduateDate(EmpWorkInfoDo data, boolean checkGraduateDate) {
        if (!checkGraduateDate) {
            return;
        }
        if (data.getEmpType() == null) {
            return;
        }

        String dictJson = cacheService.getValue(String.format(DICT_KEY, data.getEmpType().getValue()));
        if (dictJson == null) {
            return;
        }
        SysParamDictDto dict = FastjsonUtil.toObject(dictJson, SysParamDictDto.class);
        if (BaseEmpType.isIntern(dict.getDictCode())) {
            // 当员工类型为实现生时，判断预计毕业日期必填
            PreCheck.preCheckNotNull(data.getExpectGraduateDate(), LangUtil.getMsg(MISSING_EXCEPT_GRADUATE_DATE));
        }
    }

    /**
     * 分页列表查询
     */
    public PageResult<EmpWorkInfoDo> selectPage(EmpPageQueryDto dto) {
        EmpWorkInfoDo query = new EmpWorkInfoDo();
        query.setIdentifier(IDENTIFIER);
        query.setTenantId(UserContext.getTenantId());
        query.setOrganize(dto.getOrganize());
        query.setLeaveDate(dto.getLeaveDate());
        if (dto.getEmpStatus() != null) {
            EnumSimple empStatus = new EnumSimple();
            empStatus.setValue(String.valueOf(dto.getEmpStatus()));
            query.setEmpStatus(empStatus);
        }
        return empWorkInfoRepository.selectPage(dto, query, dto.getKeywords(), DataEntity.getTimeIfNullSetDefault(dto.getDateTime()));
    }

    /**
     * 查询公司下所人时间轴后的信息
     *
     * @param dateStartTime
     * @return
     */
    public List<EmpWorkInfoDo> selectGeStartTime(Long dateStartTime) {
        EmpWorkInfoDo data = new EmpWorkInfoDo();
        data.setIdentifier(IDENTIFIER);
        data.setTenantId(UserContext.getTenantId());
        return empWorkInfoRepository.selectGeStartTime(data, dateStartTime);
    }

    /**
     * 查公司下所时间轴生效的员工
     *
     * @param dateStartTime
     * @return
     */
    public List<EmpWorkInfoDo> selectListByCompany(Long dateStartTime, String companyId) {
        EmpWorkInfoDo data = new EmpWorkInfoDo();
        data.setIdentifier(IDENTIFIER);
        data.setTenantId(companyId);
        return empWorkInfoRepository.selectListByCompany(data, dateStartTime);
    }

    /**
     * 查岗位下所时间轴生效的员工
     *
     * @param dateStartTime
     * @param postId
     * @return
     */
    public List<EmpWorkInfoDo> selectListByPost(Long dateStartTime, String postId) {
        EmpWorkInfoDo data = new EmpWorkInfoDo();
        data.setIdentifier(IDENTIFIER);
        data.setPost(postId);
        return empWorkInfoRepository.selectListByPost(data, dateStartTime);
    }

    public List<EmpWorkInfoDo> getEmpListByCompanyEmail(List<String> companyEmailList) {
        return empWorkInfoRepository.getEmpListByCompanyEmail(UserContext.getTenantId(),
                IDENTIFIER, System.currentTimeMillis(), companyEmailList);
    }

    public List<EmpWorkInfoDo> getLeaveEmpList(Long leaveDate, Integer empStatus) {
        return empWorkInfoRepository.getLeaveEmpList(UserContext.getTenantId(),
                IDENTIFIER, System.currentTimeMillis(), leaveDate, empStatus);
    }

    public List<EmpWorkInfoDo> getByWorkno(String bid, String workno, Long dateStartTime) {
        return empWorkInfoRepository.getByWorkno(UserContext.getTenantId(), IDENTIFIER, bid, workno, dateStartTime);
    }

    public List<EmpWorkInfoDo> getEmpListByWorkNos(List<String> workNos) {
        return empWorkInfoRepository.getEmpListByWorkNos(UserContext.getTenantId(),
                IDENTIFIER, System.currentTimeMillis(), workNos);
    }

    public PageResult<Map<String, String>> queryEmpColumnsPage(EmpSearchColumnsDto empSearchColumnsDto) {
        return empWorkInfoRepository.queryEmpColumnsPage(empSearchColumnsDto, IDENTIFIER);
    }

    public List<EmpWorkInfoDo> getEmpListByEmpIds(List<String> empIds, long dateTime) {
        return empWorkInfoRepository.getEmpListByEmpIds(IDENTIFIER, dateTime, empIds);
    }

    public List<EmpWorkInfoDo> getNoLeaveEmpListByEmpIds(List<String> empIds, long dateTime) {
        return empWorkInfoRepository.getNoLeaveEmpListByEmpIds(IDENTIFIER, dateTime, empIds);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpReportLeaderDto(EmpReportLeaderDto empReportLeaderDto, long dateTime) {
        return empWorkInfoRepository.getEmpWorkInfoByEmpReportLeaderDto(IDENTIFIER, dateTime, empReportLeaderDto);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpConcurrentPostLeaderDto(EmpConcurrentPostLeaderDto empConcurrentPostDto, long dateTime) {
        return empWorkInfoRepository.getEmpWorkInfoByEmpConcurrentPostLeaderDto(IDENTIFIER, dateTime, empConcurrentPostDto);
    }

    public List<EmpWorkInfoDo> getEmpListByExt(Map<String, String> extMap, Long dateTime) {
        return empWorkInfoRepository.getEmpListByExt(extMap, IDENTIFIER, dateTime);
    }

    public List<EmpWorkInfoDo> getEmpByOrganize(String organize, Long dateTime) {
        return empWorkInfoRepository.getEmpDisable(UserContext.getTenantId(), IDENTIFIER, organize, null, dateTime);
    }

    public PageResult<Map<String, String>> newlySignedList(ContractQueryDto query) {
        return empWorkInfoRepository.newlySignedList(IDENTIFIER, query, DateUtil.getCurrentTimestamp());
    }


    public void saveContractRelation(String empId, String contractId) {
        EmpWorkInfoDo workInfo = empWorkInfoRepository.selectByEmpId(empId, IDENTIFIER, DateUtil.getCurrentTimestamp());
        if (workInfo == null) {
            log.warn("emp of contract is null,emp id = {},contract id = {}", empId, contractId);
            return;
        }
        PropertyValue propertyValue = workInfo.getProperties().get("lastContract.bid");
        if (propertyValue != null && propertyValue instanceof SimplePropertyValue) {
            String empContractBid = ((SimplePropertyValue) propertyValue).getArrayValues().get(0);
            if (contractId.equals(empContractBid)) {
                return;
            }
        }

        empWorkInfoRepository.saveContractRelation(workInfo, contractId);
    }

    public List<EmpWorkInfoDo> getEmpListByPosts(Long dateTime, List<String> postIds) {
        return empWorkInfoRepository.getEmpListByPosts(IDENTIFIER, dateTime, postIds);
    }

    public List<EmpWorkInfoDo> getNonLeaveEmpPage(long dateTime, BasePage basePage) {
        return empWorkInfoRepository.getNonLeaveEmpPage(IDENTIFIER, dateTime, basePage);
    }

    public List<EmpWorkInfoDo> selectByLikeCostCenter(Long dateTime, String costCenterId) {
        return empWorkInfoRepository.selectByLikeCostCenter(IDENTIFIER, dateTime, costCenterId);
    }

    public List<EmpWorkInfoDo> selectListByJob(Long dateTime, String jobId) {
        return empWorkInfoRepository.selectByJob(IDENTIFIER, dateTime, jobId);
    }

    public List<EmpWorkInfoDo> getEmpListByOrgId(String orgBid, Long dateTime) {
        return empWorkInfoRepository.getEmpListByOrgId(IDENTIFIER, dateTime, orgBid);
    }

    public List<EmpWorkInfoDo> gerEmpWorkInfoRange(String empId, Long startTime, Long endTime) {
        return empWorkInfoRepository.gerEmpWorkInfoRange(IDENTIFIER, empId, startTime, endTime);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoAtWork(List<String> empIds) {
        return empWorkInfoRepository.getEmpWorkInfoAtWork(IDENTIFIER, empIds, System.currentTimeMillis());
    }

    public List<EmpWorkInfoDo> getEmpTimeLineListByEmpId(String empId) {
        EmpWorkInfoDo query = new EmpWorkInfoDo();
        query.setIdentifier(IDENTIFIER);
        query.setTenantId(UserContext.getTenantId());
        query.setEmpId(empId);
        return empWorkInfoRepository.selectListNoDataTime(query);
    }
}
