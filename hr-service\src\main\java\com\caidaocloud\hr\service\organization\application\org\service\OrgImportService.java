package com.caidaocloud.hr.service.organization.application.org.service;


import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.dataimport.DataImportService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpBasicInfoService;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.organization.application.org.dto.DictKeyValue;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgImportDo;
import com.caidaocloud.hr.service.organization.infrastructure.repository.po.OrgImportPo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeParent;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrgImportService extends DataImportService<OrgImportDo, OrgImportPo> {
    private static String businessCode = "ORG_INFO_IMPORT",
            DICT_MODEL = "M_ORG",
            OrgTypeDictCode = "OrgType",
            ArchitectureTypeDictCode = "ArchitectureType",
            ADMINISTRATION = "Administration",
            split = "_";

    public static Map<String, DictKeyValue> dictMaps = new HashMap<>();

    @Resource
    private OrgImportDo orgImportDo;
    @Resource
    private OrgService orgService;
    @Resource
    private DictService dictService;
    @Resource
    private EmpBasicInfoService empBasicInfoService;

    @Override
    public String getExcelCode() {
        return businessCode;
    }

    @Override
    public List<OrgImportDo> getPoDataFromExcel(InputStream inputStream) {
        return orgImportDo.getOrgImportDoFromExcel(inputStream);
    }

    @Override
    public List<OrgImportDo> batchInsertUpdateData(List<OrgImportDo> list) {
        if(null == list || list.size() == 0){
            return new ArrayList<>();
        }
        List<OrgImportDo> errorList = new ArrayList<>();
        List<OrgDo> orgList = new ArrayList<>();
        List<OrgDo> orgPidList = new ArrayList<>();
        List<EmpBasicInfoDo> basicList = new ArrayList<>();

        List<String> codeList = new ArrayList<>();
        List<String> pidList = new ArrayList<>();
        List<String> leaderEmpIds = new ArrayList<>();
        for(OrgImportDo data :list){
            codeList.add(data.getCode());
            pidList.add(data.getPidValue());
            leaderEmpIds.add(data.getLeaderEmpId());
        }

        // 根据组织code查数据
        codeList.removeAll(Collections.singleton(null));
        if(CollectionUtils.isNotEmpty(codeList)){
            orgList = orgService.getOrgListByOrgCodes(codeList,System.currentTimeMillis());
        }
        Map<String, OrgDo> oldOrgMap = orgList.stream().collect(Collectors.toMap(OrgDo::getCode, st -> st));


        // 根据组织父级id查数据
        pidList.removeAll(Collections.singleton(null));
        if(CollectionUtils.isNotEmpty(pidList)){
            orgPidList = orgService.selectByIds(pidList, System.currentTimeMillis());
        }
        Map<String, OrgDo> oldOrgPidMap = orgPidList.stream().collect(Collectors.toMap(OrgDo::getBid, st -> st));


        // 根据组织负责人查数据
        leaderEmpIds.removeAll(Collections.singleton(null));
        if(CollectionUtils.isNotEmpty(leaderEmpIds)){
            basicList = empBasicInfoService.getEmpBasicInfoListByBids(leaderEmpIds, System.currentTimeMillis());
        }
        Map<String, EmpBasicInfoDo> oldBasicInfoMap = basicList.stream().collect(Collectors.toMap(EmpBasicInfoDo::getBid, st -> st));

        if(null == orgList || orgList.size() == 0){
            return insertOrgImportList(list,oldOrgMap,oldBasicInfoMap,oldOrgPidMap);
        }

        List<OrgImportDo> insertList = new ArrayList<>();
        List<OrgImportDo> updateList = new ArrayList<>();
        for(OrgImportDo importDo : list){
            OrgDo orgDo = oldOrgMap.get(importDo.getCode());
            if(orgDo == null){
                insertList.add(importDo);
            } else {
                importDo.setBid(orgDo.getBid());
                updateList.add(importDo);
            }
        }
        errorList.addAll(insertOrgImportList(insertList,oldOrgMap,oldBasicInfoMap,oldOrgPidMap));
        errorList.addAll(updateOrgImportList(updateList,oldOrgMap,oldBasicInfoMap,oldOrgPidMap));

        return errorList;
    }

    private List<OrgImportDo> updateOrgImportList(List<OrgImportDo> updateList, Map<String, OrgDo> oldOrgMap,
            Map<String, EmpBasicInfoDo> oldBasicInfoMap,
            Map<String, OrgDo> oldOrgPidMap) {
        List<OrgImportDo> errorList = new ArrayList<>();
        for(OrgImportDo importDo : updateList){
            try {
                OrgDo orgDo = oldOrgMap.get(importDo.getCode());
                BeanUtils.copyProperties(importDo, orgDo);
                doConvertOrgInfo(importDo,orgDo,oldBasicInfoMap,oldOrgPidMap);
                orgService.updateOrg(orgDo, null);

            }catch (Exception e){
                log.error("组织导入异常--> error:{}", e.getMessage());
                setEmptyTips(importDo, e.getMessage());
                errorList.add(importDo);
            }
        }
        return errorList;
    }

    private void doConvertOrgInfo(OrgImportDo importDo, OrgDo orgDo, Map<String, EmpBasicInfoDo> oldBasicInfoMap, Map<String, OrgDo> oldOrgPidMap) {
        if(StringUtils.isNotBlank(importDo.getLeaderEmpId())){
            EmpBasicInfoDo basicInfo = oldBasicInfoMap.get(importDo.getLeaderEmpId());
            if(basicInfo == null || StringUtils.isEmpty(basicInfo.getBid())){
                throw new RuntimeException("导入数据异常，未找到部门负责人");
            }
            EmpSimple empSimple = new EmpSimple();
            empSimple.setEmpId(basicInfo.getBid());
            empSimple.setWorkno(basicInfo.getWorkno());
            empSimple.setName(basicInfo.getName());
            orgDo.setLeaderEmp(empSimple);
        }

        if ("2154/12/31 0:00:00".equals(importDo.getDataEndTimeTxt())) {
            orgDo.setDataEndTime(253402271999000L);
        }
        if(StringUtils.isEmpty(importDo.getCreateTimeTxt())){
            orgDo.setCreateTime(0L);
        }
        if(StringUtils.isEmpty(importDo.getUpdateTimeTxt())){
            orgDo.setUpdateTime(0L);
        }
        if(StringUtils.isNotBlank(importDo.getName())){
            orgDo.setFullName(importDo.getName());
        }

        orgDo.setVirtual(false);
        String splitTenantId = split + getTenantId();
        String sexDictKey = DICT_MODEL + split + ArchitectureTypeDictCode + split + ADMINISTRATION + splitTenantId;
        DictKeyValue dict = dictMaps.get(sexDictKey);
        if (dict != null && dict.getValue() != null) {
            DictSimple dictSimple = new DictSimple();
            dictSimple.setText(dict.getText());
            dictSimple.setValue(dict.getValue());
            orgDo.setSchemaType(dictSimple);
        }
        if(StringUtils.isNotBlank(importDo.getPidValue())){
            OrgDo pidData = oldOrgPidMap.get(importDo.getPidValue());
            if(pidData == null || StringUtils.isEmpty(pidData.getBid())){
                throw new RuntimeException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30041));
            }
            TreeParent parent = new TreeParent();
            parent.setPid(pidData.getBid());
            orgDo.setPid(parent);
        }
    }

    private List<OrgImportDo> insertOrgImportList(List<OrgImportDo> insertList, Map<String, OrgDo> oldOrgMap,
            Map<String, EmpBasicInfoDo> oldBasicInfoMap,
            Map<String, OrgDo> oldOrgPidMap) {
        List<OrgImportDo> errorList = new ArrayList<>();
        for(OrgImportDo importDo : insertList){
            try {
                OrgDo orgDo = ObjectConverter.convert(importDo, OrgDo.class);
                doConvertOrgInfo(importDo,orgDo,oldBasicInfoMap, oldOrgPidMap);
                orgService.saveOrg(orgDo, null);

            }catch (Exception e){
                log.error("组织导入异常--> error:{}", e.getMessage());
                setEmptyTips(importDo, e.getMessage());
                errorList.add(importDo);
            }
        }
        return errorList;

    }

    @Override
    public boolean checkEmptyProp(OrgImportDo org) {
        if(StringUtils.isEmpty(org.getCode())){
            setEmptyTips(org, "部门编码不能为空");
        }
        if(StringUtils.isEmpty(org.getName())){
            setEmptyTips(org, "部门名称不能为空");
        }
        if(StringUtils.isEmpty(org.getOrgTypeCode())){
            setEmptyTips(org, "部门类型不能为空");
        }
        return true;
    }

    private void setEmptyTips(OrgImportDo data, String tip){
        data.setCheckEmpty(true);
        if(null == data.getCheckEmptyTips()){
            data.setCheckEmptyTips(tip);
        }else {
            data.setCheckEmptyTips(data.getCheckEmptyTips() + "，" + tip);
        }
    }

    @Override
    public boolean checkEmptyMark(OrgImportDo data) {
        return data.isCheckEmpty();
    }

    @Override
    public boolean installProp(OrgImportDo data) {
        installDate(data);
        installOrgType(data);

        if(data.isCheckEmpty()){
            return false;
        }
        return true;
    }

    private void installDate(OrgImportDo data) {
        DateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        try {
            if(StringUtils.isNotBlank(data.getDataStartTimeTxt())){
                data.setDataStartTime(format.parse(data.getDataStartTimeTxt()).getTime());
            }
            if(StringUtils.isNotBlank(data.getDataEndTimeTxt())){
                data.setDataEndTime(format.parse(data.getDataEndTimeTxt()).getTime());
            }
            if(StringUtils.isNotBlank(data.getCreateTimeTxt())){
                data.setCreateTime(format.parse(data.getCreateTimeTxt()).getTime());
            }
            if(StringUtils.isNotBlank(data.getUpdateTimeTxt())){
                data.setUpdateTime(format.parse(data.getUpdateTimeTxt()).getTime());
            }
        } catch (ParseException e) {
            setEmptyTips(data, "日期转换异常");
            log.error("import Data parse error:{}", e.getMessage());
        } catch (ServerException e) {
            setEmptyTips(data, e.getMessage());
        }
    }

    private void installOrgType(OrgImportDo data) {
        String splitTenantId = split + getTenantId();
        try {
            /**
             * 设置组织类型 字典枚举
             */
            if (StringUtils.isNotBlank(data.getOrgTypeCode())) {
                String sexDictKey = DICT_MODEL + split + OrgTypeDictCode + split + data.getOrgTypeCode() + splitTenantId;
                DictKeyValue dict = dictMaps.get(sexDictKey);
                if (dict != null && dict.getValue() != null) {
                    DictSimple dictSimple = new DictSimple();
                    dictSimple.setText(dict.getText());
                    dictSimple.setValue(dict.getValue());
                    data.setOrgType(dictSimple);
                } else {
                    setEmptyTips(data, "组织类型不存在");
                }
            }
        } catch (Exception e) {
            setEmptyTips(data, e.getMessage());
        }
    }

    @Override
    public void initProperty(){
        List<Pair<String, String>> employeeDictModelList = new ArrayList<>(3);
        employeeDictModelList.add(Pair.pair(DICT_MODEL, OrgTypeDictCode));
        employeeDictModelList.add(Pair.pair(DICT_MODEL, ArchitectureTypeDictCode));

        for (Pair<String, String> pair : employeeDictModelList) {
            Result result = dictService.getEnableDictListAllInfo(pair.getValue(), pair.getKey());
            if (result.isSuccess() && result.getData() != null) {
                List<DictKeyValue> dictKeyValueList = FastjsonUtil.toList(FastjsonUtil.toJson(result.getData()), DictKeyValue.class);
                if (CollectionUtils.isNotEmpty(dictKeyValueList)) {
                    dictKeyValueList.forEach(
                            dictKeyValue ->
                                    dictMaps.put(pair.getKey() + split + pair.getValue() + split + dictKeyValue.getCode() + "_" + UserContext.getTenantId(), dictKeyValue)
                    );
                }
            }
        }
        log.info("dictMaps {}", dictMaps);
    }
}
