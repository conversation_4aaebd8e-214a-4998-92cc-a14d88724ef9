package com.caidaocloud.hr.service.organization.domain.company.entity;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.SortOrder;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.domain.company.repository.ICompanyRepository;
import com.caidaocloud.hr.service.organization.domain.company.repository.IPayUnitRepository;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.CompanyQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.PayUnitQueryDto;
import com.caidaocloud.hr.service.vo.organization.company.CompanyVo;
import com.caidaocloud.hr.service.vo.organization.company.PayUnitVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 合同公司（公司管理）
 *
 * <AUTHOR>
 * @Date 2021/11/23
 */
@Slf4j
@Data
@Service
public class PayUnitDo extends DataEntity {
    /**
     * 缴纳单位
     */
    private String unitName;
    /**
     *
     */
    private String i18nUnitName;
    /**
     * 缴纳单位编码
     */
    private String unitCode;
    private String socialSecurityNo;
    private String socialCreditCode;
    private String operatorName;
    private String operatorPhone;
    private String bank;
    private String account;
    private String serviceFee;

    private final static String UNIT_IDENTIFIER = "entity.hr.PayUnit";

    @Resource
    private IPayUnitRepository payUnitRepository;

    public String getDoIdentifier() {
        return UNIT_IDENTIFIER;
    }

    /**
     * 新增
     *
     * @param data
     * @return
     */
    public String save(PayUnitDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        String userId = null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
        String tenantId = null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
        data.setTenantId(tenantId);
        data.setCreateBy(userId);
        data.setCreateTime(System.currentTimeMillis());
        data.setUpdateBy(userId);
        data.setUpdateTime(data.getCreateTime());
        data.setIdentifier(UNIT_IDENTIFIER);
        data.setDeleted(Boolean.FALSE);
        return payUnitRepository.insert(data).getBid();
    }

    /**
     * 编辑
     * @param data
     */
    public void update(PayUnitDo data) {
        UserInfo userInfo = UserContext.preCheckUser();
        PayUnitDo dbData = payUnitRepository.selectById(data.getBid(), UNIT_IDENTIFIER);
        PreCheck.preCheckArgument(null == dbData || null == dbData.getBid(), "编辑的数据不存在!");

        List<String> props = Lists.newArrayList("bid", "id", "identifier", "entityId", "tenantId", "createTime", "createBy",
                "deleted", "dataStartTime", "dataEndTime");
        
        data.setUpdateBy(userInfo.getUserid().toString());
        data.setUpdateTime(System.currentTimeMillis());

        BeanUtil.copyProperties(data, dbData, props.toArray(new String[]{}));

        payUnitRepository.updateById(dbData);
    }

    /**
     * 删除
     *
     * @param data
     */
    public void deleteById(PayUnitDo data) {
        data.setIdentifier(UNIT_IDENTIFIER);
        payUnitRepository.delete(data);
    }

    /**
     * 查看详情
     *
     * @param bid
     * @return
     */
    public PayUnitDo selectById(String bid) {
        return payUnitRepository.selectById(bid, UNIT_IDENTIFIER);
    }

    public List<PayUnitDo> selectBatchIds(List<String> bids) {
        return payUnitRepository.selectBatchIds(bids, UNIT_IDENTIFIER, UserContext.getTenantId());
    }

    /**
     * 分页列表查询
     *
     * @param page
     * @return
     */
    public PageResult<PayUnitDo> selectPage(PayUnitQueryDto page) {
        UserInfo userInfo = UserContext.preCheckUser();
        PayUnitDo data = new PayUnitDo();
        data.setIdentifier(UNIT_IDENTIFIER);
        data.setTenantId(userInfo.getTenantId());
        return payUnitRepository.selectPage(page, data);
    }

    public List<PayUnitDo> selectList() {
        UserInfo userInfo = UserContext.preCheckUser();
        PayUnitDo data = new PayUnitDo();
        data.setIdentifier(UNIT_IDENTIFIER);
        data.setTenantId(userInfo.getTenantId());
        return payUnitRepository.selectList(data);
    }

    public List<PayUnitDo> selectListAll() {
        UserInfo userInfo = UserContext.preCheckUser();
        PayUnitDo data = new PayUnitDo();
        data.setIdentifier(UNIT_IDENTIFIER);
        data.setTenantId(userInfo.getTenantId());
        return payUnitRepository.selectListAll(data);
    }

    public List<PayUnitDo> selectByCode(PayUnitDo data) {
        data.setIdentifier(UNIT_IDENTIFIER);
        data.setTenantId(UserContext.getTenantId());
        return payUnitRepository.selectByCode(data);
    }

    public List<PayUnitDo> selectByCodes(List<String> codes) {
        UserInfo userInfo = UserContext.preCheckUser();
        return payUnitRepository.selectByCodes(codes, UNIT_IDENTIFIER, userInfo.getTenantId());
    }

    public static PayUnitDo selectAllNode(){
        PayUnitDo data = new PayUnitDo();
        data.setBid("-1");
        data.setUnitName("全部缴纳单位");
        return data;
    }

    public static void i18nConvert(PayUnitDo payUnitDo, PayUnitVo it) {
        it.setI18nUnitName(StringUtils.isEmpty(payUnitDo.getI18nUnitName()) ?
                ImmutableMap.of("default", Optional.ofNullable(payUnitDo.getUnitName()).orElse("")) :
                FastjsonUtil.toObject(payUnitDo.getI18nUnitName(), Map.class));
    }

}
