package com.caidaocloud.hr.service.organization.application.company.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.lang.Pair;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.common.infrastructure.utils.ObjectConvertUtil;
import com.caidaocloud.hr.service.dto.adapter.CompanyInputDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.company.entity.PayUnitDo;
import com.caidaocloud.hr.service.organization.domain.company.service.CompanyDomainService;
import com.caidaocloud.hr.service.organization.domain.company.service.PayUnitDomainService;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.CompanyDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.CompanyQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.PayUnitDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.PayUnitQueryDto;
import com.caidaocloud.hr.service.vo.organization.company.CompanyVo;
import com.caidaocloud.hr.service.vo.organization.company.PayUnitVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.rmi.ServerException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 缴纳单位
 *
 * <AUTHOR>
 * @Date 2024.12.6
 */
@Service
public class PayUnitService {
    @Resource
    private PayUnitDomainService payUnitDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private RedisTemplate<String,Integer> redisTemplate;

    private final String GENERATE_UNITCODE_CACHE_KEY = "HR_GENERATE_UNITCODE_NUM_%s";

    /**
     * 创建
     *
     * @param
     * @return
     */
    public String savePayUnit(PayUnitDto dto) {
        dto.setUnitCode(increseNum());
        PayUnitDo payUnitDo = ObjectConverter.convert(dto, PayUnitDo.class);
        empExtFieldService.doCusExtProps(payUnitDo.getDoIdentifier(), dto.getExt(), payUnitDo);
        return payUnitDomainService.save(payUnitDo);
    }

    /**
     * 编辑公司
     * @param
     */
    public void updatePayUnitById(PayUnitDto dto) {
        PayUnitDo payUnitDo = ObjectConverter.convert(dto, PayUnitDo.class);
        empExtFieldService.doCusExtProps(payUnitDo.getDoIdentifier(), dto.getExt(), payUnitDo);
        payUnitDomainService.update(payUnitDo);
    }

    /**
     * 删除
     * @param data
     */
    public void deleteUnitById(PayUnitDo data) {
        PayUnitDo payUnitDo = payUnitDomainService.selectById(data.getBid());
        payUnitDomainService.deleteById(data);
        LogRecordContext.putVariable("name", payUnitDo.getUnitName());
        LogRecordContext.putVariable("condition", true);
    }

    /**
     * 查看详情
     * @param bid
     * @return
     */
    public PayUnitDo getPayUnitById(String bid) {
        return payUnitDomainService.selectById(bid);
    }

    /**
     * 查看缴纳单位列表
     * @param payUnitQueryDto
     * @return
     */
    public PageResult<PayUnitDo> getPayUnitPageResult(PayUnitQueryDto payUnitQueryDto) {
        return payUnitDomainService.selectPage(payUnitQueryDto);
    }

    public List<PayUnitDo> selectList() {
        return payUnitDomainService.selectList();
    }

    public List<PayUnitDo> selectListAll() {
        return payUnitDomainService.selectListAll();
    }

    public List<PayUnitDo> selectByCodes(List<String> codes) {
        return payUnitDomainService.selectByCodes(codes);
    }


    /**
     * 岗位导出
     * @return
     */

    public List<TagProperty> installNewlySignedExportProperty() {
        List<TagProperty> list = new ArrayList<>();
        addTagPropertyToList(list, "unitName", "缴纳单位名称", 1);
        addTagPropertyToList(list, "unitCode", "缴纳单位编码", 2);
        addTagPropertyToList(list, "socialSecurityNo", "社会保险登记编号", 3);
        addTagPropertyToList(list, "socialCreditCode", "统一社会信用代码", 4);
        addTagPropertyToList(list, "operatorName", "经办人姓名", 5);
        addTagPropertyToList(list, "operatorPhone", "经办人电话", 6);
        addTagPropertyToList(list, "bank", "开户银行", 7);
        addTagPropertyToList(list, "account", "开户账号", 8);
        addTagPropertyToList(list, "serviceFee", "服务费", 9);
        return list;
    }

    public void addTagPropertyToList(List<TagProperty> list, String property, String propertyTxt, int order) {
        list.add(new TagProperty(property, propertyTxt, order));
    }

    public PageResult<PayUnitVo> getPayUnitForExport(PayUnitQueryDto dto) {
        PageResult<PayUnitDo> pageResult = getPayUnitPageResult(dto);
        List<PayUnitVo> items = ObjectConvertUtil.convertList(pageResult.getItems(), PayUnitVo.class, PayUnitDo::i18nConvert);
        return new PageResult(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }

    private String increseNum(){
        // 检查键是否存在
        String cacheKey = String.format(GENERATE_UNITCODE_CACHE_KEY, SecurityUserUtil.getSecurityUserInfo().getTenantId());
        Boolean hasKey = redisTemplate.hasKey(cacheKey);
        Integer value;
        if (hasKey != null && hasKey) {
            value = redisTemplate.opsForValue().increment(cacheKey).intValue();
        } else {
            redisTemplate.opsForValue().set(cacheKey, 1);
            value = 1;
        }
        // 构建 4 位数字字符串
        return String.format("PU%04d", value);
       
    }
    
    
}
