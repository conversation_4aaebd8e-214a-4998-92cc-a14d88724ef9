package com.caidaocloud.hr.service.organization.domain.jobgrade.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeChannelDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 职级管理
 *
 * <AUTHOR>
 * @Date 2021/11/30
 */
@Slf4j
@Service
public class JobGradeChannelDomainService {
    @Resource
    private JobGradeChannelDo jobGradeChannelDo;
    @Resource
    private JobGradeDo jobGradeDo;

    public String saveJobGradeChannel(JobGradeChannelDo data) {
        return jobGradeChannelDo.save(data);
    }

    public void updateJobGradeChannel(JobGradeChannelDo data) {
        jobGradeChannelDo.update(data);
    }

    public void deleteJobGradeChannel(JobGradeChannelDo data) {
        List<JobGradeDo> list = jobGradeDo.selectList(data.getBid(), null);
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(list), "请先删除职级通道下的职级");
        jobGradeChannelDo.delete(data);
    }

    public void updateStatus(JobGradeChannelDo data, BusinessEventTypeEnum eventTypeEnum) {
        if (BusinessEventTypeEnum.DISABLE == eventTypeEnum) {
            List<JobGradeDo> list = jobGradeDo.selectList(data.getBid(), StatusEnum.ENABLED.getIndex());
            PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(list), "请先停用职级通道下的职级");
        }
        jobGradeChannelDo.updateStatus(data, eventTypeEnum);
    }

    public JobGradeChannelDo selectJobGradeChannelById(String bid) {
        return jobGradeChannelDo.selectById(bid);
    }

    public List<JobGradeChannelDo> selectJobGradeChannelList(Integer status) {
        return jobGradeChannelDo.selectList(status);
    }

    public List<JobGradeChannelDo> selectJobGradeChannelListByIds(List<String> bids) {
        return jobGradeChannelDo.selectBatchIds(bids);
    }

    public List<JobGradeChannelDo> selectList(Integer status) {
        return jobGradeChannelDo.selectList(status);
    }
}
