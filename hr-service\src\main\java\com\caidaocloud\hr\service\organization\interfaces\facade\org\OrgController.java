package com.caidaocloud.hr.service.organization.interfaces.facade.org;

import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.dto.emp.RoleInfoDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.TreeDataStatusOptDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.base.PageListVo;
import com.caidaocloud.hr.service.organization.application.org.dto.LabelDataDto;
import com.caidaocloud.hr.service.organization.application.org.dto.OrgLabelData;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.CustomOrgRoleDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgReportDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgOrPostQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgPageQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.vo.org.OrgChangeRecordVo;
import com.caidaocloud.hr.service.organization.interfaces.vo.org.OrgReportExtendVo;
import com.caidaocloud.hr.service.vo.organization.company.org.OrgVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityRelationDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/hr/org/v1")
@Api(value = "/api/hr/org/v1", description = "组织管理", tags = "v0.2")
public class OrgController {
    @Resource
    private OrgService orgService;
    //  组织名称最大长度
    public static final int ORG_NAME_LENGTH = 500;

    @ApiOperation("新增组织")
    @PostMapping("/save")
    @LogRecordAnnotation(success = "新增了组织{{#orgName}}", category = "新增", menu = "组织-组织管理-组织架构", condition = "{{#condition}}")
    public Result save(@RequestBody OrgDto dto) {
        checkOrgDto(peekOrgDto(dto));
        OrgDo orgDo = ObjectConverter.convert(dto, OrgDo.class);
        orgDo.setI18nName(LangUtil.getI18nValue(dto.getName(), dto.getI18nName()));
        orgDo.setI18nFullName(LangUtil.getI18nValue(dto.getFullName(), dto.getI18nFullName()));
        orgDo.setCustomOrgRoles(convertCustomOrgRoleList(dto));
        String bid = orgService.saveOrg(orgDo, dto.getExt());
        return Result.ok(bid);
    }

    private List<CustomOrgRoleDo> convertCustomOrgRoleList(OrgDto dto) {
        if (CollectionUtils.isEmpty(dto.getOtherRoles())) {
            return Lists.list();
        }
        List<CustomOrgRoleDo> list = new LinkedList<>();
        for (RoleInfoDto infoDto : dto.getOtherRoles()) {
            CustomOrgRoleDo data = new CustomOrgRoleDo(infoDto.getDictValue(), infoDto.getEmp(), infoDto.getPost());
            list.add(data);
        }
        return list;
    }

    @ApiOperation("编辑组织")
    @PostMapping("/update")
    @LogRecordAnnotation(success = "编辑了组织{{#orgName}}", category = "编辑", menu = "组织-组织管理-组织架构", condition = "{{#condition}}")
    public Result update(@RequestBody OrgDto dto) {
        PreCheck.preCheckArgument(StringUtils.isBlank(dto.getBid()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30085));
        checkOrgDto(peekOrgDto(dto));
        OrgDo orgDo = ObjectConverter.convert(dto, OrgDo.class);
        orgDo.setI18nName(LangUtil.getI18nValue(dto.getName(), dto.getI18nName()));
        orgDo.setI18nFullName(LangUtil.getI18nValue(dto.getFullName(), dto.getI18nFullName()));
        orgDo.setCustomOrgRoles(convertCustomOrgRoleList(dto));
        orgService.updateOrg(orgDo, dto.getExt());
        return Result.ok(dto);
    }

    private OrgDto peekOrgDto(OrgDto dto) {
        dto.setName(StringUtils.isEmpty(dto.getName()) ? LangUtil.getDefaultValue(dto.getI18nName()) :
                dto.getName());
        dto.setFullName(StringUtils.isEmpty(dto.getFullName()) ? LangUtil.getDefaultValue(dto.getI18nFullName()) :
                dto.getFullName());
        return dto;
    }

    private void checkOrgDto(OrgDto dto) {
        PreCheck.preCheckArgument(null == dto.getDataStartTime(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30086));
        PreCheck.preCheckArgument(StringUtil.isBlank(dto.getFullName()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30087));
        PreCheck.preCheckArgument(StringUtil.isBlank(dto.getName()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30088));
        //PreCheck.preCheckArgument(StringUtil.isBlank(dto.getCode()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30089));
        if (dto.getVirtual() == null) {
            dto.setVirtual(false);
        }

        PreCheck.preCheckArgument(dto.getFullName()
                .length() > ORG_NAME_LENGTH, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32004));
        PreCheck.preCheckArgument(dto.getName()
                .length() > ORG_NAME_LENGTH, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32005));

    }

    @ApiOperation("删除组织")
    @DeleteMapping("/delete")
    @LogRecordAnnotation(success = "删除了组织{{#orgName}}", category = "删除", menu = "组织-组织管理-组织架构", condition = "{{#condition}}")
    public Result delete(@RequestParam("bid") String bid, @RequestParam("dataTime") Long dataTime) {
        orgService.deleteOrgById(bid, dataTime);
        return Result.ok(true);
    }

    @ApiOperation("启用或停用组织")
    @PostMapping("/updateStatus")
    @LogRecordAnnotation(success = "{{#operate}}了组织{{#orgName}}", category = "{{#operate}}", menu = "组织-组织管理-组织架构")
    public Result updateStatus(@RequestBody TreeDataStatusOptDto dto) {
        dto.preCheckTimelineArgument();
        OrgDo data = ObjectConverter.convert(dto, OrgDo.class);
        OrgDo orgById = orgService.getOrgById(data.getBid(), data.getDataStartTime());
        LogRecordContext.putVariable("orgName", orgById.getFullName());
        if (StatusEnum.ENABLED.getIndex().equals(dto.getStatus())) {
            LogRecordContext.putVariable("operate", "启用");
            orgService.enable(data, dto.getUpdateChildren());
        } else {
            LogRecordContext.putVariable("operate", "停用");
            return orgService.disable(data, data.getDataStartTime());
        }
        return Result.ok(true);
    }

    @ApiOperation("查看组织信息")
    @GetMapping("/detail")
    public Result<OrgVo> getDetail(@RequestParam("bid") String bid, @RequestParam("dataTime") Long dataTime) {
        return Result.ok(orgService.getDetail(bid, dataTime));
    }

    @ApiOperation("才到二期：查询组织列表")
    @PostMapping("/list")
    public Result getList(@RequestBody OrgPageQueryDto queryDto) {
        // 组织类型 orgType$dict$value
        // 负责人 leaderEmp$empId   HRBP hrbpEmp$empId (否)
        // leaderEmp  hrbpEmp  模糊
        // List<OrgTreeData<Map>> treeMapList = orgService.getTreeMapList(queryDto);
        List pageList = orgService.getPageList(queryDto);
        return Result.ok(pageList);
    }

    @ApiOperation("组织导出")
    @PostMapping("/export")
    public Result export(@RequestBody OrgPageQueryDto queryDto, HttpServletResponse response) {
        orgService.export(queryDto, response);
        return Result.ok();
    }

    @ApiOperation("查询组织列表")
    @GetMapping("/orgDetailList")
    public Result<List<OrgReportDo>> getList() {
        return Result.ok(orgService.getOrgReportList());
    }


    @ApiOperation("下拉框：查询组织树结构下拉数据")
    @GetMapping("/simpleTree")
    public Result getSimpleTreeList(@RequestParam(value = "dataTime", required = false) Long dataTime,
                                    @RequestParam(value = "schemaType", required = false, defaultValue = "") String schemaType) {
        dataTime = null == dataTime ? DateUtil.getMidnightTimestamp() : dataTime;
        List<TreeData<LabelDataDto>> treeData = orgService.fetchSimpleTree(dataTime, schemaType);
        return Result.ok(new ItemsResult<>(treeData));
    }

    @ApiOperation("查询包括公司的组织树")
    @GetMapping("/tree")
    public Result getCompanyTree(@RequestParam("dataTime") Long dataTime,
                                 @RequestParam(value = "schemaType", required = false, defaultValue = "") String schemaType) {
        return Result.ok(new ItemsResult<>(orgService.getCompanyTree(dataTime, schemaType, StatusEnum.ENABLED)));
    }

    @ApiOperation("查询包括公司的组织树")
    @GetMapping("/treeList")
    public Result<List<OrgLabelData>> getCompanyTreeByKey(@RequestParam("dataTime") Long dataTime,
                                                          @RequestParam(value = "keywords", required = false) String keywords) {
        return Result.ok(orgService.getCompanyTreeByKey(dataTime, keywords));
    }

    @ApiOperation("动态字段组织架构树列表")
    @PostMapping("/treePage")
    public Result treePage(@RequestBody OrgPageQueryDto queryDto) {
        PageListVo vo = PageListVo.bulid()
                .setColumns(orgService.getOrgMetadataProperty())
                .setItems(orgService.getTreeMapList(queryDto.getDateTime()));
        return Result.ok(vo);
    }

    @ApiOperation("查看组织变动记录")
    @GetMapping("/changeRecord")
    public Result<List<OrgChangeRecordVo>> queryChangeRecord(@RequestParam("bid") String bid) {
        List<OrgDo> list = orgService.selectOrgChangeRecord(bid);
        List<OrgChangeRecordVo> data = Lists.list();
        if (null != list && !list.isEmpty()) {
            list.forEach(item -> {
                OrgChangeRecordVo vo = ObjectConverter.convert(item, OrgChangeRecordVo.class);
                Optional entityRelationDto = item.fetchRelation("reportExtend");
                if (entityRelationDto.isPresent()) {
                    EntityRelationDto entiry = (EntityRelationDto) entityRelationDto.get();
                    vo.setReportExtends(entiry.getTargetIds());
                }
                data.add(vo);
            });
        }
        return Result.ok(data);
    }

    @ApiOperation("查看组织汇报关系变动记录")
    @GetMapping("/reportExtendChangeRecord")
    public Result<List<OrgReportExtendVo>> queryReportExtendChangeRecord(@RequestParam("recordIds") String recordIds,
                                                                         @RequestParam("dataTime") Long dataTime) {
        return Result.ok(ObjectConverter.convertList(orgService.queryReportExtendChangeRecord(recordIds, dataTime),
                OrgReportExtendVo.class));
    }

    @ApiOperation("获取当前租户所有生效组织信息")
    @PostMapping("/getOrgAllList")
    public Result getOrgAllList(@RequestBody OrgOrPostQueryDto queryDto) {
        return Result.ok(orgService.fetchAllOrgDo(queryDto));
    }

//    @ApiOperation("才到二期：下拉框：模糊查询负责人")
//    @GetMapping("/leader/query")
//    public Result<List<EmpSimple>> selectLeader(String param) {
//        List<OrgDataOutVo> orgDataOutVos = orgService.selectAllData();
//        List<EmpSimple> leaders = orgDataOutVos.stream().map(orgDataOutVo -> orgDataOutVo.getLeaderEmp()).filter(empSimple -> ObjectUtil.isNotEmpty(empSimple)).distinct().collect(Collectors.toList());
//
//        if (StringUtils.isNotEmpty(param)) {
//            List<EmpSimple> leaderSelect = leaders.stream().filter(empSimple -> (StringUtils.isNotEmpty(empSimple.getName()) && empSimple.getName().contains(param))
//                    || (StringUtils.isNotEmpty(empSimple.getWorkno()) && empSimple.getWorkno().contains(param))).collect(Collectors.toList());
//            return Result.ok(leaderSelect);
//        }
//
//        return Result.ok(leaders);
//    }
//
//    @ApiOperation("才到二期：下拉框：模糊查询hrbp")
//    @GetMapping("/hrbp/query")
//    public Result<List<EmpSimple>> selectHrbp(@RequestParam(value = "dataTime") Long dataTime,
//                                              @RequestParam(value = "param",required = false) String param) {
//        List<EmpSimple> hrbps = customOrgRoleDo.selectAllHrbpRole(dataTime)
//                .stream().filter(cor -> null != cor.getLeaderEmp() && StringUtil.isNotEmpty(cor.getLeaderEmp().getEmpId())).map(CustomOrgRoleDo::getLeaderEmp).distinct()
//                .collect(Collectors.toList());
//        if (StringUtils.isNotEmpty(param)) {
//            List<EmpSimple> hrbpSelect = hrbps.stream().filter(empSimple -> (
//                    StringUtils.isNotEmpty(empSimple.getName()) && empSimple.getName().contains(param))
//                    || StringUtils.isNotEmpty(empSimple.getWorkno()) && empSimple.getWorkno().contains(param)).collect(Collectors.toList());
//            return Result.ok(hrbpSelect);
//        }
//
//        return Result.ok(hrbps);
//    }
}
