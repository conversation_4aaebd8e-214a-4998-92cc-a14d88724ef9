spring:
 profiles:
  active: dev
 output:
   ansi:
     enabled: always

authScope:
 /api/hr/company/v1/list: entity.hr.Company
 /api/hr/org/v1/list: entity.hr.Org
 /api/hr/postmanage/v1/list: entity.hr.Post
 /api/hr/workplace/v1/list: entity.hr.Workplace
 /api/hr/emp/work/v1/getEmpStatisticsWithQuery: entity.hr.EmpWorkInfo
 /api/hr/emp/work/v1/list: entity.hr.EmpWorkInfo
 /api/hr/contract/v1/newlySignedList: entity.hr.EmpWorkInfo
 /api/hr/contract/v1/list: entity.hr.LastContract
 /api/hr/contract/v1/getApprovalRecords: entity.hr.Contract
 /api/hr/contract/v1/getContractRecords: entity.hr.Contract
 /api/hr/contract/type/set/v1/list: entity.hr.ContractTypeSet
 /api/hr/transfer/v1/list: ENTITY_HR_EMP_TRANSFER
 /api/hr/v1/termination/list: entity.hr.TerminationApply
 /api/hr/v1/termination/list/dynamic: entity.hr.TerminationApply
 /api/hr/v1/confirmation/todo: entity.hr.EmpWorkInfo
 /api/hr/contract/v1/contractList: entity.hr.Contract
 /api/hr/contract/v1/continue/list: entity.hr.LastContract
 /api/hr/emp/work/v1/page/config: entity.hr.EmpWorkInfo

rabbitmq:
 topics:
  - topic: EMP_ES_SYNC_BATCH
    exchange: hrpaas.es.exchange
    routingKey: routingKey.hrpaas.es.hr.
    queue: caidaocloud.hrpaas.sync.hr.es.
    exchangeType: DIRECT
    tenantIsolated: true
    consumersCount: 1
  - topic: ORG_SYNC_EMP_WORK_INFO
    exchange: hrpaas.es.exchange
    routingKey: routingKey.hrpaas.es.emporgsync.
    queue: caidaocloud.hrpaas.sync.emporgsync.es.
    exchangeType: DIRECT
    tenantIsolated: true
    consumersCount: 1
  - topic: POST_SYNC_EMP_WORK_INFO
    exchange: hrpaas.es.exchange
    routingKey: routingKey.hrpaas.es.postnamesync.
    queue: caidaocloud.hrpaas.sync.postnamesync.es.
    exchangeType: DIRECT
    tenantIsolated: true
    consumersCount: 1
  - topic: WORKPLACE_SYNC_EMP_WORK_INFO
    exchange: hrpaas.es.exchange
    routingKey: routingKey.hrpaas.es.workplacenamesync.
    queue: caidaocloud.hrpaas.sync.workplacenamesync.es.
    exchangeType: DIRECT
    tenantIsolated: true
    consumersCount: 1
  - topic: COST_SYNC_EMP_WORK_INFO
    exchange: hrpaas.es.exchange
    routingKey: routingKey.hrpaas.es.costnamesync.
    queue: caidaocloud.hrpaas.sync.costnamesync.es.
    exchangeType: DIRECT
    tenantIsolated: true
    consumersCount: 1
  - topic: JOB_SYNC_EMP_WORK_INFO
    exchange: hrpaas.es.exchange
    routingKey: routingKey.hrpaas.es.jobnamesync.
    queue: caidaocloud.hrpaas.sync.jobnamesync.es.
    exchangeType: DIRECT
    tenantIsolated: true
    consumersCount: 1
  - topic: JOB_GRADE_SYNC_EMP_WORK_INFO
    exchange: hrpaas.es.exchange
    routingKey: routingKey.hrpaas.es.jobgradenamesync.
    queue: caidaocloud.hrpaas.sync.jobgradenamesync.es.
    exchangeType: DIRECT
    tenantIsolated: true
    consumersCount: 1
  - topic: PAGE_DETAIL_CHANGE
    exchange: caidao.hrpaas
    routingKey: caidao.hrpaas.page.detail.change
    queue: caidaocloud.hr.page.detail.change
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 1
  - topic: DYNAMIC_COLUMN_SAVED_EMPLOYEE
    exchange: caidao.hrpaas
    routingKey: caidao.hrpaas.dynamic.column.employee
    queue: caidaocloud.hr.dynamic.column.employee
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 1
  - topic: DYNAMIC_COLUMN_SAVED_EMPLOYEECARD
    exchange: caidao.hrpaas
    routingKey: caidao.hrpaas.dynamic.column.employeecard
    queue: caidaocloud.hr.dynamic.column.employeecard
    exchangeType: DIRECT
    tenantIsolated: false
    consumersCount: 1
i18n:
  resource:
    path: i18n/hr/message
