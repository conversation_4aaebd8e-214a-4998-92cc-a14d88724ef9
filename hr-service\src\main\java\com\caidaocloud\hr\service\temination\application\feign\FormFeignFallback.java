package com.caidaocloud.hr.service.temination.application.feign;

import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.hr.service.temination.application.dto.FormDataDto;
import com.caidaocloud.hr.service.temination.application.dto.FormDataMapDto;
import com.caidaocloud.hr.service.temination.application.dto.FormDefDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class FormFeignFallback implements FormFeignClient {

    @Override
    public Result<List<FormDefDto>> getFormDefList(String formType, boolean implantable) {
        return Result.fail();
    }

    @Override
    public Result<FormDefDto> getFormDef(String name) {
        return Result.fail();
    }

    @Override
    public Result<FormDefDto> getFormDefById(String id) {
        return Result.fail();
    }

    @Override
    public Result<FormDataMapDto> getFormDataMap(String formId, String id) {
        return Result.fail();
    }

    @Override
    public JSONObject saveFormData(String formId, FormDataDto formDataDto) {
        return null;
    }

    @Override
    public Result update(String formId, FormDataDto formDataDto) {
        return Result.fail();
    }
}
