package com.caidaocloud.hr.service.transfer.application.feign;

import com.caidaocloud.hr.service.transfer.application.dto.auth.AuthResourceDto;
import com.caidaocloud.hr.service.transfer.application.feign.impl.AuthFeignClientImpl;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限服务feign
 *
 * <AUTHOR>
 * @date 2022/8/22
 **/
@FeignClient(value = "caidaocloud-auth-service", fallback = AuthFeignClientImpl.class, configuration = FeignConfiguration.class)
public interface AuthFeignClient {

    /**
     * 创建权限资源
     *
     * @param resourceList
     * @return
     */
    @PostMapping("/api/auth/v1/resources")
    Result<String> createResources(@RequestBody List<AuthResourceDto> resourceList);

    /**
     * 删除资源
     *
     * @param resourceCode
     * @return
     */
    @DeleteMapping("/api/auth/v1/resource")
    Result<String> deleteResource(@RequestParam("resourceCode") String resourceCode);

    /**
     * 更新资源
     *
     * @param resource
     * @return
     */
    @PutMapping("/api/auth/v1/resource")
    Result<String> updateResource(@RequestBody AuthResourceDto resource);

    @GetMapping("/api/auth/v1/subject/role/list")
    Result<List<Long>> getRoleIdsBySubject(@RequestParam("subjectId") Long subjectId);

    /**
     * 通过用户id获取用户权限code
     *
     * @param subjectId
     * @param parentCode
     * @return
     */
    @GetMapping("/api/auth/v1/subject/resource/code")
    Result<List<String>> getResourceCodeBySubjectId(@RequestParam("subjectId") Long subjectId, @RequestParam("parentCode") String parentCode);

}
