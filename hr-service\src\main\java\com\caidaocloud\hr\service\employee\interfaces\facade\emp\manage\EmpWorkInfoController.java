package com.caidaocloud.hr.service.employee.interfaces.facade.emp.manage;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.dto.EmpCostCenterDto;
import com.caidaocloud.hr.service.dto.growthrecord.GrowthRecordDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.*;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ExcelUtils;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpConcurrentPostLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportResp;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.*;
import com.caidaocloud.hr.service.enums.growthrecord.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.growthrecord.application.event.publish.GrowthRecordPublish;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.tag.application.tag.service.EmpTagInfoService;
import com.caidaocloud.hr.service.tag.interfaces.vo.EmpTagInfoVo;
import com.caidaocloud.hr.service.vo.EmpPageVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.*;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/hr/emp/work/v1")
@Api(value = "/api/hr/emp/work/v1", description = "员工任职和基本信息", tags = "v1.4")
public class EmpWorkInfoController {
    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private EmpBasicInfoService empBasicInfoService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Autowired
    private GrowthRecordPublish growthRecordPublish;
    @Resource
    private EmpConcurrentPostService empConcurrentPostService;
    @Resource
    private EmpTagInfoService empTagInfoService;
    @Resource
    private OrgService orgService;

    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    @ApiOperation("查询员工统计信息")
    @PostMapping("/getEmpStatistics")
    public Result getEmpStatistics() {
        return ResponseWrap.wrapResult(empWorkInfoService.getEmpStatistics());
    }

    @ApiOperation("查询员工统计信息")
    @PostMapping("/getEmpStatisticsWithQuery")
    public Result getEmpStatisticsQueryParam(@RequestBody EmpPageQueryDto dto) {
        if (dto.getDateTime() == null) {
            return Result.fail("时间参数不能为空");
        }
        return ResponseWrap.wrapResult(empWorkInfoService.getEmpStatisticsFromEs(dto));
    }

    @ApiOperation("查询员工列表")
    @PostMapping("/list")
    public Result<PageResult<EmpPageVo>> getEmpInfoList(@RequestBody EmpPageQueryDto dto) {
        return ResponseWrap.wrapResult(empWorkInfoService.getEmpPageListFromEs(dto));
    }


    @ApiOperation("查询员工任职信息详情")
    @GetMapping("/detail")
    public Result<EmpWorkInfoVo> getEmpWorkInfo(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime) {
        EmpWorkInfoDo workInfoDo = empWorkInfoService.getEmpWorkInfo(empId, dataTime);
        if (workInfoDo == null) {
            return Result.ok(new EmpWorkInfoVo());
        }

        EmpWorkInfoVo workInfoVo = ObjectConverter.convert(workInfoDo, EmpWorkInfoVo.class);
        if (StringUtil.isNotEmpty(workInfoDo.getCostCenters())) {
            workInfoVo.setCostCenters(FastjsonUtil.toArrayList(workInfoDo.getCostCenters(), EmpCostCenterDto.class));
        }

        // DEV-5215
        if (null != workInfoVo.getLeadEmpId() && StringUtil.isEmpty(workInfoVo.getLeadEmpId().getEmpId())) {
            workInfoVo.setLeadEmpId(null);
        }

        // 自定义字段查询
        Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(workInfoDo.getDoIdentifier(), workInfoDo);
        workInfoVo.setExt(ext);

        if(!"enabled".equals(postTxtShowCode)){
            String postTxt = workInfoVo.getPostTxt();
            if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
                postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                workInfoVo.setPostTxt(postTxt);
            }
        }

        List<EmpTagInfoVo> allEmpTagInfo = empTagInfoService.getEmpTagInfo(empId, System.currentTimeMillis(), true);
        workInfoVo.setTagIds(allEmpTagInfo.stream().map(o -> o.getTagBid()).collect(Collectors.joining(",")));
        return Result.ok(workInfoVo);
    }

    @ApiOperation("查询员工任职信息时间轴")
    @GetMapping("/timeLine")
    public Result getTimeLine(@RequestParam("empId") String empId) {
        List<EmpWorkInfoDo> workInfoDos = empWorkInfoService.getTimeLine(empId);
        if (workInfoDos == null) {
            return Result.ok();
        }
        List<Long> timeLines = workInfoDos.stream().map(o -> o.getDataStartTime()).collect(Collectors.toList());

        return Result.ok(timeLines);
    }

    @ApiOperation("查看任职信息分页")
    @PostMapping("/page")
    public Result<PageResult<EmpWorkInfoVo>> getEmpWorkInfoPage(@RequestBody EmpPageQueryDto queryDto) {
        return Result.ok(empWorkInfoService.getWorkInfoPage(queryDto));
    }

    @ApiOperation("查看任职信息es分页")
    @PostMapping("/workInfo/page")
    public Result<PageResult<EmpWorkInfoVo>> getEmpWorkInfoPageEs(@RequestBody EmpPageQueryDto queryDto) {
        return Result.ok(empWorkInfoService.getWorkInfoPageFromEs(queryDto));
    }

    @ApiOperation("查询Hrbp员工任职信息详情")
    @GetMapping("/hrbp")
    public Result<EmpWorkInfoVo> getOrganizeHrbp(@RequestParam("organize") String organize,
                                                 @RequestParam("dataTime") Long dataTime) {
        EmpWorkInfoDo workInfoDo = empWorkInfoService.getOrganizeHrbp(organize, dataTime);
        if (workInfoDo == null) {
            return Result.ok(new EmpWorkInfoVo());
        }

        EmpWorkInfoVo workInfoVo = ObjectConverter.convert(workInfoDo, EmpWorkInfoVo.class);
        if (StringUtil.isNotEmpty(workInfoDo.getCostCenters())) {
            workInfoVo.setCostCenters(FastjsonUtil.toArrayList(workInfoDo.getCostCenters(), EmpCostCenterDto.class));
        }
        return Result.ok(workInfoVo);
    }

    @ApiOperation("查询Hrbp员工任职信息详情")
    @GetMapping("/allLeader")
    public Result<List<EmpWorkInfoVo>> getAllOrganizeLeader(@RequestParam("dataTime") Long dataTime) {
        List<EmpWorkInfoDo> workInfoDoList = empWorkInfoService.getAllOrganizeLeader(dataTime);
        if (workInfoDoList == null || workInfoDoList.size() == 0) {
            return Result.ok(new ArrayList<>());
        }

        List<EmpWorkInfoVo> workInfoVoList = ObjectConverter.convertList(workInfoDoList, EmpWorkInfoVo.class);
        return Result.ok(workInfoVoList);
    }

    @ApiOperation("查询Hrbp员工任职信息详情")
    @GetMapping("/allHrbp")
    public Result<List<EmpWorkInfoVo>> getAllOrganizeHrbp(@RequestParam("dataTime") Long dataTime) {
        List<EmpWorkInfoDo> workInfoDoList = empWorkInfoService.getAllOrganizeHrbp(dataTime);
        if (workInfoDoList == null || workInfoDoList.size() == 0) {
            return Result.ok(new ArrayList<>());
        }

        List<EmpWorkInfoVo> workInfoVoList = ObjectConverter.convertList(workInfoDoList, EmpWorkInfoVo.class);
        return Result.ok(workInfoVoList);
    }

    @ApiOperation("根据员工id查询Hrbp员工任职信息详情")
    @GetMapping("/hrbpByEmpId")
    public Result<EmpWorkInfoVo> getHrbpByEmpId(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime) {
        EmpWorkInfoDo empWorkInfo = empWorkInfoService.getHrbpByEmpId(empId, dataTime);
        return Result.ok(ObjectConverter.convert(empWorkInfo, EmpWorkInfoVo.class));
    }

    @ApiOperation("根据员工id查询部门Hrbp员工任职信息详情")
    @GetMapping("/leaderByEmpId")
    public Result<EmpWorkInfoVo> getLeaderByEmpId(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime) {
        EmpWorkInfoDo empWorkInfo = empWorkInfoService.getLeaderByEmpId(empId, dataTime);
        return Result.ok(ObjectConverter.convert(empWorkInfo, EmpWorkInfoVo.class));
    }

    @ApiOperation("根据员工id查询部门领导人员工任职信息详情")
    @GetMapping("/orgLeaderByEmpId")
    public Result<EmpWorkInfoVo> getOrgLeaderByEmpId(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime) {
        EmpWorkInfoDo empWorkInfo = empWorkInfoService.getOrgLeaderByEmpId(empId, dataTime);
        return Result.ok(ObjectConverter.convert(empWorkInfo, EmpWorkInfoVo.class));
    }

    @ApiOperation("新增员工")
    @PostMapping("/save")
    @LogRecordAnnotation(category = "新增", success = "新增了{{#name}}({{#workno}})", menu = "人事-员工信息", condition = "{{#condition}}")
    public Result save(@RequestBody EmpInfoDto empInfoDto) {
        LogRecordContext.putVariable("name", empInfoDto.getEmpPrivateInfo().getName());
        LogRecordContext.putVariable("workno", empInfoDto.getEmpWorkInfo().getWorkno());
        String empId = empBasicInfoService.save(empInfoDto);
        saveGrowthRecord(empId, empInfoDto.getEmpWorkInfo().getDataStartTime());
        LogRecordContext.putVariable("condition", true);
        return Result.ok(true);
    }

    @ApiOperation("编辑员工任职信息")
    @PostMapping("/update")
    @LogRecordAnnotation(category = "编辑", success = "将{{#name}}({{#workno}})的{{#change}}", menu = "人事-员工信息-任职信息")
    public Result update(@RequestBody ReportLineDto reportLineDto) {
//        empWorkInfoService.updatePostAndReportLine(reportLineDto);
        SpringUtil.getBean(EmpDataRangeService.class).updatePostAndReportLineV2(reportLineDto);
        return Result.ok(reportLineDto.getWorkInfo());
    }

    private void saveGrowthRecord(String empId, Long dataStartTime) {
        dataStartTime = null == dataStartTime ? System.currentTimeMillis() : dataStartTime;
        EmpWorkInfoDo empWorkInfo = empWorkInfoService.getEmpWorkInfo(empId, dataStartTime);
        NestPropertyValue properties = empWorkInfo.getProperties();
        GrowthRecordDto growthRecordDto = new GrowthRecordDto();
        growthRecordDto.setTenantId(UserContext.getTenantId());
        growthRecordDto.setEmpId(empId);
        growthRecordDto.setBusinessEventType(BusinessEventTypeEnum.ONBOARDING.toString());

        growthRecordDto.setCreateBy(UserContext.getUserId());
        growthRecordDto.setEffectiveDate(empWorkInfo.getHireDate());
        ArrayList<GrowthRecordDto.DataItem> dataList = new ArrayList<>();
        //记录数据变化情况
        properties.entrySet().stream().forEach(entry -> {
            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
            String propKey = entry.getKey();
            dataItem.setProp(propKey);
            if (properties.get(propKey) instanceof SimplePropertyValue) {
                dataItem.setNewValue(((SimplePropertyValue) properties.get(propKey)).getValue());
            } else if (properties.get(propKey) instanceof DictSimple) {
                dataItem.setNewValue(((DictSimple) properties.get(propKey)).getValue());
            } else if (properties.get(propKey) instanceof EnumSimple) {
                dataItem.setNewValue(((EnumSimple) properties.get(propKey)).getValue());
            } else if (properties.get(propKey) instanceof Address) {
                dataItem.setNewValue(((Address) properties.get(propKey)).doText());
            } else if (properties.get(propKey) instanceof EmpSimple) {
                dataItem.setNewValue(((EmpSimple) properties.get(propKey)).getEmpId());
            } else if (properties.get(propKey) instanceof JobGradeRange) {
                dataItem.setNewValue(((JobGradeRange) properties.get(propKey)).getStartGrade());
            } else if (properties.get(propKey) instanceof ComponentPropertyValue) {
                log.warn("propKey:{},propType:{},value:{}", propKey, properties.get(propKey).getClass(), properties.get(propKey));
            } else {
                log.warn("[其他类型]propKey:{},propType:{},value:{}", propKey, properties.get(propKey) == null ? null : properties.get(propKey).getClass(), properties.get(propKey));
            }
            dataList.add(dataItem);
        });

        growthRecordDto.setDataList(dataList);
        growthRecordPublish.publish(growthRecordDto);
    }

    @ApiOperation("上传头像")
    @PostMapping("/upload")
    public Result upload(@RequestBody PhotoUploadDto dto) {
        empWorkInfoService.photoUpload(dto);
        return Result.ok(true);
    }

    @ApiOperation("根据员工公司邮箱获取员工ID")
    @PostMapping("/empList")
    public Result getEmpList(@RequestBody List<String> companyEmailList) {
        if (null == companyEmailList || companyEmailList.isEmpty()) {
            return Result.ok(Lists.newArrayList());
        }

        List<EmpWorkInfoDo> empList = empWorkInfoService.getEmpList(companyEmailList);
        List<EmpWorkInfoVo> empListVo = ObjectConverter.convertList(empList, EmpWorkInfoVo.class);
        return Result.ok(empListVo);
    }

    @ApiOperation("根据员工工号获取员工ID")
    @PostMapping("/getEmpListByWorkno")
    public Result getEmpListByWorkno(@RequestBody List<String> worknoList) {
        if (null == worknoList || worknoList.isEmpty()) {
            return Result.ok(Lists.newArrayList());
        }

        List<EmpWorkInfoDo> empList = empWorkInfoService.getEmpListByWorkno(worknoList);
        List<EmpWorkInfoVo> empListVo = ObjectConverter.convertList(empList, EmpWorkInfoVo.class);
        return Result.ok(empListVo);
    }

    @ApiOperation("查询员工信息字段值")
    @PostMapping("/searchEmpColumnsPage")
    public Result searchEmpColumnsPage(@RequestBody EmpSearchColumnsDto empSearchColumnsDto) {

        PageResult<Map<String, String>> pageResult = empWorkInfoService.queryEmpColumnsPage(empSearchColumnsDto);
        return Result.ok(pageResult);
    }


    @GetMapping("/getEmpByExt")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userName", value = "用户名-账号扩展字段", dataType = "string", paramType = "query")
    })
    @ApiOperation("根据扩展字段获取员工信息")
    public Result<EmpWorkInfoDo> getEmpWorkInfoByExt(@ApiParam(hidden = true) @RequestParam Map<String, String> extMap) {
        EmpWorkInfoDo empWorkInfoDo = empWorkInfoService.getEmpListByExt(extMap);
        return empWorkInfoDo != null ? Result.ok(empWorkInfoDo) : Result.fail("获取失败");
    }

    @ApiOperation("根据员工工号获取员工ID")
    @PostMapping("/getEmpListByEmpIds")
    public Result<List<EmpWorkInfoVo>> getEmpListByEmpIds(@RequestBody List<String> empIds) {
        if (null == empIds || empIds.isEmpty()) {
            return Result.ok(Lists.newArrayList());
        }

        List<EmpWorkInfoDo> empList = empWorkInfoService.getEmpWorkInfoByEmpIds(empIds);
        List<EmpWorkInfoVo> empListVo = ObjectConverter.convertList(empList, EmpWorkInfoVo.class);
        return Result.ok(empListVo);
    }

    @ApiOperation("获取所有员工信息")
    @PostMapping("/getAllEmpList")
    public Result getAllEmpList() {
        List<EmpWorkInfoDo> empList = empWorkInfoService.getAllEmpList();
        List<EmpWorkInfoVo> empListVo = ObjectConverter.convertList(empList, EmpWorkInfoVo.class);
        return Result.ok(empListVo);
    }
    @ApiOperation("导出员工列表")
    @PostMapping("/exportEmpList")
    public void exportNewlySignedList(@RequestBody EmpPageQueryDto dto, HttpServletResponse response) {
        PageResult<EmpPageVo> page = empWorkInfoService.getEmpPageListFromExport(dto);
        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : empWorkInfoService.installNewlySignedExportProperty()) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(tagProperty.getPropertyTxt(), tagProperty.getProperty(), 15);
            exprortEntity.setOrderNum(tagProperty.getOrder());
            colList.add(exprortEntity);
        }
        List<Map<String, Object>> dataList = org.apache.commons.collections.CollectionUtils.isEmpty(page.getItems()) ? Lists.newArrayList() : installNewlySignedDataList(page.getItems(), colList);
        try {
            ExcelUtils.downloadDataMapExcel(colList, dataList, "员工信息", response);
        } catch (Exception e) {
            log.error("download NewlySignedList excel error.{}", e.getMessage(), e);
        }
    }

    private List<Map<String, Object>> installNewlySignedDataList(List<EmpPageVo> items, List<ExcelExportEntity> colList) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (EmpPageVo vo : items) {
            Map<String, Object> map = new HashMap<>();
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(vo));
            for (ExcelExportEntity entity : colList) {
                map.put(entity.getKey().toString(), json.get(entity.getKey()));
            }
            dataList.add(map);
        }
        return dataList;
    }
    @ApiOperation("批量查询员工任职信息详情")
    @GetMapping("/empWorkInfoByEmpIds")
    public Result<List<EmpWorkInfoVo>> empWorkInfoByEmpIds(@RequestParam("empIds") String empIds) {
        List<EmpWorkInfoDo> workInfoDoList = empWorkInfoService.getEmpWorkInfoByEmpIds(empIds);
        if (workInfoDoList == null || workInfoDoList.size() == 0) {
            return Result.ok(new ArrayList<>());
        }

        List<EmpWorkInfoVo> workInfoVoList = ObjectConverter.convertList(workInfoDoList, EmpWorkInfoVo.class);
        return Result.ok(workInfoVoList);
    }

    @ApiOperation("全量比较数据库数据")
    @GetMapping("/syncDataFromDb")
    public Result<String> syncDataFromDb(@RequestParam("dataTime") Long dataTime) {
        int count = empWorkInfoService.syncDataFromDb(dataTime);
        return Result.ok(String.valueOf(count));
    }

    @ApiOperation("同步单员工数据到ES")
    @GetMapping("/syncEmpFromDb")
    public Result syncEmpFromDb(@RequestParam("empId") String empId) {
        empWorkInfoService.syncEmpFromDb(empId);
        return Result.ok();
    }

    @PostMapping("/deleteEmpByIds")
    @ApiOperation("删除员工")
    @LogRecordAnnotation(category = "删除", success = "删除了{{#content}}", menu = "人事-员工信息")
    public Result deleteWorkInfo(@RequestParam String empIds) {
        List<String> empIdList = Arrays.asList(empIds.split(","));
        empWorkInfoService.deleteEmpByEmpIds(empIdList);
        return Result.ok(true);
    }

    @PostMapping("/getReportEmpList")
    @ApiOperation("主岗查询下级任职兼岗和组织信息")
    public Result getReportEmpList(@RequestBody EmpReportLeaderDto empReportLeaderDto) {
        return Result.ok(SpringUtil.getBean(EmpDataRangeService.class).checkReportLeader(empReportLeaderDto));
    }

    @PostMapping("/getConcurrentPostReportEmpList")
    @ApiOperation("兼岗查询下级任职兼岗和组织信息")
    public Result getConcurrentPostReportEmpList(@RequestBody EmpConcurrentPostLeaderDto empConcurrentPostDto) {
        if (empConcurrentPostDto == null || StringUtils.isEmpty(empConcurrentPostDto.getEmpId()) || StringUtils.isEmpty(empConcurrentPostDto.getOrganizeId()) || StringUtils.isEmpty(empConcurrentPostDto.getPostId())) {
            return Result.fail("兼岗查询下级任职兼岗和组织信息参数有误！");
        }
//        try {
//            empConcurrentPostDto.setEndTime(DateUtil.convert(DateUtil.formatDate(empConcurrentPostDto.getEndTime()), "yyyy-MM-dd"));
//        } catch (ParseException e) {
//            e.printStackTrace();
//            return Result.fail("兼岗结束时间精确到天处理异常！");
//        }
        EmpReportResp empReportResp = new EmpReportResp();
        List<EmpReportResp.EmpReportInfo> empReportInfoList = Lists.newArrayList();
        List<EmpReportResp.EmpOrgInfo> empOrgInfoList = Lists.newArrayList();
        // 兼岗结束时间 > 今天,则不查询
        if (empConcurrentPostDto.getEndTime() != null && empConcurrentPostDto.getEndTime() > System.currentTimeMillis()) {
            return Result.ok(empReportResp);
        }
        //结束时间取下一天的0秒
        if (empConcurrentPostDto.getEndTime() != null) {
            try {
                empConcurrentPostDto.setEndTime(DateUtil.convert(DateUtil.formatDate(empConcurrentPostDto.getEndTime()), "yyyy-MM-dd") + 24 * 60 * 60 * 1000L);
            } catch (ParseException e) {
                log.error(e.getMessage(), e);
            }
        }

        //作为主岗找下级员工
        List<EmpWorkInfoDo> empWorkInfoDoList = empWorkInfoService.getEmpWorkInfoByEmpConcurrentPostLeaderDto(empConcurrentPostDto, -1);
        log.info("getConcurrentPostReportEmpList,empWorkInfoDoList:{} ", FastjsonUtil.toJson(empWorkInfoDoList));
        empReportResp.buildEmpWorkInfo(empWorkInfoDoList, empReportInfoList, true);

        //作为兼岗找下级员工
        List<EmpConcurrentPostDo> empConcurrentPostListByLeader = empConcurrentPostService.getListByEmpConcurrentPostLeader(empConcurrentPostDto);
        log.info("getConcurrentPostReportEmpList,empConcurrentPostListByLeader:{} ", FastjsonUtil.toJson(empConcurrentPostListByLeader));
        SpringUtil.getBean(EmpDataRangeService.class).buildEmpConcurrentPostDo(empConcurrentPostListByLeader, empReportInfoList, false);

        empReportResp.setEmpReportInfoList(empReportInfoList);

        //组织
        List<OrgDo> orgListByLeader = orgService.getOrgListByEmpConcurrentPostLeader(empConcurrentPostDto, -1L);
        empReportResp.buildOrgInfo(orgListByLeader, empOrgInfoList);
        empReportResp.setEmpOrgInfoList(empOrgInfoList);
        return Result.ok(empReportResp);
    }

    @ApiOperation("获取页面detail")
    @GetMapping("/page/config")
    public Result<EmpPageConfigDto> pageConfig(@RequestParam(required = false) String empId,
                                               @RequestParam(required = false, defaultValue = "page.detail.employee") String pageId) {
        return Result.ok(empWorkInfoService.getPageConfig(empId, pageId));
    }

}
