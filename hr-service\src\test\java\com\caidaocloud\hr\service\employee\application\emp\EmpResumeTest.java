package com.caidaocloud.hr.service.employee.application.emp;

import com.caidaocloud.hr.service.employee.application.emp.dto.resume.EmpResumeConfigDto;
import com.caidaocloud.hr.service.employee.application.emp.dto.resume.EmpResumePropDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpResumeService;
import com.caidaocloud.hr.service.search.infrastructure.repository.EmpSearchInfoRepository;
import com.caidaocloud.hr.service.search.infrastructure.repository.po.EmpSearchInfoPo;
import com.caidaocloud.hr.service.tag.application.tag.service.EmpTagInfoService;
import com.caidaocloud.hr.service.tag.interfaces.dto.EmpTagInfoBatchDto;
import com.caidaocloud.oss.dto.UploadResult;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.PropUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StringValueResolver;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class EmpResumeTest {
    @Before
    public void initUserInfo() {
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        securityUserInfo.setTenantId("11");
        securityUserInfo.setUserId(0L);
        securityUserInfo.setEmpId(0L);
        securityUserInfo.setIsAdmin(false);
        SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
    }

    @Test
    public void testTags(){
        String json = "{\"tagBid\":\"1965399295776768\",\"empIds\":[\"1974580455643136\"]}\n";
        EmpTagInfoBatchDto dti = FastjsonUtil.toObject(json, EmpTagInfoBatchDto.class);
        SpringUtil.getBean(EmpTagInfoService.class).tagTagAndEmps(dti);
        System.out.println("------");
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("empId", "1974580455643136"))
                    .must(QueryBuilders.matchQuery("tenantId", "11"));
            List<EmpSearchInfoPo> pageEs = SpringUtil.getBean(EmpSearchInfoRepository.class).search(boolQuery);
            System.out.println(pageEs);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testRule(){
        EmpResumeConfigDto erc = new EmpResumeConfigDto();
        erc.setTenantId("11");
        erc.setId(1L);
        //erc.setTemplate("/Users/<USER>/Downloads/resume_template.docx");
        erc.setTemplate("/caidao/de87tegedecfe0dc7d4c-resume_template.docx");
        List<EmpResumePropDto> configs = new ArrayList<>();
        EmpResumePropDto erp = new EmpResumePropDto();
        erp.setAlias("a");
        erp.setIdentifier("entity.hr.EmpWorkInfo");
        erp.setTimeline(true);
        configs.add(erp);

        erp = new EmpResumePropDto();
        erp.setAlias("b");
        erp.setIdentifier("entity.hr.EmpPrivateInfo");
        configs.add(erp);

        erp = new EmpResumePropDto();
        erp.setAlias("edus");
        erp.setIdentifier("entity.hr.EmpEduExp");
        erp.setMultiple(true);
        configs.add(erp);

        erp = new EmpResumePropDto();
        erp.setAlias("ewes");
        erp.setIdentifier("entity.hr.EmpWorkExperience");
        erp.setMultiple(true);
        configs.add(erp);

        erp = new EmpResumePropDto();
        erp.setAlias("c");
        erp.setIdentifier("entity.hr.EmpWorkOverview");
        erp.setPostMethod("calcWorkAge");
        configs.add(erp);

        erp = new EmpResumePropDto();
        erp.setAlias("d");
        erp.setIdentifier("EmpCurrentContract");
        configs.add(erp);

        erc.setConfigs(configs);
        System.out.println("------");
        System.out.println(FastjsonUtil.toJson(erc));
        SpringUtil.getBean(EmpResumeService.class).save(erc);
    }

    @Test
    public void testGet(){
        Map<String, Object> empResumeData =
                SpringUtil.getBean(EmpResumeService.class)
                        .getEmpResumeData("1898806405814273", 1715097600000L);
        System.out.println(FastjsonUtil.toJson(empResumeData));
    }

    @Test
    public void testCreate(){
        SpringUtil.getBean(EmpResumeService.class)
            .doEmpResume("1898806405814273", 1715097600000L,
            null, "/Users/<USER>/Downloads/hell1.docx",
            "/Users/<USER>/Downloads/resume_template.docx", 0);
        System.out.println("-----");
    }

    @Test
    public void testUpload(){
        UploadResult uploadResult = SpringUtil.getBean(EmpResumeService.class)
                .wordFile("1898806405814273", 1715097600000L, "/Users/<USER>/Downloads/resume_template.docx");
        System.out.println("-----testUpload---------");
        System.out.println();
        System.out.println();
        log.info("-----uploadResult={}", FastjsonUtil.toJson(uploadResult));
    }

    @Test
    public void testYml(){
        StringValueResolver bean = SpringUtil.getBean(StringValueResolver.class);
        PropUtil bean1 = SpringUtil.getBean(PropUtil.class);
        bean1.setEmbeddedValueResolver(bean);

        String prop = PropUtil.getProp("a.b.c");
        System.out.println(prop);
        prop = PropUtil.getProp("a.b.d");
        System.out.println(prop);
    }

    @Test
    public void testWordFile(){
        UploadResult uploadResult = SpringUtil.getBean(EmpResumeService.class)
                .wordFile("1923578806597638", 1717603200000L, null);
        System.out.println("-----testUpload---------");
        System.out.println();
        System.out.println();
        log.info("-----uploadResult={}", FastjsonUtil.toJson(uploadResult));
    }
}
