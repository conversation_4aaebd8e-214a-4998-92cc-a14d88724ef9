package com.caidaocloud.hr.service.employee.domain.emp.manage.service;

import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpCertificateDo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class EmpCertificateDomainService {
    @Resource
    private EmpCertificateDo empCertificateDo;

    public List<EmpCertificateDo> getEmpCertificateList(String empId) {
        return empCertificateDo.selectListByEmpId(empId);
    }

    public EmpCertificateDo getDetail(String bid) {
        return empCertificateDo.selectById(bid);
    }

    public String save(EmpCertificateDo data) {
        return empCertificateDo.save(data);
    }

    public void update(EmpCertificateDo data) {
        empCertificateDo.update(data);
    }

    public void delete(String bid) {
        empCertificateDo.delete(bid);
    }
}
