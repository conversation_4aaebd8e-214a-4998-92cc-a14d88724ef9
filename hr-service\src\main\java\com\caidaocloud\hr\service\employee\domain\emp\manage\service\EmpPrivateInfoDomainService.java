package com.caidaocloud.hr.service.employee.domain.emp.manage.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.common.tool.SnowUtil;
import com.caidaocloud.hr.service.employee.application.feign.oboarding.service.OnboardingService;
import com.caidaocloud.hr.core.feign.UserFeignClient;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpInfoImportDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPrivacyQueryDto;
import com.caidaocloud.hr.core.dto.SyncOnBoarDingDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpIdentificationVo;
import com.caidaocloud.hr.service.search.application.service.EmpWorkInfoSearchService;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpPrivateInfoDomainService {
    private static final String COMPLETE = "COMPLETE";

    @Resource
    private EmpPrivateInfoDo empPrivateInfoDo;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private EmpWorkInfoSearchService empWorkInfoSearchService;
    @Resource
    private EmpBasicInfoDo empBasicInfoDo;
    @Resource
    private EmpWorkInfoDo empWorkInfoDo;
    @Resource
    private OnboardingService onboardingService;
    @Resource
    private UserFeignClient userFeignClient;

    @Value("${caidaocloud.import.checkOnboarding:false}")
    private boolean checkOnBoarding;
    @Value("${caidaocloud.import.checkProps:}")
    private String checkProps;

    /**
     * 获取候选人在onBoarDing的empId 如果没有获取到则雪花ID
     */
    public void getEmpAtOnBoarDingEmpId(List<EmpInfoImportDo> errorList, List<EmpInfoImportDo> empPrivateInfoDoList) {
        log.info("import add emp getEmpAtOnBoarDingEmpId checkOnBoarding:{}", checkOnBoarding);
        log.info("import add emp getEmpAtOnBoarDingEmpId checkProps:{}", checkProps);

        if (checkOnBoarding && StringUtils.isNotBlank(checkProps)) {
            /**
             * 入职完成的导入新增员工 调用更新转正用户接口
             */
            List<SyncOnBoarDingDto> entryProcessSuccessList = new ArrayList<>();

            EmpPrivacyQueryDto queryDto = new EmpPrivacyQueryDto();
            Boolean phone = checkProps.contains("phone");
            Boolean name = checkProps.contains("empName");
            Boolean cardNo = checkProps.contains("cardNo");
            if (phone) {
                List<String> collect = empPrivateInfoDoList.stream().filter(emp -> null != emp.getPhone())
                        .map(emp -> emp.getPhone().getValue()).collect(Collectors.toList());
                queryDto.setPhoneList(collect);
            }
            if (name) {
                queryDto.setEmpNameList(empPrivateInfoDoList.stream().map(EmpInfoImportDo::getName).collect(Collectors.toList()));
            }
            if (cardNo) {
                queryDto.setCardNoList(empPrivateInfoDoList.stream().map(EmpInfoImportDo::getCardNo).collect(Collectors.toList()));
            }
            List<EmpIdentificationVo> empProcessInfoList = onboardingService.getEmpProcessInfoList(queryDto);
            for (EmpInfoImportDo empInfo : empPrivateInfoDoList) {
                Integer notSuccessCount = 0;
                for (EmpIdentificationVo empIdentificationVo : empProcessInfoList) {
                    // 匹配条件完成 并且 入职完成
                    boolean success = false;
                    // 匹配条件完成 但是 非入职完成状态
                    boolean notSuccess = false;
                    boolean joinSuccess = false;
                    if (phone && name && cardNo) {
                        joinSuccess = empInfo.getPhone().equals(empIdentificationVo.getPhone()) && empInfo.getName().equals(empIdentificationVo.getName()) && empInfo.getCardNo().equals(empIdentificationVo.getCardNo());
                    }
                    if (phone && name) {
                        joinSuccess = empInfo.getPhone().equals(empIdentificationVo.getPhone()) && empInfo.getName().equals(empIdentificationVo.getName());
                    }
                    if (phone && cardNo) {
                        joinSuccess = empInfo.getPhone().equals(empIdentificationVo.getPhone()) && empInfo.getCardNo().equals(empIdentificationVo.getCardNo());
                    }
                    if (name && cardNo) {
                        joinSuccess = empInfo.getName().equals(empIdentificationVo.getName()) && empInfo.getCardNo().equals(empIdentificationVo.getCardNo());
                    }
                    if (phone) {
                        joinSuccess = empInfo.getPhone().equals(empIdentificationVo.getPhone());
                    }
                    if (cardNo) {
                        joinSuccess = empInfo.getCardNo().equals(empIdentificationVo.getCardNo());
                    }
                    if (joinSuccess) {
                        success = COMPLETE.equals(empIdentificationVo.getStatus().getValue());
                        notSuccess = !success;
                    }
                    if (success) {
                        empInfo.setEmpId(empIdentificationVo.getEmpId());
                        break;
                    }
                    if (notSuccess) {
                        notSuccessCount++;
                    }
                }
                if (empInfo.getEmpId() == null && notSuccessCount > 0) {
                    log.info("import add emp :{} notEntrySuccessCount:{}", empInfo.getName(), notSuccessCount);
                    empInfo.setCheckEmpty(true);
                    empInfo.setCheckEmptyTips(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32025));
                    errorList.add(empInfo);
                } else if (empInfo.getEmpId() != null) {
                    log.info("import add emp :{}  empId At OnBoarDing:{}", empInfo.getName(), empInfo.getEmpId());
                    entryProcessSuccessList.add(getSyncOnBoarDingDtoByEmpImportDo(empInfo, UserContext.getTenantId()));
                } else {
                    empInfo.setEmpId(Optional.ofNullable(empInfo.getEmpId()).orElse(SnowUtil.nextId()));
                }
            }
            log.info("emp import batchSyncOfficial :{}", FastjsonUtil.toJson(entryProcessSuccessList));
            userFeignClient.batchSyncOfficial(entryProcessSuccessList);
        }
    }

    public void createOrUpdUser(List<SyncOnBoarDingDto> createObBoarDingUserList) {
        log.info("sync emp import entry to batchSyncOnBoarDing mobNumS: {}", createObBoarDingUserList.stream().map(SyncOnBoarDingDto::getMobNum).collect(Collectors.toList()));
        try {
            userFeignClient.batchSyncOnBoarDing(createObBoarDingUserList);
        } catch (Exception e) {
            log.error("sync emp import entry to batchSyncOnBoarDing error: {}", e.getMessage(), e);
        }
    }

    public SyncOnBoarDingDto getSyncOnBoarDingDtoByEmpImportDo(EmpInfoImportDo empImportDo, String tenantId) {
        return new SyncOnBoarDingDto(
                empImportDo.getPhone(),
                Long.valueOf(tenantId),
                empImportDo.getName(),
                Long.valueOf(empImportDo.getEmpId()));
    }

    public List<EmpPrivateInfoDo> getEmpPrivateInfoListByEmpId(List<String> empIds) {
        return empPrivateInfoDo.getListByEmpId(empIds);
    }

    public List<EmpPrivateInfoDo> getAllEmpPrivateInfoListByEmpId(List<String> empIds) {
        return empPrivateInfoDo.getAllListByEmpId(empIds);
    }

    public void deleteByEmpId(String empId) {
        List<EmpPrivateInfoDo> empPrivateInfoDoList = empPrivateInfoDo.getAllEmpPrivateInfoByEmpIds(Lists.newArrayList(empId));
        if (CollectionUtils.isNotEmpty(empPrivateInfoDoList)) {
            empPrivateInfoDo.batchDeleteByIds(empPrivateInfoDoList.stream().map(EmpPrivateInfoDo::getBid).collect(Collectors.toList()));
        }
    }

    public List<EmpPrivateInfoDo> getAllEmpPrivateInfoListByPhone(List<String> phoneS) {
        return empPrivateInfoDo.getAllListByPhones(phoneS);
    }

    public List<EmpPrivateInfoDo> getAllEmpPrivateInfoListByCardNo(List<String> cardNos) {
        return empPrivateInfoDo.getAllListByCardNos(cardNos);
    }

    public String save(EmpPrivateInfoDo data) {
        return empPrivateInfoDo.save(data);
    }

    public void update(EmpPrivateInfoDo data) {
        empPrivateInfoDo.update(data);

        Long dataTime = DateUtil.getCurrentTimestamp();
        // 同步修改姓名
        EmpBasicInfoDo basicInfo = empBasicInfoDo.getEmpBasicInfo(data.getEmpId(), dataTime);
        if (null != basicInfo && (!Objects.equals(basicInfo.getName(), data.getName()) ||
                !Objects.equals(basicInfo.getEnName(), data.getEnName()) ||
                !Objects.equals(basicInfo.getPhone(), data.getPhone())
                || (null != basicInfo.getSex() && !Objects.equals(basicInfo.getSex().getValue(), null == data.getSex() ? "" : data.getSex().getValue()))
                || (null != data.getSex() && !Objects.equals(data.getSex().getValue(), null == basicInfo.getSex() ? "" : basicInfo.getSex().getValue())))) {
            basicInfo.setName(data.getName());
            basicInfo.setEnName(data.getEnName());
            basicInfo.setSex(data.getSex());
            basicInfo.setPhone(data.getPhone());
            basicInfo.setDataStartTime(dataTime);
            basicInfo.setEmpId(data.getEmpId());
            log.info("basicInfo={}", FastjsonUtil.toJson(basicInfo));
            empBasicInfoDo.update(basicInfo);
        }

        // 任职信息
        EmpWorkInfoDo workInfo = empWorkInfoDo.getEmpWorkInfo(data.getEmpId(), dataTime);
        if (null != workInfo && (!Objects.equals(workInfo.getName(), data.getName())
                || !Objects.equals(workInfo.getEnName(), data.getEnName()))) {
            workInfo.setName(data.getName());
            workInfo.setEnName(data.getEnName());
            workInfo.setDataStartTime(dataTime);
            log.info("workInfo={}", FastjsonUtil.toJson(workInfo));
            empWorkInfoDomainService.calcRetireDate(workInfo,empPrivateInfoDo);
        }
        // 修改任职信息触发es同步
        empWorkInfoDo.update(workInfo);
        // empWorkInfoSearchService.syncEmpPrivateInfo(data);
    }

    public void updateByEmpId(EmpPrivateInfoDo data) {
        EmpPrivateInfoDo temp = getByEmpId(data.getEmpId());
        data.setBid(temp.getBid());

        empPrivateInfoDo.update(data);

        Long dataTime = DateUtil.getCurrentTimestamp();
        // 同步修改姓名
        EmpBasicInfoDo basicInfo = empBasicInfoDo.getEmpBasicInfo(data.getEmpId(), dataTime);
        if (null != basicInfo && (!Objects.equals(basicInfo.getName(), data.getName()) ||
                !Objects.equals(basicInfo.getEnName(), data.getEnName()) ||
                !Objects.equals(basicInfo.getPhone(), data.getPhone())
                || (null != basicInfo.getSex() && !Objects.equals(basicInfo.getSex().getValue(), null == data.getSex() ? "" : data.getSex().getValue()))
                || (null != data.getSex() && !Objects.equals(data.getSex().getValue(), null == basicInfo.getSex() ? "" : basicInfo.getSex().getValue())))) {
            basicInfo.setName(data.getName());
            basicInfo.setEnName(data.getEnName());
            basicInfo.setSex(data.getSex());
            basicInfo.setPhone(data.getPhone());
            basicInfo.setDataStartTime(dataTime);
            log.info("basicInfo={}", FastjsonUtil.toJson(basicInfo));
            empBasicInfoDo.update(basicInfo);
        }

        empWorkInfoSearchService.syncEmpPrivateInfo(data);
    }

    public EmpPrivateInfoDo getByEmpId(String empId) {
        return empPrivateInfoDo.getByEmpId(empId);
    }

    public void saveBatch(List<EmpPrivateInfoDo> list) {
        empPrivateInfoDo.saveBatch(list);
    }

    public PageResult<EmpPrivateInfoDo> selectHasGuardian(BasePage page) {
        return empPrivateInfoDo.selectHasGuardian(page);
    }

    public List<EmpPrivateInfoDo> getAllEmpPrivateInfoByEmpIds(List<String> empIds) {
        return empPrivateInfoDo.getAllEmpPrivateInfoByEmpIds(empIds);
    }

    private void setErrorMsg(EmpInfoImportDo emp, String msg) {
        emp.setCheckEmpty(true);
        if (null == emp.getCheckEmptyTips()) {
            emp.setCheckEmptyTips(msg);
        } else {
            emp.setCheckEmptyTips(emp.getCheckEmptyTips() + "，" + msg);
        }
    }

    public PageResult<EmpPrivateInfoDo> selectPage(BasePage page) {
        return empPrivateInfoDo.selectPage(page);
    }
}
