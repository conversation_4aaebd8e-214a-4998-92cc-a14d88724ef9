package com.caidaocloud.hr.service.employee.infrastructure.emp.repository.impl;

import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpCertificateDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpCertificateRepository;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class EmpCertificateRepositoryImpl extends BaseRepositoryImpl<EmpCertificateDo> implements IEmpCertificateRepository {

    @Override
    public List<EmpCertificateDo> selectListByEmpId(String empId, String identifier, String tenantId) {
        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString())
                        .andEq("tenantId", tenantId), EmpCertificateDo.class).getItems();
    }

    @Override
    public void delete(String bid, String identifier) {
        DataDelete.identifier(identifier).softDelete(bid);
    }
}
