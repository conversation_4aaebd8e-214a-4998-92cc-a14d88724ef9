package com.caidaocloud.hr.service.employee.domain.emp.manage.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.hr.service.dto.ruleset.EmpRuleSetDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.emp.ruleset.service.EmpRuleSetService;
import com.caidaocloud.hr.service.employee.domain.base.constant.BaseConstant;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.service.EmpRuleSetDomainService;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class WorknoAutoDomainService {
    @Resource
    private CacheService cacheService;
    @Resource
    private EmpRuleSetDomainService empRuleSetDomainService;
    @Resource
    private EmpRuleSetService empRuleSetService;
    @Resource
    private Locker locker;

    public String nextWorkno(){
        return nextWorkno(UserContext.getTenantId(), null, null);
    }

    public String nextWorkno(String inputWorkno, String oldWorkno){
        return nextWorkno(UserContext.getTenantId(), inputWorkno, oldWorkno);
    }

    public String nextWorkno(String tenantId, String inputWorkno, String oldWorkno){
        EmpRuleSetDto rule = empRuleSetDomainService.loadRule();
        if(null == rule || null == rule.getWorknoAutoCreate() || !rule.getWorknoAutoCreate()){
            // 未开启员工工号自动生成
            if(StringUtil.isEmpty(inputWorkno)){
                PreCheck.preCheckArgument(StringUtil.isEmpty(oldWorkno),
                        LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32036));
                return oldWorkno;
            }

            return inputWorkno;
        }

        if(null != oldWorkno){
            return oldWorkno;
        }

        String cacheKey = String.format(HrConstant.AUTO_GENERATE_JOB_NUMBER, tenantId);
        // 和候选人自动生成工号保持一致
        Long currentNo = cacheService.increment(cacheKey) - 1;

        String preFix = rule.getWorknoPrefix();
        preFix = null == preFix ? "" : preFix;
        Integer length = rule.getWorknoLength() - preFix.length();
        String format = String.format(HrConstant.AUTO_GENERATE_FORMAT, preFix, length);
        return String.format(format, currentNo).replace(BaseConstant.SPACE_SEPARATOR, HrConstant.ZERO_FILLING_RULE);
    }

    public void updateWorknoMax(String tenantId, String workno){
        if(StringUtil.isEmpty(tenantId) || StringUtil.isEmpty(workno)){
            log.warn("update max workno end. tenantId={} or workno={} is empty.", tenantId, workno);
            return;
        }
        EmpRuleSetDto rule = empRuleSetDomainService.loadRule();
        if(null == rule || null == rule.getWorknoAutoCreate() || !rule.getWorknoAutoCreate()){
            // 未开启员工工号自动生成
            return;
        }

        workno = workno.replace(rule.getWorknoPrefix(), "");
        Long nextNo = Long.valueOf(workno);
        String cacheKey = String.format(HrConstant.AUTO_GENERATE_JOB_NUMBER, tenantId);
        Long currentNo = cacheService.getLong(cacheKey);
        if(nextNo < currentNo){
            return;
        }

        // 替换当前的最大自动生成工号的值
        var lockKey = String.format("AUTO_GENERATE_WORKNO_REPLACE_VALUE_%s", tenantId);
        var lock = locker.getLock(lockKey);
        try {
            var locked = lock.tryLock(30, TimeUnit.SECONDS);
            if (locked) {
                try{
                    currentNo = cacheService.getLong(cacheKey);
                    if(nextNo > currentNo){
                        rule.setWorknoStartValue(nextNo + 1);
                        cacheService.increment(cacheKey, nextNo - currentNo);
                        //updateEmpRuleSet(rule);
                    } else if(nextNo.equals(currentNo)){
                        cacheService.increment(cacheKey);
                    }
                } catch (Exception e){
                    log.error("updateEmpRuleSet err,{}", e.getMessage(), e);
                } finally {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("updateWorkNoMax get lock key err,{}", e);
        }
    }

    private void updateEmpRuleSet(EmpRuleSetDto rule){
        empRuleSetService.save(rule);
    }
}
