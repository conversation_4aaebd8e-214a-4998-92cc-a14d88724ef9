package com.caidaocloud.hr.service.organization.interfaces.facade.jobgrade;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.common.infrastructure.utils.ObjectConvertUtil;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.organization.application.jobgrade.service.JobGradeService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.organization.domain.cost.entity.CostCenterDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeChannelDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeDo;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.DragSortDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.StatusOptDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.jobgrade.IdQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.jobgrade.JobGradeDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.jobgrade.JobGradeQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.vo.jobgrade.JobGradeTreeVo;
import com.caidaocloud.hr.service.organization.interfaces.vo.jobgrade.JobGradeVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/1
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/jobgrade/v1")
@Api(value = "/api/hr/jobgrade/v1", description = "职级", tags = "v0.1")
public class JobGradeController {
    @Resource
    private JobGradeService jobGradeService;

    private void checkGrade(JobGradeDto dto) {
        PreCheck.preCheckArgument(StringUtils.isBlank(dto.getJobGradeCode()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30081));
        PreCheck.preCheckArgument(null == dto.getJobGradeLevel(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30082));
        PreCheck.preCheckArgument(null == dto.getChannelId(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30083));
    }

    @ApiOperation("新增职级")
    @PostMapping("/save")
    public Result saveJobGrade(@RequestBody JobGradeDto dto) {
        checkGrade(dto);
        JobGradeDo jobGradeDo = ObjectConverter.convert(dto, JobGradeDo.class);
        setI18nProperty(jobGradeDo,dto);
        return Result.ok(jobGradeService.saveJobGrade(jobGradeDo));
    }

    private void setI18nProperty(JobGradeDo jobGradeDo, JobGradeDto dto) {
        jobGradeDo.setI18nJobGradeName(LangUtil.getI18nValue(dto.getJobGradeName(), dto.getI18nJobGradeName()));
        jobGradeDo.setJobGradeName(String.valueOf(dto.getI18nJobGradeName().get("default")));
    }

    @ApiOperation("修改职级")
    @PostMapping("/update")
    public Result updateJobGrade(@RequestBody JobGradeDto dto) {
        PreCheck.preCheckArgument(null == dto.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30084));
        checkGrade(dto);
        JobGradeDo jobGradeDo = ObjectConverter.convert(dto, JobGradeDo.class);
        setI18nProperty(jobGradeDo,dto);
        jobGradeService.updateJobGrade(jobGradeDo);
        return Result.ok(true);
    }

    @ApiOperation("删除职级")
    @DeleteMapping("delete")
    public Result deleteJobGrade(@RequestParam("bid") String bid) {
        JobGradeDo data = new JobGradeDo();
        data.setBid(bid);
        jobGradeService.deleteJobGrade(data);
        return Result.ok(true);
    }

    @ApiOperation("启用或停用职级")
    @PostMapping("/updateStatus")
    public Result updateStatus(@RequestBody StatusOptDto dto) {
        dto.preCheckArgument();
        JobGradeDo data = ObjectConverter.convert(dto, JobGradeDo.class);
        if (StatusEnum.ENABLED.getIndex().equals(dto.getStatus())) {
            jobGradeService.enable(data);
        } else {
            jobGradeService.disable(data);
        }
        return Result.ok(true);
    }

    @ApiOperation("查看职级详情")
    @GetMapping("/detail")
    public Result getJobGradeById(@RequestParam("bid") String bid) {
        JobGradeDo gradeDo = jobGradeService.selectJobGradeById(bid);
        return Result.ok(ObjectConvertUtil.convert(gradeDo, JobGradeVo.class,JobGradeDo::i18nConvert));
    }

    @ApiOperation("查询职级列表")
    @PostMapping("/list")
    public Result getJobGradeList(@RequestBody JobGradeQueryDto dto) {
        List<JobGradeDo> list = jobGradeService.selectJobGradeList(dto.getChannelId(), dto.getStatus());
        for (JobGradeDo jobGradeDo : list) {
            jobGradeDo.setJobGradeName(LangUtil.parseI18nValue(jobGradeDo.getJobGradeName(), jobGradeDo.getI18nJobGradeName()));
        }
        return Result.ok(ObjectConvertUtil.convertList(list, JobGradeVo.class, JobGradeDo::i18nConvert));
    }

    @ApiOperation("根据BID集合查询职级列表")
    @PostMapping("/listByBids")
    public Result listByBids(@RequestBody IdQueryDto dto) {
        return Result.ok(ObjectConverter.convertList(jobGradeService.selectJobGradeListByIds(dto.getBids()), JobGradeVo.class));
    }

    @ApiOperation("职级拖动排序")
    @PostMapping("/dragSort")
    public Result<Boolean> dragSort(@RequestBody DragSortDto dto) {
        PreCheck.preCheckArgument(org.apache.commons.collections.CollectionUtils.isEmpty(dto.getItems()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30075));
        List<JobGradeDo> dataList = dto.getItems().stream().map(o -> {
            JobGradeDo data = new JobGradeDo();
            data.setBid(o.getBid());
            data.setSortNum(o.getSortNum());
            return data;
        }).collect(Collectors.toList());
        jobGradeService.dragSort(dataList);
        return Result.ok(true);
    }

    @ApiOperation("职级下拉列表")
    @GetMapping("/treeList")
    public Result<List<JobGradeTreeVo>> getTreeList() {
        List list = jobGradeService.getTreeList();
        return Result.ok(list);
    }
}
