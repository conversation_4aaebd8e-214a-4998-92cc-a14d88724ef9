package com.caidaocloud.hr.service.search.application.dto;

import com.caidaocloud.hr.service.organization.interfaces.dto.cost.CostCenterDto;
import com.caidaocloud.hr.service.tag.interfaces.vo.TagInfoKVVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class EmpSearchInfoDto {
    private String id;

    private String bid;

    private String tenantId;

    private long createTime;

    private long updateTime;
    /**
     * 员工ID
     */
    private String empId;

    /**
     * 员工工号
     */
    private String workno;

    /**
     * 员工姓名（个人信息冗余字段）
     */
    private String name;

    /**
     * 员工英文名（个人信息冗余字段）
     */
    private String enName;

    /**
     * 员工状态
     */
    private String empStatusValue;

    /**
     * 员工状态
     */
    private String empStatusText;

    /**
     * 用工类型\员工类型
     */
    private String empTypeText;

    /**
     * 用工类型\员工类型
     */
    private String empTypeValue;

    /**
     * 合同公司ID
     */
    private String company;

    /**
     * 合同公司名称
     */
    private String companyTxt;

    /**
     * 所属组织Id
     */
    private String organize;

    /**
     * 所属组织名称
     */
    private String organizeTxt;

    /**
     * 所属组织全路径
     */
    private String organizePath;

    /**
     * 关联的职务ID
     */
    private String job;

    /**
     * 关联的职务名称
     */
    private String jobTxt;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 岗位名称
     */
    private String postTxt;

    /**
     * 入职日期
     */
    private Long hireDate;

    /**
     * 员工头像
     */
    private Attachment photo;

    /**
     * 工作地ID
     */
    private String workplace;

    /**
     * 工作地名称
     */
    private String workplaceTxt;

    /**
     * 员工性别（个人信息模型字段）
     */
    private String sex;

    /**
     * 员工性别（个人信息模型字段）
     */
    private String sexText;

    /**
     * 转正状态
     */
    private String confirmationStatus;

    /**
     * 转正状态
     */
    private String confirmationStatusText;

    /**
     * 直接上级
     */
    private EmpSimple leadEmpId;

    /**
     * 职级职等
     */
    private JobGradeRange jobGrade;

    /**
     * 转正日期
     */
    private Long confirmationDate;

    /**
     * 离职日期
     */
    private Long leaveDate;

    /**
     * 工时制
     */
    private String workHourValue;

    /**
     * 工时制
     */
    private String workHourText;

    /**
     * 员工公司邮箱
     */
    private String companyEmail;

    /**
     * 入司途径
     */
    private String joinCompanyWayValue;

    /**
     * 入司途径
     */
    private String joinCompanyWayText;

    /**
     * 数据生效时间
     */
    private long dataStartTime;

    /**
     * 数据失效时间
     */
    private long dataEndTime;

    /**
     * 成本中心
     */
    private List<CostCenterDto> costCenters;

    /**
     * 兼岗
     */
    private List<EmpConcurrentPostSearchDto> concurrentPost;

    /**
     * 离职状态
     */
    private String resignationStatus;
    /**
     * 合同类型
     */
    private DictSimple contractType;
    /**
     * 基准岗位txt
     */
    private String benchPostTxt;
    /**
     * 基准岗位
     */
    private String benchPost;

    /**
     * 动态表头
     */
    private Map<String,Object> dynamicColumn;
    /**
     * 标签
     */
    private List<TagInfoKVVo> empTags;
}