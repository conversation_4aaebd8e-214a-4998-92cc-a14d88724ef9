package com.caidaocloud.hr.service.organization.interfaces.facade.company;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.common.infrastructure.utils.ObjectConvertUtil;
import com.caidaocloud.hr.service.contract.application.exception.ContinueMatchException;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpInsuranceInfoService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ExcelUtils;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.hr.service.organization.application.company.service.PayUnitService;
import com.caidaocloud.hr.service.organization.domain.company.entity.PayUnitDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.PayUnitDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.PayUnitQueryDto;
import com.caidaocloud.hr.service.vo.organization.company.PayUnitVo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.rmi.ServerException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司管理 --> 缴纳单位
 *
 * <AUTHOR>
 * @date 2024.12.6
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/unit/v1")
@Api(value = "/api/hr/unit/v1", description = "社保公积金缴纳单位", tags = "v8.5")
public class PayUnitController {
    
    @Resource
    private PayUnitService payUnitService;

    @Resource
    private EmpExtFieldService empExtFieldService;
    
    @Resource
    private EmpInsuranceInfoService empInsuranceInfoService;

    @PostMapping("list")
    @ApiOperation("缴纳单位列表")
    public Result<PageResult<PayUnitVo>> getList(@RequestBody PayUnitQueryDto queryDto) {
        PageResult<PayUnitDo> pageResult = payUnitService.getPayUnitPageResult(queryDto);
        List<PayUnitVo> items = ObjectConvertUtil.convertList(pageResult.getItems(), PayUnitVo.class, PayUnitDo::i18nConvert);
        return ResponseWrap.wrapResult(new PageResult(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }
    
    @GetMapping("selectList")
    @ApiOperation("下拉列表")
    public Result<List<PayUnitVo>> getList() {
        List<PayUnitDo> payUnitDos = payUnitService.selectListAll();
        List<PayUnitVo> items = ObjectConvertUtil.convertList(payUnitDos, PayUnitVo.class, PayUnitDo::i18nConvert);
        return ResponseWrap.wrapResult(items);
    }
    
    

    @PostMapping("save")
    @ApiOperation("增加缴纳单位")
    @LogRecordAnnotation(success = "新增了缴纳单位{{#name}}", category = "新增", menu = "组织-公司管理-缴纳单位", condition = "{{#condition}}")
    public Result save(@RequestBody PayUnitDto dto) throws ServerException {
        LogRecordContext.putVariable("name", dto.getUnitName());
        preCheckArgument(peekDefaultValue(dto));
        String id = payUnitService.savePayUnit(dto);
        LogRecordContext.putVariable("condition", true);
        return Result.ok(id);
    }

    @ApiOperation("查看详情缴纳单位")
    @GetMapping("/detail")
    public Result<PayUnitVo> getDetail(@RequestParam("bid") String bid) {
        PayUnitDo payUnitDo = payUnitService.getPayUnitById(bid);
        if (payUnitDo == null) {
            return Result.ok(new PayUnitVo());
        }
        // 自定义字段查询
        Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(payUnitDo.getDoIdentifier(), payUnitDo);
        PayUnitVo payUnitVo = ObjectConvertUtil.convert(payUnitDo, PayUnitVo.class, PayUnitDo::i18nConvert);
        payUnitVo.setExt(ext);
        return ResponseWrap.wrapResult(payUnitVo);
    }

    @PostMapping("update")
    @ApiOperation("编辑缴纳单位")
    @LogRecordAnnotation(success = "编辑了缴纳单位{{#name}}", category = "编辑", menu = "组织-公司管理-缴纳单位", condition = "{{#condition}}")
    public Result update(@RequestBody PayUnitDto dto) {
        PreCheck.preCheckArgument(null == dto.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30065));
        LogRecordContext.putVariable("name", dto.getUnitName());
        preCheckArgument(peekDefaultValue(dto));
        payUnitService.updatePayUnitById(dto);
        LogRecordContext.putVariable("condition", true);
        return Result.ok(true);
    }

    @ApiOperation("删除缴费单位")
    @DeleteMapping("/delete")
    @LogRecordAnnotation(success = "删除了缴纳单位{{#name}}", category = "删除", menu = "组织-公司管理-缴纳单位", condition = "{{#condition}}")
    public Result delete(@RequestParam("bid") String bid) {
        PayUnitDo payUnitDo = new PayUnitDo();
        payUnitDo.setBid(bid);
        Boolean flag = empInsuranceInfoService.hasEmpByPayUnit(bid);
        if (flag) {
            throw com.caidaocloud.excption.ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_33001"));
        }
        payUnitService.deleteUnitById(payUnitDo);
        return Result.ok(true);
    }

    @PostMapping("/export")
    @ApiOperation("导出缴纳单位")
    @LogRecordAnnotation(success = "导出了缴纳单位列表", menu = "组织-公司管理-缴纳单位", category = "导出")
    public void export(@RequestBody PayUnitQueryDto dto, HttpServletResponse response) {
        PageResult<PayUnitVo> page = payUnitService.getPayUnitForExport(dto);
        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : payUnitService.installNewlySignedExportProperty()) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(tagProperty.getPropertyTxt(), tagProperty.getProperty(), 15);
            exprortEntity.setOrderNum(tagProperty.getOrder());
            colList.add(exprortEntity);
        }
        List<Map<String, Object>> dataList = org.apache.commons.collections.CollectionUtils.isEmpty(page.getItems()) ? Lists.newArrayList() : installNewlySignedDataList(page.getItems(), colList);
        try {
            ExcelUtils.downloadDataMapExcel(colList, dataList, "缴纳单位导出", response);
        } catch (Exception e) {
            log.error("download NewlySignedList excel error.{}", e.getMessage(), e);
        }
    }

    private PayUnitDto peekDefaultValue(PayUnitDto dto) {
        dto.setUnitName(StringUtils.isEmpty(dto.getUnitName()) ? LangUtil.getDefaultValue(dto.getI18nUnitName()) :
                dto.getUnitName());
        return dto;
    }

    private void preCheckArgument(PayUnitDto dto) {
        PreCheck.preCheckArgument(null == dto.getUnitName(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30057));
    }

    private List<Map<String, Object>> installNewlySignedDataList(List<PayUnitVo> items, List<ExcelExportEntity> colList) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (PayUnitVo vo : items) {
            Map<String, Object> map = new HashMap<>();
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(vo));
            for (ExcelExportEntity entity : colList) {
                map.put(entity.getKey().toString(), json.get(entity.getKey()));
            }
            dataList.add(map);
        }
        return dataList;
    }

}
