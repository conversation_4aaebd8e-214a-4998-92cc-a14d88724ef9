package com.caidaocloud.hr.service.temination.domain.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.tag.interfaces.vo.TagInfoKVVo;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationApplyType;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationConfigStatus;
import com.caidaocloud.hrpaas.metadata.sdk.annotation.DisplayAsArray;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Option;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;

@Data
public class TerminationConfig extends DataSimple {

    private static String IDENTIFIER = "entity.hr.TerminationConfig";

    private String name;

    private String description;

    private TerminationConfigStatus status;

    @DisplayAsArray
    private List<TerminationPropertyConfig> enabledTerminationProperties = Lists.list();

    @DisplayAsArray
    private List<TagInfoKVVo> tagProperties = Lists.list();

    @DisplayAsArray
    private List<MetadataPropertyVo> displayWorkInfos;

    private TerminationApplyType terminationApplyType;

    private String formDefId;

    public String create(){
        val user = SecurityUserUtil.getSecurityUserInfo();
        setCreateBy(String.valueOf(user.getUserId()));
        setCreateTime(System.currentTimeMillis());
        setUpdateBy(getCreateBy());
        setUpdateTime(getCreateTime());
        status = TerminationConfigStatus.NOT_ENABLED;
        return DataInsert.identifier(IDENTIFIER).insert(this);
    }

    public void update() {
        val user = SecurityUserUtil.getSecurityUserInfo();
        val one = DataQuery.identifier(IDENTIFIER).one(getBid(), TerminationConfig.class);
        if(one.status.equals(TerminationConfigStatus.ENABLED)){
            if(null != one.getEnabledTerminationProperties() && !one.getEnabledTerminationProperties().isEmpty()){
                if(null == this.getEnabledTerminationProperties()){
                    throw new ServerException("离职标准字段不允许删除");
                }
                for(TerminationPropertyConfig propertyConfig : one.getEnabledTerminationProperties()){
                    this.getEnabledTerminationProperties().stream().filter(newPropertyConfig->
                            newPropertyConfig.getProperty() == propertyConfig.getProperty())
                            .findFirst().orElseThrow(()->new ServerException("离职标准字段不允许删除"));
                }
            }
            if(!StringUtils.equals(one.formDefId, formDefId)){
                throw new ServerException("自定义设置不允许更新");
            }
        }else if(one.status.equals(TerminationConfigStatus.DISABLED)){
            throw new ServerException("配置当前状态不允许更新");
        }else if(one.status.equals(TerminationConfigStatus.NOT_ENABLED)){
            //do nothing
        }

        if(!one.terminationApplyType.equals(terminationApplyType)){
            throw new ServerException("配置类型不允许更新");
        }
        BeanUtils.copyProperties(one, this,
                "name", "tagProperties", "description", "enabledTerminationProperties","displayWorkInfos", "formDefId");
        setUpdateBy(String.valueOf(user.getUserId()));
        setUpdateTime(System.currentTimeMillis());
        //status = TerminationConfigStatus.NOT_ENABLED;
        DataUpdate.identifier(IDENTIFIER).update(this);
    }

    public static void enable(String bid){
        List<TerminationConfig> configs = listAll();
        val config = configs.stream().filter(it->it.getBid().equals(bid)).findFirst()
                .orElseThrow(()->new ServerException("配置不存在"));
        if(config.status.equals(TerminationConfigStatus.ENABLED)){
            throw new ServerException("配置已开启");
        }
        config.status = TerminationConfigStatus.ENABLED;
        DataUpdate.identifier(IDENTIFIER).update(config);
        val type = config.terminationApplyType;
        configs.stream().filter(it->
                it.getStatus().equals(TerminationConfigStatus.ENABLED) &&
                        it.getTerminationApplyType().equals(type) &&
                    !it.getBid().equals(bid)).forEach(it->{
            it.status = TerminationConfigStatus.DISABLED;
            DataUpdate.identifier(IDENTIFIER).update(it);
        });
    }

    public static List<TerminationConfig> listAll(){
        return DataQuery.identifier(IDENTIFIER).limit(500, 1).filter(DataFilter.eq("tenantId",
                SecurityUserUtil.getSecurityUserInfo().getTenantId()), TerminationConfig.class).getItems();
    }

    public static Option<TerminationConfig> loadEnabled(TerminationApplyType type){
        val list = DataQuery.identifier(IDENTIFIER).filter(DataFilter.eq("terminationApplyType",
                type.toString()).andEq("status", TerminationConfigStatus.ENABLED.toString()),
                TerminationConfig.class).getItems();
        if(list.size() != 0){
            return Option.option(list.get(0));
        }else{
            return Option.none();
        }
    }

    public static Option<TerminationConfig> load(String bid){
        return Option.option(DataQuery.identifier(IDENTIFIER).oneOrNull(bid, TerminationConfig.class));
    }

}
