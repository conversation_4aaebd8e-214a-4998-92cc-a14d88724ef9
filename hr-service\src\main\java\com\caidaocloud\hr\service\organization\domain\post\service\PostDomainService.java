package com.caidaocloud.hr.service.organization.domain.post.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpConcurrentPostService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpPostRecordService;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPostRecordDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.growthrecord.domain.service.GrowthRecordDomainService;
import com.caidaocloud.hr.service.organization.application.org.dto.OrgStructurePosDto;
import com.caidaocloud.hr.service.organization.domain.common.event.CommonChangeEvent;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.BenchmarkPositionDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.repository.IPostRepository;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgOrPostQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.PostDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.PostQueryDto;
import com.caidaocloud.hr.service.search.application.event.EntityDataChange;
import com.caidaocloud.hr.service.util.EntityDataUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.NestPropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/3
 */
@Slf4j
@Service
public class PostDomainService {
    @Resource
    private PostDo postDo;
    @Resource
    private JobDo jobDo;
    @Resource
    private BenchmarkPositionDo benchmarkPositionDo;
    @Resource
    private OrgDo orgDo;
    @Resource
    private WorkplaceDo workplaceDo;
    @Resource
    private EmpWorkInfoDo empWorkInfoDo;
    @Resource
    private IPostRepository postRepository;

    public void updateStatus(PostDo data, BusinessEventTypeEnum eventTypeEnum) {
        postDo.updateStatus(data, eventTypeEnum);
    }

    public void delete(String bid, Long dataStartTime) {
        postDo.delete(bid, dataStartTime);
    }

    public PostDo selectById(String bid, Long dateTime) {
        return postDo.selectById(bid, dateTime);
    }

    public String save(PostDo data) {
        checkPostData(data);
        return postDo.save(data);
    }

    public void update(PostDo data) {
        checkPostData(data);
        postDo.update(data);
    }

    public List<EmpWorkInfoDo> getEmpByPost(String bid, Long dateTime) {
        return postDo.getEmpByPost(bid, dateTime);
    }

    private void checkPostData(PostDo data) {
        long dataStartTime = data.getDataStartTime();
        doPostRelation(data);

        // 所属组织时间轴
        List<OrgDo> dbOrgList = orgDo.selectAllByIds(Arrays.stream(data.getOrgId().split(",")).collect(Collectors.toList()), dataStartTime);
        PreCheck.preCheckArgument(null == dbOrgList || dbOrgList.isEmpty(),
                "所属组织不存在");
        data.setOrgName(dbOrgList.stream().map(OrgDo::getName).collect(Collectors.joining(",")));

        // 工作地时间轴
        if (StringUtil.isNotBlank(data.getWorkplaceId())) {
            WorkplaceDo dbWorkplace = workplaceDo.selectById(data.getWorkplaceId());
            PreCheck.preCheckArgument(null == dbWorkplace || StringUtil.isEmpty(dbWorkplace.getBid()),
                    "工作地不存在");
            data.setWorkplaceName(dbWorkplace.getName());

            updateEmployeeWorkplace(data, dbWorkplace);
        }
    }

    private void doPostRelation(PostDo data) {
        if (null == data.getRelation()) {
            return;
        }

        if (1 == data.getRelation() && StringUtil.isNotBlank(data.getJobId())) {
            // 职务
            JobDo dbJob = jobDo.selectById(data.getJobId());
            PreCheck.preCheckArgument(null == dbJob || StringUtil.isEmpty(dbJob.getBid()), "关联的职务不存在");
            data.setJobName(dbJob.getName());
        } else if (2 == data.getRelation() && StringUtil.isNotBlank(data.getBenchmarkPositionId())) {
            // 基准岗位
            BenchmarkPositionDo dbBenchmarkPosition = benchmarkPositionDo.selectById(data.getBenchmarkPositionId());
            PreCheck.preCheckArgument(null == dbBenchmarkPosition || StringUtil.isEmpty(dbBenchmarkPosition.getBid()),
                    "关联的基准岗位不存在");
            data.setBenchmarkPositionName(dbBenchmarkPosition.getName());
        }
    }

    public PageResult<PostDo> selectPage(PostQueryDto pageQueryDto) {
        return postDo.selectPage(pageQueryDto);
    }

    private void updateEmployeeWorkplace(PostDo data, WorkplaceDo dbWorkplace) {
        if (null == data.getSyncEmpPost() || !data.getSyncEmpPost() || null == dbWorkplace) {
            return;
        }

//            查时间轴大于今天的 用于查未来 暂时注释不用
//        empWorkInfoDo.selectGeStartTime(DateUtil.getMidnightTimestamp() * 1000);
//            查时间轴当前生效的员工信息
        List<EmpWorkInfoDo> empWorkInfoDos = empWorkInfoDo.selectListByPost(DateUtil.getMidnightTimestamp() * 1000, data.getBid());
        for (EmpWorkInfoDo infoDo : empWorkInfoDos) {
            infoDo.setWorkplace(dbWorkplace.getBid());
            infoDo.setWorkplaceTxt(dbWorkplace.getName());
            empWorkInfoDo.update(infoDo);
        }
    }

    public List<PostDo> selectList(String orgId, Integer status, Long dataStartTime, String keyword) {
        return postDo.selectList(orgId, status, dataStartTime, keyword);
    }

    public List<PostDo> selectPostListByOrgIdList(OrgOrPostQueryDto orgQueryDto) {
        return postDo.selectListByOrgIdList(orgQueryDto);
    }

    public PageResult<PostDo> selectPageAll(PostQueryDto pageQueryDto) {
        List<PostDo> list = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 5000;
        boolean flag = true;
        int index = pageSize;
        while (flag) {
            pageQueryDto.setPageNo(pageNo);
            pageQueryDto.setPageSize(pageSize);
            PageResult<PostDo> page = selectPage(pageQueryDto);
            list.addAll(page.getItems());
            if (page.getTotal() > index) {
                pageNo++;
                index += pageSize;
            } else {
                flag = false;
            }
        }
        return new PageResult<>(list);
    }

    public List<PostDo> selectPostDoByCodes(List<String> codes, BasePageQueryDto page) {
        return postDo.selectPostDoByCodes(codes, page);
    }

    public List<PostDo> selectOneByJobId(String jobId) {
        return postDo.selectListByJobId(jobId, 1);
    }

    public PageResult<PostDo> selectPostPageListByOrgId(PostQueryDto pageQueryDto) {
        return postDo.selectPostPageListByOrgId(pageQueryDto);
    }

    public List<PostDo> selectByIds(List<String> postList, Long dataTime) {
        return postDo.selectByIds(postList, dataTime);
    }

    public List<OrgStructurePosDto> selectPostCardInfoByOrgId(String orgId, StatusEnum status, long datetime) {
        return postRepository.selectPostCardInfo(orgId, status, datetime);
    }

    public String onlySave(PostDo data) {
        return postDo.save(data);
    }

    public void onlyUpdate(PostDo data) {
        postDo.update(data);
    }

    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    @Resource
    private EmpPostRecordService empPostRecordService;

    @Resource
    private EmpConcurrentPostService empConcurrentPostService;

    /**
     * *岗位更新后：*员工信息-任职信息-岗位、员工信息-成长记录-岗位、员工信息-主岗记录-岗位、员工信息
     * * 任职信息-直接上级岗位、员工信息-成长记录-直接上级岗位、员工信息-兼岗记录-兼岗岗位、员工信息-兼
     * * 岗记录-兼岗上级岗位；
     *
     * @param data
     */
    public void sync(List<EntityDataChange> data) {
        CommonChangeEvent cce = new CommonChangeEvent(1);
        data.forEach(entityData -> {
            EntityDataDto after = entityData.getAfter();
            EntityDataDto before = entityData.getBefore();
            if (after == null || after.isDeleted()) {
                return;
            }

            // 生效的岗位信息
            PostDto beforeData = null;
            if (before != null) {
                beforeData = EntityDataUtil.convertEntityData(before, PostDto.class);
            }
            PostDto afterData = EntityDataUtil.convertEntityData(after, PostDto.class);
            if(null == before && null != after){
                // 新增
                cce.setEventObject(afterData);
                cce.setBid(afterData.getBid());
                cce.setName(afterData.getName());
                cce.setTenantId(after.getTenantId());
                cce.setDataStartTime(afterData.getDataStartTime());
            } else if(after != null && !after.isDeleted()){
                // 修改
                if(!Objects.equals(beforeData.getName(), afterData.getName())){
                    cce.setEventObject(afterData);
                    cce.setBid(afterData.getBid());
                    cce.setName(afterData.getName());
                    cce.setTenantId(after.getTenantId());
                    cce.setDataStartTime(afterData.getDataStartTime());
                    cce.setChanged(true);
                } else if(StringUtil.isNotEmpty(cce.getBid())
                        && !Objects.equals(cce.getName(), afterData.getName())){
                    cce.setChanged(true);
                }
            }
        });

        if (cce.isChanged()) {
            doPostChange(cce);
        }
    }

    public void doPostChange(CommonChangeEvent event) {
        log.info("do PostChange event.... data={}", FastjsonUtil.toJson(event));
        try {
            BasePage basePage = new BasePage();
            basePage.setPageSize(5000);
            basePage.setPageNo(1);

            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(event.getTenantId());
            userInfo.setUserId(0L);
            userInfo.setEmpId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);

            UserInfo user = new UserInfo();
            user.setTenantId(event.getTenantId());
            user.setStaffId(0L);
            user.setUserid(0);
            UserContext.setCurrentUser(user);

            // 员工信息，成长记录；调用 员工 变更记录方法
            doEmpWorkInfoChangeData(event, basePage);

            // 员工主岗信息；
            basePage.setPageNo(1);
            doEmpPostRecordChangeData(event, basePage);

            // 员工上级信息 成长记录；调用 员工 变更记录方法
            basePage.setPageNo(1);
            doEmpWorkInfoLeaderChangeData(event, basePage);

            // 员工兼岗信息
            basePage.setPageNo(1);
            doEmpConcurrentPostChangeData(event, basePage);

            // 员工兼岗上级信息
            basePage.setPageNo(1);
            doEmpConcurrentPostLeaderChangeData(event, basePage);
        } catch (Exception e) {
            log.error("do PostChange event.... err, {}", e.getMessage());
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            UserContext.remove();
        }
    }

    private void doEmpWorkInfoChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
            .andEq("post", event.getBid())
            .andGe("dataEndTime", String.valueOf(event.getDataStartTime()));
        PageResult<EmpWorkInfoDo> pr = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpWorkInfoDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        GrowthRecordDomainService grdService = SpringUtil.getBean(GrowthRecordDomainService.class);
        pr.getItems().forEach(data -> {
            if(null != data.getEmpStatus() && null != data.getEmpStatus().getValue()
                    && EmpStatusEnum.LEAVE_JOB.realValue().equals(data.getEmpStatus().getValue())){
                // 如果员工已离职，则不处理
                return;
            }
            try {
                EmpWorkInfoDo before = new EmpWorkInfoDo();
                BeanUtils.copyProperties(data, before);
                NestPropertyValue properties = new NestPropertyValue();
                BeanUtils.copyProperties(data.getProperties(), properties);
                before.setProperties(properties);
                data.setPostTxt(event.getName());
                data.getProperties().add("postTxt", event.getName());
                if(data.getDataStartTime() < event.getDataStartTime()){
                    data.setDataStartTime(event.getDataStartTime());
                    empWorkInfoDomainService.updateJoinTxt(data);
                    //empWorkInfoDomainService.doProcessGrowthRecord(before, data);
                    return;
                }
                empWorkInfoDomainService.updateJoinTxt(data);
                grdService.updateGrowthRecord(data.getEmpId(), event, data.getDataStartTime(), "postTxt");
            } catch (Exception e){
                log.error("Failed to update employee employment information and position name. dataId={}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpWorkInfoChangeData(event, basePage);
    }

    private void doEmpWorkInfoLeaderChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
            .andEq("leaderPost", event.getBid())
            .andGe("dataEndTime", String.valueOf(event.getDataStartTime()));
        PageResult<EmpWorkInfoDo> pr = DataQuery.identifier("entity.hr.EmpWorkInfo")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpWorkInfoDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        GrowthRecordDomainService grdService = SpringUtil.getBean(GrowthRecordDomainService.class);
        pr.getItems().forEach(data -> {
            if(null != data.getEmpStatus() && null != data.getEmpStatus().getValue()
                    && EmpStatusEnum.LEAVE_JOB.realValue().equals(data.getEmpStatus().getValue())){
                // 如果员工已离职，则不处理
                return;
            }
            try {
                EmpWorkInfoDo before = new EmpWorkInfoDo();
                BeanUtils.copyProperties(data, before);
                NestPropertyValue properties = new NestPropertyValue();
                BeanUtils.copyProperties(data.getProperties(), properties);
                before.setProperties(properties);
                data.setLeaderPostTxt(event.getName());
                data.getProperties().add("leaderPostTxt", event.getName());
                if(data.getDataStartTime() < event.getDataStartTime()){
                    data.setDataStartTime(event.getDataStartTime());
                    empWorkInfoDomainService.updateJoinTxt(data);
                    //empWorkInfoDomainService.doProcessGrowthRecord(before, data);
                    return;
                }
                empWorkInfoDomainService.updateJoinTxt(data);
                grdService.updateGrowthRecord(data.getEmpId(), event, data.getDataStartTime(), "leaderPostTxt");
            } catch (Exception e){
                log.error("Failed to update employee employment information, superior position name. dataId={}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpWorkInfoLeaderChangeData(event, basePage);
    }

    private void doEmpPostRecordChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
            .andEq("post", event.getBid())
            .and(DataFilter.eq("endTime", null).orGe("endTime", String.valueOf(event.getDataStartTime())));
        PageResult<EmpPostRecordDo> pr = DataQuery.identifier("entity.hr.EmpPostRecord")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpPostRecordDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        pr.getItems().forEach(data -> {
            data.setPostTxt(event.getName());
            data.getProperties().add("postTxt", event.getName());
            try {
                empPostRecordService.updateById(data);
            } catch (Exception e){
                log.error("Failed to update the position name of the employee's main job record. dataId={}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpPostRecordChangeData(event, basePage);
    }

    private void doEmpConcurrentPostChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
            .andEq("post", event.getBid())
            .andGe("endDate", String.valueOf(event.getDataStartTime()));
        PageResult<EmpConcurrentPostDo> pr = DataQuery.identifier("entity.hr.EmpConcurrentPost")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpConcurrentPostDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        pr.getItems().forEach(data -> {
            data.setPostTxt(event.getName());
            data.getProperties().add("postTxt", event.getName());
            try {
                empConcurrentPostService.updateById(data);
            } catch (Exception e){
                log.error("Employee part-time position name update failed. dataId={}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpConcurrentPostChangeData(event, basePage);
    }

    private void doEmpConcurrentPostLeaderChangeData(CommonChangeEvent event, BasePage basePage) {
        DataFilter filter = DataFilter.eq("tenantId", event.getTenantId())
            .andEq("leaderPost", event.getBid())
            .andGe("endDate", String.valueOf(event.getDataStartTime()));
        PageResult<EmpConcurrentPostDo> pr = DataQuery.identifier("entity.hr.EmpConcurrentPost")
                .decrypt().specifyLanguage().queryInvisible().limit(basePage.getPageSize(), basePage.getPageNo())
                .filter(filter, EmpConcurrentPostDo.class, -1);
        if (null == pr || null == pr.getItems() || pr.getItems().isEmpty()) {
            return;
        }

        pr.getItems().forEach(data -> {
            data.setLeaderPostTxt(event.getName());
            data.getProperties().add("leaderPostTxt", event.getName());
            try {
                empConcurrentPostService.updateById(data);
            } catch (Exception e){
                log.error("Failed to update the position name of the employee's part-time supervisor. dataId={}, errMsg={}",
                        data.getId(), e.getMessage(), e);
            }
        });

        if (pr.getItems().size() < basePage.getPageSize()) {
            return;
        }
        basePage.setPageNo(basePage.getPageNo() + 1);
        doEmpConcurrentPostLeaderChangeData(event, basePage);
    }

    public List<EmpConcurrentPostDo> getEnableConcurrentByPost(String postId, Long dateTime) {
        return postDo.getEnableConcurrentByPost(postId, dateTime);
    }


    public PostDo getI18nPost(String bid) {
        PostDo postDo = selectById(bid, System.currentTimeMillis());
        return Optional.ofNullable(postDo).map(it -> {
            PostDo i18nPostDo = new PostDo();
            i18nPostDo.setBid(it.getBid());
            i18nPostDo.setName(LangUtil.parseI18nValue(it.getName(), it.getI18nName()));
            i18nPostDo.setCode(it.getCode());
            return i18nPostDo;
        }).orElse(null);
    }

    public List<PostDo> getBatchI18nPost(List<String> bids) {
        return Optional.ofNullable(bids).map(it -> it.stream().map(this::getI18nPost)
                        .filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(Lists.list());
    }

}
