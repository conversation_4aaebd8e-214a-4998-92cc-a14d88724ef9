package com.caidaocloud.hr.service.organization.application.post.service;

import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.dataimport.DataImportService;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.organization.application.post.enums.PostRelation;
import com.caidaocloud.hr.service.organization.application.workplace.service.WorkplaceService;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.BenchmarkPositionDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostImportDo;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.organization.infrastructure.repository.po.PostImportPo;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2022/11/9
 */
@Service
@Slf4j
public class PostImportService extends DataImportService<PostImportDo, PostImportPo> {

	private final String EXCEL_CODE = "POST_IMPORT";
	private static final String ORG_CODE="ORG_%d_%s";
	private static final String WORK_PLACE = "WORK_PLACE", JOB = "JOB", BENCH_POST = "BENCH_POST", JOB_GRADE = "JOB_GRADE";
	@Resource
	private PostImportDo postImportDo;
	@Resource
	private OrgService orgService;
	@Resource
	private PostService postService;
	@Resource
	private WorkplaceService workplaceService;

	private static Map<String, Map<String, OrgDo>> orgDoMaps = new HashMap<>();
	private static Map<String, Map<String, JobDo>> jobDoMaps = new HashMap<>();
	private static Map<String, Map<String, WorkplaceDo>> workplacesMaps = new HashMap<>();
	private static Map<String, Map<String, BenchmarkPositionDo>> benchPostMaps = new HashMap<>();
	private static Map<String, Map<String, JobGradeDo>> jobGradeMaps = new HashMap<>();

	@Override
	public String getExcelCode() {
		return EXCEL_CODE;
	}

	@Override
	public void prepareOperateDataFromInputStream(InputStream inputStream, String processId, String tenantId, Long userId) {
		List<PostImportDo> list = getPoDataFromExcel(inputStream);
		initOrg(list);
		SpringUtil.getBean(this.getClass()).operateDataFromInputStream(list, processId, tenantId, userId);
	}

	@Override
	public void initProperty() {
		String tenantId = UserContext.getTenantId();
		jobDoMaps.put(getMapKeyWithTenantId(JOB, tenantId), getJobMap());
		benchPostMaps.put(getMapKeyWithTenantId(BENCH_POST, tenantId), getBenchPostMap());
		workplacesMaps.put(getMapKeyWithTenantId(WORK_PLACE, tenantId), getWorkplaceMap());
		jobGradeMaps.put(getMapKeyWithTenantId(JOB_GRADE, tenantId), getJobGradeMap());
	}

	@Override
	protected Map<String, WorkplaceDo> getWorkplaceMap() {
		List<WorkplaceDo> list = workplaceService.selectList(StatusEnum.ENABLED);
		return list.stream().collect(Collectors.toMap(WorkplaceDo::getCode, obj -> obj, (a, b) -> a));
	}

	private void initOrg(List<PostImportDo> list) {
		String tenantId = UserContext.getTenantId();
		Map<Long, List<PostImportDo>> orgMap = Sequences.sequence(list).filter(post -> post.getDataStartTime() != null)
				.toMap(PostImportDo::getDataStartTime);
		// 根据生效时间查询所有组织
		for (Map.Entry<Long, List<PostImportDo>> entry : orgMap.entrySet()) {
			String key = formatOrgKey(entry.getKey(), tenantId);
			List<String> codeList = Sequences.sequence(entry.getValue()).map(PostImportDo::getOrgCode).toList();
			List<OrgDo> orgList = orgService.getOrgListByOrgCodes(codeList, entry.getKey());
			orgDoMaps.put(key, orgList.stream().collect(Collectors.toMap(OrgDo::getCode, obj -> obj, (a, b) -> a)));
		}
	}

	private String formatOrgKey(Long dataStartTime, String tenantId) {
		return String.format(ORG_CODE, dataStartTime, tenantId);
	}

	/**
	 * 从excel文件中读取原始数据
	 *
	 * @param inputStream
	 * @return
	 */
	@Override
	public List<PostImportDo> getPoDataFromExcel(InputStream inputStream) {
		return postImportDo.getExcelData(inputStream);
	}

	/**
	 * 批量插入或更新数据，返回错误数据，正确数据入库
	 *
	 * @param list
	 * @return
	 */
	@Override
	public List<PostImportDo> batchInsertUpdateData(List<PostImportDo> list) {
		List<PostImportDo> errorList = new ArrayList<>();
		Map<Long, List<PostImportDo>> startTimeMap = Sequences.sequence(list).toMap(PostImportDo::getDataStartTime);
		// 根据生效日期分组
		for (Map.Entry<Long, List<PostImportDo>> entry : startTimeMap.entrySet()) {
			List<String> codeList = Sequences.sequence(entry.getValue()).map(PostImportDo::getCode).toList();
			List<PostDo> existList = postService.selectPostDoByCodes(codeList, entry.getKey());
			Map<String, PostDo> codeMap = existList.stream()
					.collect(Collectors.toMap(PostDo::getCode, obj -> obj, (a, b) -> a));

			// 岗位新增或更新
			for (PostImportDo importDo : entry.getValue()) {
				try {
					PostDo post = ObjectConverter.convert(importDo, PostDo.class);
					PostDo exist;
					if ((exist = codeMap.get(post.getCode())) != null) {
						BeanUtil.copyWithNoValue(post, exist);
						postService.update(exist);
						continue;
					}
					postService.save(post);
				}
				catch (Exception e) {
					log.error("import post failed", e);
					importDo.setEmptyTips(e.getMessage());
					errorList.add(importDo);
				}
			}
		}
		return errorList;
	}

	/**
	 * 检查不能为空的字段
	 *
	 * @param data
	 * @return
	 */
	@Override
	public boolean checkEmptyProp(PostImportDo data) {
		if (data.getDataStartTimeTxt() == null) {
			data.setEmptyTips(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30047));
		}
		if (StringUtils.isEmpty(data.getRelationTxt())) {
			data.setEmptyTips(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30052));
		}
		if (StringUtils.isEmpty(data.getName())) {
			data.setEmptyTips(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30053));
		}
		if (StringUtils.isEmpty(data.getCode())) {
			data.setEmptyTips(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30054));
		}
		if (StringUtils.isEmpty(data.getOrgCode())) {
			data.setEmptyTips(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30089));
		}
		return !data.isCheckEmpty();
	}

	/**
	 * 检查字段不能匹配的处理，返回不能匹配的数据
	 *
	 * @param data
	 * @return
	 */
	@Override
	public boolean checkEmptyMark(PostImportDo data) {
		return data.isCheckEmpty();
	}

	/**
	 * 组装属性字段，如id，枚举类型数据
	 *
	 * @param data
	 * @return
	 */
	@Override
	public boolean installProp(PostImportDo data) {
		if (data.getDataStartTime() == null) {
			data.setEmptyTips(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30037));
		}

		// 获取组织数据
		data.setOrg(getOrg(data.getDataStartTime(), data.getOrgCode()));

		// 获取工作地
		if (!StringUtils.isEmpty(data.getWorkplaceCode())) {
			WorkplaceDo workplace = getWorkplace(data.getWorkplaceCode());
			data.setWorkplace(workplace);
		}

		// 获取职务/基准岗位
		PostRelation postRelation = PostRelation.getByText(data.getRelationTxt());
		if (postRelation == null) {
			data.setEmptyTips(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32034));
		}else {
			data.setRelation(postRelation.relation);
			if (StringUtils.isNotEmpty(data.getRelationCode())) {
				switch (postRelation) {
				case BENCHMARK_POSITION:
					data.setBenchmarkPosition(getBenchPost(data.getRelationCode()));
					break;
				case JOB:
					data.setJob(getJob(data.getRelationCode()));
					break;
				default:
				}
			}
		}

		// 获取职级
		if (!StringUtils.isEmpty(data.getJobGradeCode())) {
			JobGradeDo jobGrade = getJobGrade(data.getJobGradeCode());
			data.setJobGrade(jobGrade);
		}
		return !data.isCheckEmpty();
	}

	private JobDo getJob(String code) {
		Map<String, JobDo> jobDoMap = jobDoMaps.get(getMapKeyWithTenantId(JOB, getTenantId()));
		if (jobDoMap == null) {
			return null;
		}
		return jobDoMap.get(code);
	}

	private BenchmarkPositionDo getBenchPost(String code) {
		Map<String, BenchmarkPositionDo> benchmarkPositionMap = benchPostMaps.get(getMapKeyWithTenantId(BENCH_POST, getTenantId()));
		if (benchmarkPositionMap == null) {
			return null;
		}
		return benchmarkPositionMap.get(code);
	}

	private JobGradeDo getJobGrade(String code) {
		Map<String, JobGradeDo> jobGradeMap = jobGradeMaps.get(getMapKeyWithTenantId(JOB_GRADE, getTenantId()));
		if (jobGradeMap == null) {
			return null;
		}
		return jobGradeMap.get(code);
	}

	private WorkplaceDo getWorkplace(String code) {
		Map<String, WorkplaceDo> workplaceMap = workplacesMaps.get(getMapKeyWithTenantId(WORK_PLACE, getTenantId()));
		if (workplaceMap == null) {
			return null;
		}
		return workplaceMap.get(code);
	}

	private OrgDo getOrg(Long dataStartTime, String orgCode) {
		Map<String, OrgDo> orgMap = orgDoMaps.get(formatOrgKey(dataStartTime, getTenantId()));
		if (orgMap == null) {
			return null;
		}
		return orgMap.get(orgCode);
	}

}
