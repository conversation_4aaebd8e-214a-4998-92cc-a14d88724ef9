package com.caidaocloud.hr.service.transfer.application.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.em.SortOrder;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.ArchiveEventProducer;
import com.caidaocloud.hr.service.common.infrastructure.enums.ProcesStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.ContractStatusEnum;
import com.caidaocloud.hr.service.contract.application.enums.SignProcessStatusEnum;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.application.service.ContractTypeSetService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.interfaces.vo.ContractTypeSetVo;
import com.caidaocloud.hr.service.dto.transfer.TransferTerminationEventDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpWorkInfoService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpSalaryChangeDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpSalaryChangeDomainService;
import com.caidaocloud.hr.service.enums.archive.ArchiveStandardLine;
import com.caidaocloud.hr.service.organization.application.workplace.service.WorkplaceService;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.company.service.CompanyDomainService;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.job.service.JobDomainService;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hr.service.organization.domain.workplace.entity.WorkplaceDo;
import com.caidaocloud.hr.service.tag.application.tag.service.EmpTagInfoService;
import com.caidaocloud.hr.service.temination.application.FormService;
import com.caidaocloud.hr.service.temination.application.dto.FormDataDto;
import com.caidaocloud.hr.service.temination.application.dto.WfTaskApproveDTO;
import com.caidaocloud.hr.service.temination.application.dto.WfTaskRevokeDTO;
import com.caidaocloud.hr.service.temination.application.enums.WfTaskActionEnum;
import com.caidaocloud.hr.service.temination.application.feign.WfOperateFeignClient;
import com.caidaocloud.hr.service.transfer.application.dto.TransferApplyDto;
import com.caidaocloud.hr.service.transfer.application.dto.TransferFieldDto;
import com.caidaocloud.hr.service.transfer.application.enums.TransferApplyEmp;
import com.caidaocloud.hr.service.transfer.application.event.publish.TransferPublish;
import com.caidaocloud.hr.service.transfer.application.feign.PayeignClient;
import com.caidaocloud.hr.service.transfer.application.feign.vo.PayFixItemByUpdateTimeVo;
import com.caidaocloud.hr.service.transfer.domain.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.transfer.domain.entity.ChangeDefDo;
import com.caidaocloud.hr.service.transfer.domain.entity.ChangeFieldDef;
import com.caidaocloud.hr.service.transfer.domain.entity.TransferApplyDo;
import com.caidaocloud.hr.service.transfer.domain.enums.ChangeDefEnum;
import com.caidaocloud.hr.service.transfer.infrastructure.utils.ConvertHelper;
import com.caidaocloud.hr.service.transfer.interfaces.dto.*;
import com.caidaocloud.hr.service.transfer.interfaces.vo.*;
import com.caidaocloud.hr.service.util.DataSimpleUtil;
import com.caidaocloud.hr.workflow.util.WorkFlowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.query.QueryInfoCache;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.utils.PaasTransactionalUtil;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfProcessRuDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.enums.WfProcessStatusEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.caidaocloud.hr.service.transfer.infrastructure.constant.TransferConstant.*;
import static com.caidaocloud.hr.service.transfer.infrastructure.utils.DataSimpleUtil.nullOrEmpty;

/**
 * created by: FoAng
 * create time: 31/1/2023 2:04 下午
 */
@Slf4j
@Service
public class TransferService {
    @Value("${caidaocloud.payroll.hideHisAmount:false}")
    private boolean hideHisAmount;

    @Resource
    private TransferEsService transferEsService;
    @Resource
    private TransferDefService transferDefService;
    @Resource
    private FormService formService;
    @Resource
    private IWfRegisterFeign iWfRegisterFeign;
    @Resource
    private WfOperateFeignClient wfOperateFeignClient;
    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private ContractService contractService;
    @Resource
    private EmpSalaryChangeDomainService empSalaryChangeDomainService;
    @Resource
    private ContractTypeSetService contractTypeSetService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private TransferTypeService transferTypeService;
    @Resource
    private PostDomainService postDomainService;
    @Resource
    private CompanyDomainService companyDomainService;
    @Resource
    private JobDomainService jobDomainService;
    @Resource
    private WorkplaceService workplaceService;
    @Resource
    private Locker locker;
    @Resource
    private PayeignClient payeignClient;
    @Resource
    private TransferDynamicService transferDynamicService;
    private final Set<String> filterDymaicIdentitySet = Sets.newHashSet("entity.hr.EmpWorkInfo", "entity.hr.EmpPrivateInfo", "entity.hr.EmpWorkOverview", "entity.hr.LastContract");
    @Resource
    private ArchiveEventProducer archiveEventProducer;
    @Resource
    private TransferPublish transferPublish;
    @Resource
    private TransferPostService transferPostService;
    @Resource
    private EmpTagInfoService empTagInfoService;


    public TransferTemplateVo getTemplateData(TransferTemplateDto templateDto) {
        return Optional.ofNullable(templateDto.getApplyId())
                .map(it -> {
                    TransferTemplateVo templateVo = new TransferTemplateVo();
                    TransferApplyVo applyVo = detailVo(templateDto.getApplyId());
                    ChangeDefDo changeDefDo = transferDefService.getEnableOne(null == templateDto.getTypeId()
                            ? applyVo.getTypeId() : templateDto.getTypeId());
                    templateVo.setTemplate(transferDefService.defConvertVO(changeDefDo, true));
                    boolean formChange = StringUtils.isEmpty(changeDefDo.getForm()) ||
                            !changeDefDo.getForm().equals(applyVo.getFormId());
                    templateVo.setFormValueId(formChange ? null : applyVo.getFormValueId());
                    templateVo.setData(getApplyData(applyVo, changeDefDo));
                    return templateVo;
                })
                .orElseGet(() -> {
                    PreCheck.preCheckArgument(StringUtils.isEmpty(templateDto.getTypeId()), "获取数据失败，缺失必要参数");
                    TransferTemplateVo templateVo = new TransferTemplateVo();
                    templateVo.setTemplate(transferDefService.getEnableOneVO(templateDto.getTypeId()));
                    templateVo.setData(getDefaultApplyData(templateVo.getTemplate(), templateDto.getEmpId()));
                    return templateVo;
                });
    }

    private List<TransferFieldVo> getApplyData(TransferApplyVo applyVo, ChangeDefDo changeDefDo) {
        List<ChangeFieldDef> fieldDefList = TransferDefService.toFieldDefList(changeDefDo);
        List<String> keys = fieldDefList.stream().map(ChangeFieldDef::getProperty).collect(Collectors.toList());
        return applyVo.getData().stream().filter(it -> skipStandProps(it.getProperty())
                || keys.contains(it.getProperty())).collect(Collectors.toList());
    }

    @SneakyThrows
    public boolean skipStandProps(String property) {
        Field[] fields = TransferApplyDo.class.getDeclaredFields();
        return Arrays.stream(fields).anyMatch(it -> it.getName().equals(property));
    }

    public List<TransferFieldVo> getDefaultApplyData(ChangeDefVo changeDefVo, String empId) {
        // 合同相关参数
        List<TransferFieldVo> defaultValues = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(changeDefVo.getContractPropsData())) {
            List<ContractDo> contractDoList = contractService.getEmpCurrentContract(empId);
            ContractDo contractDo = CollectionUtils.isNotEmpty(contractDoList) ?
                    contractDoList.stream()
                            .filter(it -> it.getContractStatus().getValue().equals(ContractStatusEnum.EFFECTIVE.getIndex()))
                            .findFirst().orElse(null) : null;
            if (contractDo != null) {
                defaultValues.addAll(fieldListByData(contractDo, changeDefVo.getContractPropsData()));
            }
        }
        // 任职信息
        if (CollectionUtils.isNotEmpty(changeDefVo.getWorkPropsData())) {
            EmpWorkInfoDo workInfoDo = empWorkInfoService.getEmpWorkInfo(empId, System.currentTimeMillis());
            if (workInfoDo != null) {
                List<ChangeFieldDef> fieldDefList = changeDefVo.getWorkPropsData();
                defaultValues.addAll(fieldListByData(workInfoDo, TransferDefService.addLinkProperty(fieldDefList)));
            }
        }
        // 薪资变动
        EmpSalaryChangeDo salaryChangeDo = empSalaryChangeDomainService.getListByEffectDate(empId);
        if (salaryChangeDo != null) {
            defaultValues.addAll(fieldListByData(salaryChangeDo, changeDefVo.getSalaryPropsData()));
        }
        return defaultValues;
    }

    public List<TransferFieldVo> fieldListByData(DataSimple dataSimple, List<ChangeFieldDef> fieldList) {
        NestPropertyValue props = dataSimple.getProperties();
        List<TransferFieldVo> collect = fieldList.stream().map(it -> {
            TransferFieldVo item = ObjectConverter.convert(it, TransferFieldVo.class);
            item.setEnable(true);
            PropertyValue value = props.get(it.getProperty());
            item.setBefore(parsePropertyData(value));
            if (StringUtils.isNotBlank(it.getDisplayProperty())) {
                SimplePropertyValue simpleValue = (SimplePropertyValue) props.get(it.getDisplayProperty());
                String propValue = simpleValue == null ? "" : simpleValue.getValue();
                item.setBeforeTxt(propValue);
            } else {
                try {
                    PropertyValue beforeTxt = getTxtFieldPropertyValue(it, props.get(it.getProperty()));
                    item.setBeforeTxt(((SimplePropertyValue) beforeTxt).getValue());
                } catch (Exception e) {
                    log.error("getTxtFieldPropertyValue err, it = {}, pv = {}, errmsg={}",
                            FastjsonUtil.toJson(it), FastjsonUtil.toJson(props.get(it.getProperty())), e);
                }
            }
            return item;
        }).collect(Collectors.toList());
        return /*fillLinkPropertyData(collect, fieldList, props)*/collect;
    }

    private Object parsePropertyData(PropertyValue propertyValue) {
        if (propertyValue == null) return null;
        if (propertyValue instanceof SimplePropertyValue) {
            return ((SimplePropertyValue) propertyValue).getValue();
        } else if (propertyValue instanceof EnumSimple) {
            return ((EnumSimple) propertyValue).getValue();
        } else if (propertyValue instanceof DictSimple) {
            return ((DictSimple) propertyValue).getValue();
        } else {
            return propertyValue;
        }
    }

    /**
     * 获取归档文件
     *
     * @param page
     * @return
     */
    public List<DataSimple> getArchiveData(BasePage page) {
        return transferEsService.getArchiveData(page);
    }

    public PageResult<TransferListVo> pageApply(TransferQueryDto queryDto) {
        PageResult<DataSimple> pageResult = transferEsService.getPageList(queryDto);
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            List<TransferListVo> items = pageResult.getItems().stream().map(it -> {
                TransferListVo vo = new TransferListVo();
                DataSimpleUtil.convertBaseData(it, vo, ChangeDefEnum.OTHER.display());
                vo.setBusinessKey(vo.getId() + "_" + (vo.isPortalApply() ? TransferDefService.WORKFLOW_CODE_PREFIX_EMP : TransferDefService.WORKFLOW_CODE_PREFIX) + vo.getDefId());
                EmpSimple empSimple = vo.getEmp();
                if (empSimple != null) {
                    vo.setName(empSimple.getName());
                    vo.setEnName(empSimple.getEnName());
                    vo.setWorkno(empSimple.getWorkno());
                }
                doTransferBeforeInfo(it, vo);
                SimplePropertyValue esign = (SimplePropertyValue) it.getProperties().get("esign");
                vo.setEsign(null != esign ? esign.getValue() : "0");
                return vo;
            }).collect(Collectors.toList());
            return new PageResult<>(items, pageResult.getPageNo(), pageResult.getPageSize(),
                    pageResult.getTotal());
        }
        return new PageResult<>(Lists.newArrayList(), pageResult.getPageNo(), pageResult.getPageSize(),
                pageResult.getTotal());
    }

    public PageResult<Map<String, Object>> getTransferOfEmp(TransferQueryDto dto) {
        if (dto == null || StringUtils.isBlank(dto.getTypeId()) || (StringUtils.isBlank(dto.getEmpId()) && StringUtils.isBlank(dto.getKeywords()))) {
            return new PageResult();
        }
        dto.setApprovalStatus(WfTaskActionEnum.APPROVE.value);
        PageResult<Map<String, Object>> page = transferDynamicService.page(dto);
        if (!CollectionUtils.isEmpty(page.getItems())) {
            for (Map<String, Object> item : page.getItems()) {
                Iterator<Map.Entry<String, Object>> iterator = item.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, Object> next = iterator.next();
                    String[] split = next.getKey().split("@");
                    if (split.length > 1 && filterDymaicIdentitySet.contains(split[1])) {
                        iterator.remove();
                    }
                }
            }
        }
        return page;
    }

    private void doTransferBeforeInfo(DataSimple data, TransferListVo vo) {
        NestPropertyValue props = data.getProperties();
        SimplePropertyValue spv = (SimplePropertyValue) props.get("organize");
        vo.setOrganize(null != spv ? spv.getValue() : vo.getOrganize());
        spv = (SimplePropertyValue) props.get("organizeTxt");
        vo.setOrganizeTxt(null != spv ? spv.getValue() : vo.getOrganizeTxt());
        spv = (SimplePropertyValue) props.get("company");
        vo.setCompany(null != spv ? spv.getValue() : vo.getCompany());
        spv = (SimplePropertyValue) props.get("companyTxt");
        vo.setCompanyTxt(null != spv ? spv.getValue() : vo.getCompanyTxt());
        spv = (SimplePropertyValue) props.get("postTxt");
        vo.setPostTxt(null != spv ? spv.getValue() : vo.getPostTxt());
        spv = (SimplePropertyValue) props.get("post");
        vo.setPost(null != spv ? spv.getValue() : vo.getPost());
        spv = (SimplePropertyValue) props.get("hireDate");
        if (null != spv) {
            vo.setHireDate(StringUtil.isNotEmpty(spv.getValue()) ? Long.valueOf(spv.getValue()) : vo.getHireDate());
        }
    }

    public TransferApplyVo detailVo(String applyId) {
        return detailVo(applyId, false);
    }

    public TransferApplyVo detailVo(String applyId, boolean paasMode) {
        DataSimple dataSimple = transferEsService.getTransfer(applyId);
        PreCheck.preCheckArgument(dataSimple == null || StringUtils.isEmpty(dataSimple.getId()), "申请表单不存在");
        return convertApplyData(dataSimple, paasMode);
    }

    public DataSimple detailDo(String applyId) {
        return transferEsService.getTransfer(applyId);
    }

    public void revokeApply(TransferWorkflowDto dto) {
        String applyId = StringUtils.substringBefore(dto.getBusinessKey(), "_");
        TransferApplyVo detail = detailVo(applyId, false);
        Long effectiveDate = StringUtil.isNotEmpty(detail.getEffectiveDate()) ? Long.parseLong(detail.getEffectiveDate()) : 0;
        // 已生效单子不允许重复撤销
        if ((StringUtil.isNotEmpty(detail.getApprovalStatus()) && detail.getApprovalStatus().equals(WfProcessStatusEnum.APPROVE.value))
                && effectiveDate != 0 && effectiveDate.compareTo(com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil.getMidnightTimestamp()) < 0) {
            // 已生效的异动单据不能撤回
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_80004));
        }
        revokeWorkflow(dto);
    }

    private void revokeWorkflow(TransferWorkflowDto workflowDto) {
        WfTaskRevokeDTO wfRevokeDto = new WfTaskRevokeDTO();
        wfRevokeDto.setBusinessKey(workflowDto.getBusinessKey());
        try {
            Result<?> result = wfOperateFeignClient.revokeProcessOfTask(wfRevokeDto);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_40131"));
            }
            // 更新撤销状态
            final String dataId = StringUtils.substringBefore(workflowDto.getBusinessKey(), "_");
            updateApprovalStatus(dataId, ProcesStatusEnum.REVOKE);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
    }

    public void updateApprovalStatus(String applyId, ProcesStatusEnum statusEnum) {
        Map<String, Object> dataMap = transferEsService.getTransferMapData(applyId);
        if (dataMap != null) {
            dataMap.put("approvalStatus", statusEnum.name());
            dataMap.put("updateTime", System.currentTimeMillis());
            dataMap.put("updateBy", UserContext.getUserId());
            transferEsService.updateTransferMapData(dataMap);
        }
    }

    public TransferApplyVo convertApplyData(DataSimple dataSimple, boolean paasMode) {
        TransferApplyVo applyVo = new TransferApplyVo();
        DataSimpleUtil.convertBaseData(dataSimple, applyVo, ChangeDefEnum.OTHER.display());
        applyVo.setId(dataSimple.getId());
        final String changeDefId = applyVo.getDefId();
        PreCheck.preCheckArgument(StringUtils.isEmpty(changeDefId), "未获取到异动配置");
        ChangeDefDo changeDefDo = transferDefService.getById(changeDefId);
        applyVo.setTypeIdTxt(Optional.ofNullable(changeDefDo).map(ChangeDefDo::getName).orElse(""));
        Map<String, ChangeFieldDef> fieldDefMap = TransferDefService.toFieldDefList(changeDefDo).stream()
                .collect(Collectors.toMap(v1 -> String.join("$", v1.getType(), v1.getProperty()),
                        Function.identity(), (o1, o2) -> o1));
        NestPropertyValue propsValues = dataSimple.getProperties();
        List<String> propsKeys = propsValues.keySet().stream().filter(it -> !it.startsWith(OLD_MARK))
                .collect(Collectors.toList());
        Map<String, TransferFieldVo> convertMap = Maps.newHashMap();
        List<TransferFieldVo> fieldVoList = propsKeys.stream().map(it -> {
            final String propertyKey = it.endsWith(ENABLE_MARK) ? it.replace(ENABLE_MARK, "") : it;
            ChangeFieldDef fieldDef = fieldDefMap.getOrDefault(propertyKey, null);
            if (convertMap.containsKey(propertyKey) && convertMap.get(propertyKey) != null) {
                return convertMap.get(propertyKey);
            } else {
                TransferFieldVo fieldVo = Optional.ofNullable(fieldDef)
                        .map(o1 -> getTransferFieldVO(fieldDef, propsValues, paasMode)).orElse(null);
                convertMap.put(propertyKey, fieldVo);
                return fieldVo;
            }
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        applyVo.setData(addPayrollPropertyData(addLinkPropertyData(fieldVoList)));
        applyVo.setBusinessKey(applyVo.getId() + "_" + TransferDefService.WORKFLOW_CODE_PREFIX
                + applyVo.getDefId());
        return applyVo;
    }

    /**
     * 增加关联消息通知节点属性字段
     *
     * @param fieldVoList
     * @return
     */
    public List<TransferFieldVo> addLinkPropertyData(List<TransferFieldVo> fieldVoList) {
        List<String> linkPropertyList = Lists.newArrayList("work$leadEmpId$name", "work$leadEmpId$enName");
        linkPropertyList.forEach(it -> {
            final String propertyKey = StringUtils.substringBeforeLast(it, "$");
            fieldVoList.stream().filter(o1 -> String.join("$", o1.getType(), o1.getProperty()).equals(propertyKey)
                            && (o1.getBefore() != null || o1.getAfter() != null))
                    .findAny().ifPresent(fieldVo -> {
                        final String linkProperty = StringUtils.substringAfterLast(it, "$");
                        PropertyDataType dataType = fieldVo.getPropertyDataType();
                        if (dataType == null) return;
                        // 员工信息类型
                        TransferFieldVo linkField;
                        if (dataType == PropertyDataType.Emp) {
                            linkField = BeanUtil.convert(fieldVo, TransferFieldVo.class);
                            linkField.setPropertyDataType(PropertyDataType.String);
                            linkField.setProperty(String.join("-", fieldVo.getProperty(), linkProperty));
                            try {
                                linkField.setBefore(fieldVo.getBefore() == null ? "" : com.caidaocloud.hr.service.util.BeanUtil.getFieldValue(fieldVo.getBefore(), linkProperty));
                                linkField.setBeforeTxt(null);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            try {
                                linkField.setAfter(fieldVo.getAfter() == null ? "" : com.caidaocloud.hr.service.util.BeanUtil.getFieldValue(fieldVo.getAfter(), linkProperty));
                                linkField.setAfterTxt(null);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            fieldVoList.add(linkField);
                        }
                    });
        });
        return fieldVoList;
    }

    private TransferFieldVo getTransferFieldVO(ChangeFieldDef fieldDef, NestPropertyValue propsValues, boolean paasMode) {
        final String property = String.join("$", fieldDef.getType(), fieldDef.getProperty());
        TransferFieldVo item = new TransferFieldVo();
        item.setProperty(fieldDef.getProperty());
        item.setType(fieldDef.getType());
        item.setPropertyDataType(fieldDef.getDataType());
        PropertyValue enable = propsValues.get(property + ENABLE_MARK);
        item.setEnable(enable != null && Boolean.parseBoolean(((SimplePropertyValue) enable).getValue()));
        PropertyValue before = propsValues.get(OLD_MARK + property);
        item.setBefore(before == null ? null :
                showSimpleData(before) ? getSimpleData(before, paasMode) : before);
        PropertyValue beforeTxt = propsValues.get(OLD_MARK + property + TXT_FIELD_MARK);
        item.setBeforeTxt(beforeTxt == null ? null : ((SimplePropertyValue) beforeTxt).getValue());
        PropertyValue after = propsValues.get(property);
        item.setAfter(after == null ? null : (showSimpleData(after) ? getSimpleData(after, paasMode) : after));
        PropertyValue afterTxt = propsValues.get(property + TXT_FIELD_MARK);
        item.setAfterTxt(afterTxt == null ? null : ((SimplePropertyValue) afterTxt).getValue());
        return item;
    }

    private boolean showSimpleData(PropertyValue propertyValue) {
        return propertyValue instanceof SimplePropertyValue || propertyValue instanceof EnumSimple ||
                propertyValue instanceof DictSimple;
    }

    private Object getSimpleData(PropertyValue propertyValue, boolean paasMode) {
        if (propertyValue instanceof SimplePropertyValue) {
            return ((SimplePropertyValue) propertyValue).getValue();
        } else if (propertyValue instanceof EnumSimple) {
            return paasMode ? propertyValue : ((EnumSimple) propertyValue).getValue();
        } else {
            return paasMode ? propertyValue : ((DictSimple) propertyValue).getValue();
        }
    }

    public String saveApply(TransferApplyDto applyDto) {
        checkPreApplyStatus(applyDto);
        TransferApplyDo transferApplyDo = doTransferApplyData(applyDto, null, null);
        ChangeDefDo defData = transferDefService.getEnableOne(applyDto.getTypeId());
        TransferTypeVo transferType = transferTypeService.getTypeById(defData.getTypeId());
        EnumSimple workflowStatus = new EnumSimple();
        workflowStatus.setValue(WfProcessStatusEnum.IN_PROCESS.value);
        // 流程状态，默认审批中
        transferApplyDo.getProperties().add("approvalStatus", workflowStatus);
        transferApplyDo.getProperties().add("portalApply", Objects.toString(applyDto.getApplyEmp() == TransferApplyEmp.EMP));
        transferEsService.saveTransfer(transferApplyDo);
        boolean empApply = Optional.ofNullable(applyDto.getApplyEmp()).map(it -> it == TransferApplyEmp.EMP).orElse(false);
        val businessKey = String.format("%s_%s%s", transferApplyDo.getId(), empApply ? TransferDefService.WORKFLOW_CODE_PREFIX_EMP :
                TransferDefService.WORKFLOW_CODE_PREFIX, defData.getBid());
        val lock = locker.getLock(String.format("Transfer_%s", businessKey));
        var locked = false;
        try {
            EnumSimple approvalStatus = openWorkflow(applyDto, null, transferType, defData, transferApplyDo);
            locked = lock.tryLock(10, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("The data not be updated, businessKey={} data={}", businessKey, FastjsonUtil.toJson(transferApplyDo));
                return "";
            }
            val transferData = transferEsService.getTransfer(transferApplyDo.getId());
            if (transferData != null && !transferData.getProperties().isEmpty()) {
                if (transferData.getProperties().get("approvalStatus") == null || StringUtils.isBlank(((SimplePropertyValue) transferData.getProperties().get("approvalStatus")).getValue()) || !"IN_PROCESS".equals(approvalStatus.getValue())) {
                    transferData.getProperties().add("approvalStatus", approvalStatus);
                }
            }
            transferEsService.updateTransfer(transferData);
            // 未开启工作流归档文件
            if (approvalStatus.getValue().equals(WfProcessStatusEnum.APPROVE.value)) {
                archiveEventProducer.publishArchiveEvent(ArchiveStandardLine.TRANSFER, transferApplyDo.getId());
            }
            //增加未挂载流程的情况或者不走审批的情况
            if (StringUtils.isNotBlank(approvalStatus.getValue()) && !WfProcessStatusEnum.IN_PROCESS.value.equals(approvalStatus.getValue())) {
                val userInfo = SecurityUserUtil.getSecurityUserInfo();
                var wfCallbackResultDto = new WfCallbackResultDto();
                wfCallbackResultDto.setBusinessKey(businessKey);
                wfCallbackResultDto.setTenantId(userInfo.getTenantId());
                switch (approvalStatus.getValue()) {
                    case "APPROVE":
                        wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
                        break;
                    case "REFUSE":
                        wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.REFUSED);
                        break;
                    case "REVOKE":
                        wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.REVOKE);
                        break;
                    default:
                }
                transferPostService.transferPost(wfCallbackResultDto, Objects.isNull(userInfo.getUserId()) ? "0" : String.valueOf(userInfo.getUserId()));
            }
        } catch (Exception e) {
            log.error("saveApply occur err,{}", e.getMessage(), e);
            transferEsService.deleteTransfer(transferApplyDo.getId());
            if (e instanceof ServerException) {
                throw new ServerException(e.getMessage());
            }
        } finally {
            if (lock != null && locked) {
                lock.unlock();
            }
        }
        return transferApplyDo.getId();
    }

    /**
     * 组装异动申请数据，异动数据存在在datasimple的properties属性中
     * 转换为map存入es
     *
     * @param applyDto
     * @param applyId
     * @param action
     * @return
     */
    private TransferApplyDo doTransferApplyData(TransferApplyDto applyDto, String applyId, WfTaskActionEnum action) {
        PreCheck.preCheckArgument(null == applyDto.getEmp()
                || StringUtil.isEmpty(applyDto.getEmp().getEmpId()), "申请人不存在或为空");
        ChangeDefDo defData = transferDefService.getEnableOne(applyDto.getTypeId());
        return doTransferApplyData(applyDto, applyId, action, defData, null);
    }

    private TransferApplyDo doTransferApplyData(TransferApplyDto applyDto,
                                                String applyId, WfTaskActionEnum action, ChangeDefDo defData, TransferTypeVo transferType) {
        return doTransferApplyData(applyDto, applyId, action, defData, transferType, null);
    }


    /**
     * @param applyDto
     * @param applyId
     * @param action
     * @param defData
     * @param transferType
     * @param existMap
     * @return
     */
    private TransferApplyDo doTransferApplyData(TransferApplyDto applyDto,
                                                String applyId, WfTaskActionEnum action, ChangeDefDo defData, TransferTypeVo transferType, Map<String, Object> existMap) {
          /*PreCheck.preCheckArgument(defData == null || !defData.getStatus().getValue().equals(ChangeDefStatusEnum.ENABLED.getCode()),
                "异动配置不存在或未启用");*/
        PreCheck.preCheckArgument(defData == null, "异动配置不存在或未启用");
        transferType = null == transferType ? transferTypeService.getTypeById(defData.getTypeId()) : transferType;
        PreCheck.preCheckArgument(null == transferType, "异动配置不存在或未启用");
        TransferApplyDo transferApplyDo = new TransferApplyDo();
        transferApplyDo.setId(null == applyId ? SnowUtil.nextId() : applyId);
        transferApplyDo.setFormId(defData.getForm());
        transferApplyDo.setFormValueId(applyDto.getFormValueId());
        BeanUtil.copyProperties(applyDto, transferApplyDo);
        List<String> writableFields = null;
        if (applyDto instanceof TransferApplyApprovalDto) {
            writableFields = ((TransferApplyApprovalDto) applyDto).getWritableFields();
        }
        Long effectiveDate = CollectionUtils.isEmpty(applyDto.getOther()) ? null : applyDto.getOther().stream().filter(e -> "effectiveDate".equals(e.getProperty()) && Objects.nonNull(e.getValue())).map(e -> Long.valueOf(e.getValue().toString())).findFirst().orElse(null);
        // 属性转换
        buildDataProps(defData, transferApplyDo, applyDto, effectiveDate);
        // 记录旧数据
        putOldEmpInfo(transferApplyDo);
        // 上级领导人
        doLeaderOrgPost(applyDto.getWork(), transferApplyDo, effectiveDate);
        // 校验数据
        checkApplyInfo(transferApplyDo, defData, existMap);
        // 保存表单数据
        saveFormData(transferApplyDo, applyDto.getFormData(), writableFields, applyDto.getOther());
        return transferApplyDo;
    }

    /**
     * 校验审批数据是否合法
     * 1.校验合同开始时间
     *
     * @param transferApplyDo 新增异动审批数据
     * @param def             异动定义
     * @param esMap           es异动已有数据
     */
    private void checkApplyInfo(TransferApplyDo transferApplyDo, ChangeDefDo def, Map<String, Object> esMap) {
        DataSimple dataSimple = transferEsService.map2DataSimple(esMap, def);
        NestPropertyValue newMap = transferApplyDo.getProperties(), existMap = dataSimple.getProperties();
        String empId = transferApplyDo.getEmp().getEmpId();
        String startDate = getPropValue(newMap.getOrDefault("contract$startDate", existMap.get("contract$startDate"))),
                endDate = getPropValue(newMap.getOrDefault("contract$endDate", existMap.get("contract$endDate")));
        String signType = (String) parsePropertyData(newMap.getOrDefault("contract$signType", existMap.get("contract$signType")));
        contractService.checkContractDate(empId, signType, StringUtils.isEmpty(startDate) ? null : Long.valueOf(startDate), StringUtils.isEmpty(endDate) ? null : Long.valueOf(endDate));
    }

    private EnumSimple openWorkflow(TransferApplyDto applyDto, WfTaskActionEnum action, TransferTypeVo transferType,
                                    ChangeDefDo defData, TransferApplyDo transferApplyDo) {
        EnumSimple workflowStatus = new EnumSimple();
        if (null == action) {
            final boolean regFlow = transferType.isRegisterFlow();
            // 增加是否开启工作流
            boolean enableWorkflow = applyDto.getOther().stream().anyMatch(it -> it.getProperty().equals("approval") &&
                    Boolean.parseBoolean(Objects.toString(it.getValue())) && regFlow);
            workflowStatus.setValue(WfProcessStatusEnum.APPROVE.value);
            if (enableWorkflow) {
                beginWorkflow(defData, transferApplyDo, applyDto.getApplyEmp());
                workflowStatus.setValue(WfProcessStatusEnum.IN_PROCESS.value);
            }
        } else {
            workflowStatus.setValue(action.value);
        }
        //增加审批状态
        transferApplyDo.getProperties().add("approvalStatus", workflowStatus);
        return workflowStatus;
    }

    private void doLeaderOrgPost(List<TransferFieldDto> work, TransferApplyDo transferApplyDo, Long effectiveDate) {
        if (null == work) {
            return;
        }
        final long dataTime = Objects.nonNull(effectiveDate) ? effectiveDate : System.currentTimeMillis();
        work.forEach(tf -> {
            if ("leaderOrganize".equals(tf.getProperty()) && null != tf.getValue() && tf.isEnable()) {
                OrgDo org = orgDomainService.selectById(tf.getValue().toString(), dataTime);
                PreCheck.preCheckArgument(null == org || null == org.getBid(), LangUtil.getMsg(com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant.HR_ERROR_CODE_32045));
                final String propertyKey = String.join("$", ChangeDefEnum.WORK.display(), "leaderOrganize");
                transferApplyDo.getProperties().add(propertyKey + TXT_FIELD_MARK, org.getName());
            } else if ("leaderPost".equals(tf.getProperty()) && null != tf.getValue() && tf.isEnable()) {
                PostDo post = postDomainService.selectById(tf.getValue().toString(), dataTime);
                PreCheck.preCheckArgument(null == post || null == post.getBid(), LangUtil.getMsg(com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant.HR_ERROR_CODE_32046));
                final String propertyKey = String.join("$", ChangeDefEnum.WORK.display(), "leaderPost");
                transferApplyDo.getProperties().add(propertyKey + TXT_FIELD_MARK, post.getName());
            }
        });
    }

    private void putOldEmpInfo(TransferApplyDo transferApplyDo) {
        EmpSimple emp = transferApplyDo.getEmp();
        long dataTime = System.currentTimeMillis();
        EmpWorkInfoDo workInfo = empWorkInfoService.getEmpWorkInfo(emp.getEmpId(), dataTime);
        PreCheck.preCheckArgument(null == workInfo
                || StringUtil.isEmpty(workInfo.getEmpId()), "申请人不存在或为空");
        transferApplyDo.getProperties().add("hireDate", nullOrEmpty(workInfo.getHireDate()));
        transferApplyDo.getProperties().add("workHour", workInfo.getWorkHour());
        transferApplyDo.getProperties().add("empType", workInfo.getEmpType());
        transferApplyDo.getProperties().add("empStatus", workInfo.getEmpStatus());
        transferApplyDo.getProperties().add("post", nullOrEmpty(workInfo.getPost()));
        transferApplyDo.getProperties().add("postTxt", nullOrEmpty(workInfo.getPostTxt()));
        transferApplyDo.getProperties().add("workplace", nullOrEmpty(workInfo.getWorkplace()));
        transferApplyDo.getProperties().add("workplaceTxt", nullOrEmpty(workInfo.getWorkplaceTxt()));
        String organize = nullOrEmpty(workInfo.getOrganize()), organizeTxt = nullOrEmpty(workInfo.getOrganizeTxt());
        transferApplyDo.getProperties().add("organize", organize);
        transferApplyDo.getProperties().add("organizeTxt", organizeTxt);
        if (StringUtil.isNotEmpty(organize)) {
            OrgDo byBid = orgDomainService.getByBid(organize, dataTime);
            TreeParent pid = null;
            if (null != byBid && null != (pid = byBid.getPid())) {
                organize = StringUtil.isNotEmpty(pid.getPath()) ? pid.getPath() + "/" + organize : organize;
                transferApplyDo.getProperties().add("organizePath", organize);
                organizeTxt = StringUtil.isNotEmpty(pid.getNamePath()) ? pid.getNamePath() + "/" + organizeTxt : organizeTxt;
                transferApplyDo.getProperties().add("organizePathTxt", organizeTxt);
            }
        }
        transferApplyDo.getProperties().add("companyTxt", nullOrEmpty(workInfo.getCompanyTxt()));
        transferApplyDo.getProperties().add("company", nullOrEmpty(workInfo.getCompany()));
    }

    private void checkPreApplyStatus(TransferApplyDto applyDto) {
        EmpSimple empSimple = applyDto.getEmp();
        PageResult<DataSimple> pageResult = transferEsService.getPageList(new TransferQueryDto().setEmpId(empSimple.getEmpId())
                .setTypeId(applyDto.getTypeId())
                .setApprovalStatus(WfProcessStatusEnum.IN_PROCESS.value));
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(pageResult.getItems()), "该员工存在审批中的数据，无法重复提交申请");
    }

    public String saveReApply(TransferApplyDto applyDto) {
        checkPreApplyStatus(applyDto);
        DataSimple dataSimple = transferEsService.getTransfer(applyDto.getBid());
        PreCheck.preCheckArgument(null == dataSimple || StringUtil.isEmpty(dataSimple.getId()), "异动原单据不存在");
        SimplePropertyValue oldApplyId = (SimplePropertyValue) dataSimple.getProperties().get("oldApplyId");
        PreCheck.preCheckArgument(null != oldApplyId && StringUtil.isNotEmpty(oldApplyId.getValue()), "已重新发起的单据不能再次发起");

        TransferApplyDo transferApplyDo = doTransferApplyData(applyDto, null, null);
        ChangeDefDo defData = transferDefService.getEnableOne(applyDto.getTypeId());
        TransferTypeVo transferType = transferTypeService.getTypeById(defData.getTypeId());
        transferEsService.saveTransfer(transferApplyDo);
        try {
            openWorkflow(applyDto, null, transferType, defData, transferApplyDo);
            transferEsService.updateTransfer(transferApplyDo);
            // 更新历史单据
            dataSimple.getProperties().add("oldApplyId", transferApplyDo.getId());
            transferEsService.updateTransfer(dataSimple);
        } catch (Exception e) {
            log.error("openWorkflow err,{}", e.getMessage(), e);
            transferEsService.deleteTransfer(transferApplyDo.getId());
            if (e instanceof ServerException) {
                throw e;
            }
        }

        return transferApplyDo.getId();
    }

    private Long getDataStartTime(List<TransferFieldDto> other) {
        for (TransferFieldDto dto : other) {
            if ("effectiveDate".equals(dto.getProperty()) && dto.getValue() != null) {
                return Long.valueOf(String.valueOf(dto.getValue()));
            }
        }
        return null;
    }

    private void saveFormData(TransferApplyDo apply, Map<String, Object> formData, List<String> writableFields, List<TransferFieldDto> other) {
        if (StringUtils.isEmpty(apply.getFormId()) || MapUtils.isEmpty(formData)) {
            return;
        }
        FormDataDto dataDto = new FormDataDto();
        if (StringUtil.isEmpty(apply.getFormValueId())) {
            Long effectiveDate = getDataStartTime(other);
            if (effectiveDate != null && effectiveDate.compareTo(com.caidaocloud.hrpaas.metadata.sdk.util.DateUtil.getMidnightTimestamp()) > 0) {
                //未生效 打标签
                empTagInfoService.transferTag(apply.getEmp().getEmpId());
            }

            dataDto.setPropertiesMap(formData);
            dataDto.setTargetId(apply.getEmp().getEmpId());
            dataDto.setDataStartTime(effectiveDate);
            String formDataId = formService.saveFormData(apply.getFormId(), dataDto);
            if (StringUtils.isEmpty(formDataId)) {
                throw new ServerException("Form data saving failed");
            }
            apply.setFormValueId(formDataId);
            apply.getProperties().add("formValueId", formDataId);
            return;
        }

        if (CollectionUtils.isEmpty(writableFields)) {
            return;
        }

        // 表单数据保存
        val oldFormData = formService.getFormDataMap(apply.getFormId(), apply.getFormValueId());
        for (String writableField : writableFields) {
            if (!writableField.startsWith("formData.")) {
                continue;
            }

            String formKey = writableField.replace("formData.", "");
            oldFormData.put(formKey, formData.get(formKey));
        }
        dataDto.setId(apply.getFormValueId());
        dataDto.setPropertiesMap(oldFormData);
        formService.updateFormData(apply.getFormId(), dataDto);
    }

    private ChangeFieldDef checkRequireField(ChangeDefDo changeDefDo, List<TransferFieldDto> fieldDtoList, String type) {
        List<ChangeFieldDef> fieldDefList = TransferDefService.toFieldDefList(changeDefDo);
        List<ChangeFieldDef> requiredList = fieldDefList.stream().filter(it -> it.isRequired() && it.getType().equals(type)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(requiredList)) return null;
        Map<String, TransferFieldDto> applyDataMap = fieldDtoList.stream()
                .collect(Collectors.toMap(it -> String.join("$", it.getType(),
                        it.getProperty()), Function.identity(), (v1, v2) -> v1));
        return requiredList.stream().filter(it -> applyDataMap.isEmpty() || !applyDataMap.containsKey(String.join("$",
                it.getType(), it.getProperty()))
                || applyDataMap.get(String.join("$",
                it.getType(), it.getProperty())).getValue() == null).findFirst().orElse(null);
    }

    private boolean checkRequireField(ChangeDefDo changeDefDo, String propertyKey) {
        List<ChangeFieldDef> fieldDefList = TransferDefService.toFieldDefList(changeDefDo);
        List<ChangeFieldDef> requiredList = fieldDefList.stream().filter(ChangeFieldDef::isRequired).collect(Collectors.toList());
        return requiredList.stream().anyMatch(it -> String.join("$", it.getType(), it.getProperty()).equals(propertyKey));
    }

    private void buildDataProps(ChangeDefDo changeDefDo, TransferApplyDo applyDo, TransferApplyDto applyDto, Long effectiveDate) {
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        applyDo.setTenantId(userInfo.getTenantId());
        NestPropertyValue properties = applyDo.getProperties();
        properties.add("defId", changeDefDo.getBid());
        properties.add("typeId", changeDefDo.getTypeId());
        properties.add("formId", applyDo.getFormId());
        properties.add("formValueId", applyDo.getFormValueId());
        properties.add("remark", applyDto.getRemark());
        List<ChangeFieldDef> fieldDefList = TransferDefService.toFieldDefList(changeDefDo);
        Map<String, List<TransferFieldDto>> transferFieldMap = Maps.newHashMap();
        transferFieldMap.put(ChangeDefEnum.CONTRACT.display(), applyDto.getContract());
        transferFieldMap.put(ChangeDefEnum.WORK.display(), applyDto.getWork());
        transferFieldMap.put(ChangeDefEnum.SALARY.display(), applyDto.getSalary());
        transferFieldMap.put(ChangeDefEnum.OTHER.display(), applyDto.getOther());
        transferFieldMap.keySet().forEach(key -> {
            List<TransferFieldDto> fieldDtoList = transferFieldMap.getOrDefault(key, Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(fieldDtoList)) {
                fieldDtoList.forEach(it -> {
                    ChangeFieldDef fieldDef = fieldDefList.stream().filter(o1 -> String.join("$", key, it.getProperty()).equals(
                            String.join("$", o1.getType(), o1.getProperty())
                    )).findFirst().orElse(null);
                    if (fieldDef != null) {
                        it.setType(fieldDef.getType());
                        it.setPropertyDataType(fieldDef.getDataType());
                    } else {
                        it.setType(key);
                        it.setPropertyDataType(PropertyDataType.String);
                    }
                });
            }
            if (CollectionUtils.isEmpty(fieldDtoList)) return;
            // 必填字段需要根据模板来判断
            /*ChangeFieldDef fieldRequire = checkRequireField(changeDefDo, fieldDtoList, key);
            PreCheck.preCheckArgument(fieldRequire != null, "缺少必填字段:"
                    + (fieldRequire != null ? fieldRequire.getName() : null));*/
            savePropertyData(key, fieldDtoList, applyDo.getEmp(), fieldDefList, properties, effectiveDate, BooleanUtils.toBooleanDefaultIfNull(applyDto.getUseEffectiveDateWithTimeLine(), false));
        });
        // 增加申请人信息
        if (applyDto.getEmp() != null) {
            properties.add("emp", applyDo.getEmp());
        }
    }

    private DataSimple getEmp(boolean useEffectiveDateWithTimeLine, Long effectiveDate, String empId) {
        if (!(useEffectiveDateWithTimeLine && Objects.nonNull(effectiveDate))) {
            return empWorkInfoService.getEmpWorkInfo(empId, System.currentTimeMillis());
        }
        // 获取员工信息列表
        List<EmpWorkInfoDo> list = empWorkInfoService.getNoLeaveEmpWorkInfoByEmpIds(Lists.newArrayList(empId), -1L);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        // 按dataStartTime排序
        list.sort((a, b) -> {
            Long timeA = a.getDataStartTime();
            Long timeB = b.getDataStartTime();
            return timeA.compareTo(timeB);
        });
        // 查找符合条件的记录
        EmpWorkInfoDo result = null;
        for (int i = 0; i < list.size(); i++) {
            EmpWorkInfoDo current = list.get(i);
            val startTime = current.getDataStartTime();
            val endTime = current.getDataEndTime();
            if (effectiveDate.longValue() >= startTime && effectiveDate.longValue() <= endTime) {
                // 如果effectiveDate等于dataStartTime，返回上一条记录
                if (effectiveDate.equals(startTime) && i > 0) {
                    result = list.get(i - 1);
                } else {
                    result = current;
                }
                break;
            }
        }
        PreCheck.preCheckArgument(Objects.isNull(result), ErrorMessage.fromCode("caidao.exception.tranfer.no.emp"));
        return result;
    }

    private void savePropertyData(String type, List<TransferFieldDto> fieldDtoList, EmpSimple emp, List<ChangeFieldDef> changeFieldDefs, NestPropertyValue properties, Long effectiveDate, boolean useEffectiveDateWithTimeLine) {
        final DataSimple dataSimple;
        String empId = emp.getEmpId();
        if (type.equals(ChangeDefEnum.CONTRACT.display())) {
            List<ContractDo> contractDoList = contractService.getEmpCurrentContract(empId);
            dataSimple = CollectionUtils.isNotEmpty(contractDoList) ?
                    contractDoList.stream()
                            .filter(it -> it.getContractStatus().getValue().equals(ContractStatusEnum.EFFECTIVE.getIndex()))
                            .findFirst().orElse(null) : null;
        } else if (type.equals(ChangeDefEnum.WORK.display())) {
            dataSimple = getEmp(useEffectiveDateWithTimeLine, effectiveDate, empId);
        } else if (type.equals(ChangeDefEnum.SALARY.display())) {
            // 薪资变动
            dataSimple = empSalaryChangeDomainService.getListByEffectDate(empId);
        } else {
            dataSimple = null;
        }
        fieldDtoList.forEach(fieldDto -> {
            final String propertyKey = StringUtils.isEmpty(fieldDto.getType()) ? fieldDto.getProperty() : String.join("$", fieldDto.getType(), fieldDto.getProperty());
            final PropertyDataType dataType = fieldDto.getPropertyDataType();
            PreCheck.preCheckArgument(dataType == null, "数据类型错误");
            Object value = fieldDto.getValue();
            ChangeFieldDef fieldDef = changeFieldDefs.stream().filter(it -> String.join("$", it.getType(), it.getProperty()).equals(propertyKey))
                    .findAny().orElse(new ChangeFieldDef()
                            .setType(fieldDto.getType())
                            .setProperty(fieldDto.getProperty())
                            .setDataType(dataType));
            saveItemPropertyValue(dataSimple, fieldDef, properties, fieldDto, value, effectiveDate);
        });
    }

    public void saveItemPropertyValue(DataSimple dataSimple, ChangeFieldDef changeFieldDef, NestPropertyValue props, TransferFieldDto fieldDto, Object value, Long effectiveDate) {
        String saveProperty = StringUtils.isEmpty(changeFieldDef.getType()) ? changeFieldDef.getProperty() : String.join("$", changeFieldDef.getType(), changeFieldDef.getProperty());
        if (dataSimple != null) {
            PropertyValue fieldValue = getFieldPropertyValueByName(dataSimple, changeFieldDef.getProperty());
            props.add(OLD_MARK + saveProperty, fieldValue == null ? new SimplePropertyValue("") : fieldValue);
            if (StringUtils.isNotBlank(changeFieldDef.getDisplayProperty())) {
                props.add(OLD_MARK + saveProperty + TXT_FIELD_MARK, getFieldPropertyValueByName(dataSimple,
                        changeFieldDef.getDisplayProperty()));
            } else {
                props.add(OLD_MARK + saveProperty + TXT_FIELD_MARK, getTxtFieldPropertyValue(changeFieldDef,
                        fieldValue));
            }
        }
        doSysFieldValue(changeFieldDef, props, fieldDto, saveProperty, value, effectiveDate);
    }

    private void doSysFieldValue(ChangeFieldDef fieldDef, NestPropertyValue props, TransferFieldDto fieldDto, String saveProperty, Object value, Long effectiveDate) {
        // log.warn("--------------fieldDtoJson={}", FastjsonUtil.toJson(fieldDto));
        final String property = fieldDef.getProperty();
        final boolean enable = fieldDto.isEnable() || fieldDef.getType().equals(ChangeDefEnum.OTHER.display());
        props.add(saveProperty + ENABLE_MARK, Objects.toString(enable));
        props.add(property + "Change", Objects.toString(enable));
        effectiveDate = Objects.nonNull(effectiveDate) ? effectiveDate : System.currentTimeMillis();
        // doRecordChange(props, property, enable);
        if (enable) {
            PropertyValue saveValue = getFieldPropertyValue(fieldDef, value);
            props.add(saveProperty, saveValue);
            if ("company".equals(property)) {
                CompanyDo companyData = companyDomainService.selectById(getPropValue(value));
                props.add(saveProperty + TXT_FIELD_MARK, null == companyData || null == companyData.getBid()
                        ? "" : companyData.getCompanyName());
            } else if ("job".equals(property)) {
                JobDo jobData = jobDomainService.selectById(getPropValue(value));
                props.add(saveProperty + TXT_FIELD_MARK, null == jobData || null == jobData.getBid()
                        ? "" : jobData.getName());
            } else if ("post".equals(property)) {
                PostDo data = postDomainService.selectById(getPropValue(value), effectiveDate);
                props.add(saveProperty + TXT_FIELD_MARK, null == data || null == data.getBid()
                        ? "" : data.getName());
            } else if ("organize".equals(property)) {
                OrgDo data = orgDomainService.selectById(getPropValue(value), effectiveDate);
                props.add(saveProperty + TXT_FIELD_MARK, null == data || null == data.getBid()
                        ? "" : data.getName());
            } else if ("contractTypeSet".equals(property)) {
                ContractTypeSetVo data = contractTypeSetService.getDetail(getPropValue(value));
                props.add(saveProperty + TXT_FIELD_MARK, null == data || null == data.getBid()
                        ? "" : data.getContractTypeTxt());
            } else if ("workplace".equals(property)) {
                WorkplaceDo data = workplaceService.getWorkplaceById(getPropValue(value));
                props.add(saveProperty + TXT_FIELD_MARK, null == data || null == data.getBid()
                        ? "" : data.getName());
            } else {
                props.add(saveProperty + TXT_FIELD_MARK, getTxtFieldPropertyValue(fieldDef, saveValue));
            }
        }
    }

    private void doRecordChange(NestPropertyValue props, String property, boolean enable) {
        List<String> recordKeyList = Lists.newArrayList("company", "salary", "socialSecurity",
                "providentFund", "post", "jobGrade", "organize", "workplace");
        boolean anyMatch = recordKeyList.stream().anyMatch(it -> it.equals(property));
        if (anyMatch) {
            props.add(property + "Change", Objects.toString(enable));
        } else if (enable) {
            props.add(property + "Change", Objects.toString(enable));
        }
    }

    private String getPropValue(Object value) {
        if (value instanceof SimplePropertyValue) {
            SimplePropertyValue simpleValue = (SimplePropertyValue) value;
            return simpleValue.getValue();
        }
        return (String) value;
    }

    private PropertyValue getFieldPropertyValueByName(DataSimple dataSimple, String property) {
        NestPropertyValue properties = dataSimple.getProperties();
        return properties.keySet().stream()
                .filter(it -> it.equals(property))
                .map(properties::get)
                .filter(Objects::nonNull)
                .findAny()
                .orElse(null);
    }

    private PropertyValue getFieldPropertyValue(ChangeFieldDef def, Object value) {
        final PropertyDataType dataType = def.getDataType();
        switch (dataType) {
            case Enum:
                EnumSimple enumSimple = new EnumSimple();
                if (null == value) {
                    return enumSimple;
                }
                enumSimple.setValue(Objects.toString(value));
                if (CollectionUtils.isNotEmpty(def.getEnumDef())) {
                    PropertyEnumDefDto defDto = def.getEnumDef().stream().filter(it -> it.getValue().equals(enumSimple.getValue())).findFirst().orElse(null);
                    enumSimple.setText(defDto != null ? defDto.getDisplay() : enumSimple.getValue());
                }
                return enumSimple;
            case Attachment:
                return null == value ? new Attachment() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), Attachment.class);
            case Emp:
                return null == value ? new EmpSimple() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), EmpSimple.class);
            case Dict:
                return null == value ? new DictSimple() : DictSimple.doDictSimple(Objects.toString(value, null));
            case Job_Grade_Range:
                return null == value ? new JobGradeRange() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), JobGradeRange.class);
            case Address:
                return null == value ? new Address() : FastjsonUtil.toObject(FastjsonUtil.toJson(value), Address.class);
            default:
                return new SimplePropertyValue(Objects.toString(null == value ? "" : value));
        }
    }

    private PropertyValue getTxtFieldPropertyValue(ChangeFieldDef def, PropertyValue propertyValue) {
        boolean isSimple = propertyValue == null;
        isSimple = isSimple || (propertyValue instanceof SimplePropertyValue && StringUtil.isEmpty(((SimplePropertyValue) propertyValue).getValue()));
        if (isSimple) {
            return new SimplePropertyValue("");
        }

        PropertyDataType dataType = def.getDataType();
        String textValue;
        switch (dataType) {
            case Enum:
                EnumSimple enumSimple = (EnumSimple) propertyValue;
                textValue = enumSimple.getText();
                break;
            case Attachment:
                Attachment attachment = (Attachment) propertyValue;
                textValue = CollectionUtils.isNotEmpty(attachment.getNames()) ? attachment.getNames().get(0) : "";
                break;
            case Emp:
                EmpSimple empSimple = (EmpSimple) propertyValue;
                textValue = empSimple.getName();
                break;
            case Dict:
                DictSimple dictSimple = (DictSimple) propertyValue;
                textValue = dictSimple.getText();
                break;
            case Job_Grade_Range:
                JobGradeRange jobGradeRange = (JobGradeRange) propertyValue;
                textValue = jobGradeRange.getStartGradeName();
                break;
            case Timestamp:
                SimplePropertyValue timeProperty = (SimplePropertyValue) propertyValue;
                textValue = StringUtils.isNotBlank(timeProperty.getValue()) ?
                        DateUtil.formatDate(new Date(Long.parseLong(timeProperty.getValue()))) : null;
                break;
            case Boolean:
                SimplePropertyValue statusProperty = (SimplePropertyValue) propertyValue;
                textValue = Boolean.valueOf(statusProperty.getValue()) ? "是" : "否";
                break;
            case Address:
                try {
                    QueryInfoCache.init();
                    Address address = (Address) propertyValue;
                    address.setValue(String.join("/", Objects.toString(address.getProvince()),
                            Objects.toString(address.getCity()), Objects.toString(address.getArea())));
                    Address.doAddress(address);
                    textValue = address.getText();
                } catch (Exception e) {
                    log.error("getTxtFieldPropertyValue pv to address err,{}", e.getMessage(), e);
                    textValue = "";
                } finally {
                    QueryInfoCache.clear();
                }
                break;
            default:
                if (propertyValue instanceof SimplePropertyValue) {
                    SimplePropertyValue simpleValue = (SimplePropertyValue) propertyValue;
                    textValue = simpleValue.getValue() == null ? "" : Objects.toString(simpleValue.getValue());
                } else {
                    log.info("save convert property data error, def:{}, value:{}", FastjsonUtil.toJson(def),
                            FastjsonUtil.toJson(propertyValue));
                    textValue = "";
                }
                break;
        }
        return new SimplePropertyValue(textValue);
    }


    private void beginWorkflow(ChangeDefDo changeDefDo, TransferApplyDo apply, TransferApplyEmp applyEmp) {
        WfBeginWorkflowDto workflowDto = new WfBeginWorkflowDto();
        if (TransferApplyEmp.EMP.equals(applyEmp)) {
            workflowDto.setFuncCode(TransferDefService.WORKFLOW_CODE_PREFIX_EMP + changeDefDo.getBid());
        } else {
            workflowDto.setFuncCode(TransferDefService.WORKFLOW_CODE_PREFIX + changeDefDo.getBid());
        }
        workflowDto.setBusinessId(apply.getId());
        workflowDto.setApplicantId(apply.getEmp().getEmpId());
        workflowDto.setApplicantName(apply.getEmp().getName());
        // 业务单据事件时间
        SimplePropertyValue effectiveDate = (SimplePropertyValue) apply.getProperties().get("other$effectiveDate");
        workflowDto.setEventTime(null == effectiveDate || StringUtil.isEmpty(effectiveDate.getValue()) ? System.currentTimeMillis() : Long.parseLong(effectiveDate.getValue()));
        Result<?> wfResult = null;
        try {
            wfResult = iWfRegisterFeign.begin(workflowDto);
        } catch (Exception e) {
            log.error("beginWorkflow err,{}", e.getMessage(), e);
        }
        if (null == wfResult || !wfResult.isSuccess()) {
            Object msg = wfResult.getData();
            WorkFlowUtil.beginCallback(msg);
            throw new ServerException(msg.toString());
        }

        WorkFlowUtil.beginCallback(wfResult.getData());
    }

    public void updateEsignStatus(String transferId, String processStatus) {
        Map<String, Object> dataMap = transferEsService.getTransferMapData(transferId);
        if (null == dataMap) {
            log.info("updateEsignStatus fail transferId={} data empty", transferId);
            return;
        }
        String esign = "0";// 文件签署发起状态：1:已发起  0:未发起
        SignProcessStatusEnum signProcessStatus = SignProcessStatusEnum.getByValue(processStatus);
        if (null != signProcessStatus) {
            esign = SignProcessStatusEnum.initiate != signProcessStatus ? "1" : esign;
        }
        dataMap.put("esign", esign);
        dataMap.put("updateTime", System.currentTimeMillis());
        transferEsService.updateTransferMapData(dataMap);
    }

    /**
     * 审批通过，由于单据审批时未提交，采用map直接保存
     *
     * @param dto
     */
    public void approve(TransferApplyApprovalDto dto) {
        updateApply(dto, WfTaskActionEnum.IN_PROCESS);
        approveWorkflow(dto, WfTaskActionEnum.APPROVE);
    }

    private void saveSalaryData(TransferApplyApprovalDto dto) {
        List<TransferFieldDto> other = dto.getOther();
        for (TransferFieldDto transferFieldDto : other) {
            if (transferFieldDto.getProperty().equals("salary")) {
                if (ObjectUtil.isNotEmpty(transferFieldDto.getValue())) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    Result result = null;
                    try {
                        List<PayFixItemByUpdateTimeVo> list = objectMapper.readValue(transferFieldDto.getValue().toString(), new TypeReference<List<PayFixItemByUpdateTimeVo>>() {
                        });
                        list.remove(list.size() - 1);
                        result = payeignClient.savePayFixInfoAss(list);
                    } catch (Exception e) {
                        //如果报错是 token非法 可能是202环境问题，缺少token的验证
                        e.printStackTrace();
                        log.error("报酬支付模块接口报错" + e);
                    }
                    if (result.getCode() != 0) {
                        log.error(result.getMsg());
                        throw new ServerException(result.getMsg());

                    }

                }
            }
        }
    }

    private void updateApply(TransferApplyApprovalDto dto, WfTaskActionEnum statusEnum) {
        log.info("Execute approval approval or rejection. dto={}, statusEnum={}", FastjsonUtil.toJson(dto), statusEnum.value);
        Boolean isDone = null;
        try {
            val lock = locker.getLock("Transfer_" + dto.getBusinessKey());
            val locked = lock.tryLock(10, TimeUnit.SECONDS);
            if (locked) {
                try {
                    val status = doUpdateApply(dto, statusEnum);
                    isDone = Objects.nonNull(status) ? !status : null;
                } finally {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("update transfer Apply err", e);
        }
        if (Objects.isNull(isDone)) {
            throw ServerException.globalException("caidao.exception.error_80001");
        } else if (isDone) {
            throw ServerException.globalException("caidao.exception.process.done");
        }
    }

    /**
     * 审批更新异动单据数据
     *
     * @param dto
     * @param statusEnum
     * @return false 已审批不做业务数据更新;true 审批中的数据正常更新
     */
    private Boolean doUpdateApply(TransferApplyApprovalDto dto, WfTaskActionEnum statusEnum) {
        String applyId = StringUtils.substringBefore(dto.getBusinessKey(), "_");
        String defId = StringUtils.substringAfterLast(dto.getBusinessKey(), "-");
        // 获取原始单据value
        Map<String, Object> dataMap = transferEsService.getTransferMapData(applyId);
        String appStatus = null;
        if (null != dataMap && null != (appStatus = (String) dataMap.get("approvalStatus"))
                && !WfTaskActionEnum.IN_PROCESS.value.equals(appStatus)) {
            // 如果流程状态已经是流程审批通过，则不进行更新。
            log.info("process was done, businessKey={} appStatus={}", dto.getBusinessKey(), appStatus);
            return false;
        }
        log.info("appStatus = {}", appStatus);
        // 获取保存值value
        ChangeDefDo defData = transferDefService.getById(defId);
        TransferTypeVo type = transferTypeService.getTypeById(defData.getTypeId());
        val isException = PaasTransactionalUtil.manualTransactional(() -> {
            TransferApplyDo transferApplyDo = doTransferApplyData(dto, dto.getBid(), statusEnum, defData, type, dataMap);
            Map<String, Object> approvalDataMap = transferEsService.parseTransferMap(transferApplyDo);
            if (approvalDataMap != null && !approvalDataMap.isEmpty()) {
                for (String key : approvalDataMap.keySet()) {
                    dataMap.put(key, approvalDataMap.get(key));
                }
                dataMap.put("approvalStatus", statusEnum.value);
                dataMap.put("updateTime", System.currentTimeMillis());
                dataMap.put("updateBy", UserContext.getUserId());
                transferEsService.updateTransferMapData(dataMap);
            }
        });
        if (isException) {
            return null;
        }
        return true;
    }

    public void refuse(TransferApplyApprovalDto dto, WfTaskActionEnum choice) {
        updateApply(dto, WfTaskActionEnum.IN_PROCESS);
        approveWorkflow(dto, choice);
    }

    public void approveWorkflow(TransferApplyApprovalDto dto, WfTaskActionEnum choice) {
        WfTaskApproveDTO wfApproveTaskDto = new WfTaskApproveDTO();
        wfApproveTaskDto.setChoice(choice);
        wfApproveTaskDto.setComment(dto.getComment());
        wfApproveTaskDto.setTaskId(dto.getTaskId());
        try {
            String applyId = StringUtils.substringBefore(dto.getBusinessKey(), "_");
            // 获取单据生效时间
            DataSimple transfer = transferEsService.getTransfer(applyId);
            SimplePropertyValue effectiveDate = (SimplePropertyValue) transfer.getProperties().get("other$effectiveDate");
            if (null != effectiveDate && StringUtil.isNotEmpty(effectiveDate.getValue())) {
                // 调用 feign 更新业务单据事件时间
                WfProcessRuDto wfProcessRuDto = new WfProcessRuDto();
                wfProcessRuDto.setBusinessKey(dto.getBusinessKey());
                wfProcessRuDto.setEventTime(Long.parseLong(effectiveDate.getValue()));
                try {
                    iWfRegisterFeign.updateEventTime(wfProcessRuDto);
                } catch (Exception e) {
                    log.error("Transfer updateEventTime err,{}", e.getMessage(), e);
                }
            }
            Result<?> result = wfOperateFeignClient.approveTask(wfApproveTaskDto);
            if (!result.isSuccess()) {
                PreCheck.preCheckArgument(StringUtils.isNotBlank(result.getMsg()), result.getMsg());
                throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_40131"));
            }
        } catch (Exception e) {
            log.error("approveWorkflow err,{}", e.getMessage(), e);
            throw new ServerException(e.getMessage());
        }
    }

    public void remove(String applyId) {
        Map<String, Object> dataMap = transferEsService.getTransferMapData(applyId);
        if (null == dataMap || dataMap.isEmpty()) {
            return;
        }

        String appStatus = (String) dataMap.get("approvalStatus");
        boolean action = ProcesStatusEnum.APPROVE.name().equals(appStatus)
                || ProcesStatusEnum.REVOKE.name().equals(appStatus)
                || ProcesStatusEnum.REFUSE.name().equals(appStatus)
                || ProcesStatusEnum.CANCEL.name().equals(appStatus);
        if (!action) {
            // 如果流程状态不是通过、撤销、拒绝、取消状态，则不能删除
            return;
        }

        transferEsService.deleteTransfer(applyId);
    }

    public void batchRemove(List<String> applyIds) {
        transferEsService.batchDeleteTransfer(applyIds);
    }

    public void update(TransferUpdateDto updateDto) {
        if (StringUtil.isEmpty(updateDto.getApplyId()) || null == updateDto.getFields()
                || updateDto.getFields().isEmpty()) {
            return;
        }

        Map<String, Object> dataMap = transferEsService.getTransferMapData(updateDto.getApplyId());
        if (null == dataMap || dataMap.isEmpty()) {
            return;
        }

        String defId = (String) dataMap.get("defId");
        ChangeDefDo defData = transferDefService.getById(defId);
        if (null == defData) {
            return;
        }

        Map<String, ChangeFieldDef> fieldDefMap = TransferDefService.toFieldDefList(defData).stream()
                .collect(Collectors.toMap(v1 -> String.join("$", v1.getType(), v1.getProperty()),
                        Function.identity(), (o1, o2) -> o1));
        updateDto.getFields().forEach(tf -> {
            String prop = String.join("$", tf.getType(), tf.getProperty());
            if (tf.isEnable()) {
                prop = tf.getProperty();
            } else if (!fieldDefMap.containsKey(prop)) {
                return;
            }

            dataMap.put(prop, tf.getValue());
        });

        transferEsService.updateTransferMapData(dataMap);
    }

    public TransferTemplateDetailVo getDetail(String bid) {
        var detail = new TransferTemplateDetailVo();
        var transferApply = detailVo(bid, false);
        detail.setData(transferApply.getData());
        if (StringUtil.isNotBlank(transferApply.getFormId()) && StringUtil.isNotBlank(transferApply.getFormValueId())) {
            detail.setFormDef(formService.getFormDefById(transferApply.getFormId()));
            detail.setFormData(formService.getFormDataMap(transferApply.getFormId(), transferApply.getFormValueId()));
        }
        return detail;
    }

    public TransferApplyVo getLastApply(String empId) {
        TransferQueryDto queryDto = new TransferQueryDto();
        queryDto.setEmpId(empId);
        queryDto.setOrder("other$effectiveDate");
        queryDto.setSortOrder(SortOrder.DESC);
        queryDto.setPageSize(1);
        queryDto.setPageNo(1);
        PageResult<DataSimple> pageResult = transferEsService.getPageList(queryDto);
        if (null == pageResult || null == pageResult.getItems() || pageResult.getItems().isEmpty()) {
            return new TransferApplyVo();
        }

        return convertApplyData(pageResult.getItems().get(0), false);
    }

    public void terminateTransfer(TerminateTransferDto terminateTransferDto) {
        if (Objects.isNull(terminateTransferDto) || StringUtils.isBlank(terminateTransferDto.getDataId()) || Objects.isNull(terminateTransferDto.getTerminationDate())) {
            log.info("bid is empty, terminateTransferDto={}", terminateTransferDto.toString());
            return;
        }
        DataSimple transferApply = transferEsService.getTransfer(terminateTransferDto.getDataId());
        var b = transferApply == null;
        PreCheck.preCheckArgument(b, ErrorMessage.fromCode("caidao.exception.tranfer.terminate.approved"));
        val approvalStatus = ((SimplePropertyValue) transferApply.getProperties().getOrDefault("approvalStatus", new SimplePropertyValue())).getValue();
        val effectiveDate = ((SimplePropertyValue) transferApply.getProperties().getOrDefault("other$effectiveDate", new SimplePropertyValue())).getValue();
        val endDate = ((SimplePropertyValue) transferApply.getProperties().getOrDefault("other$endDate", new SimplePropertyValue())).getValue();
        val typeId = ((SimplePropertyValue) transferApply.getProperties().getOrDefault("typeId", new SimplePropertyValue())).getValue();
        b = !"APPROVE".equals(approvalStatus) ||
                (StringUtils.isNotBlank(effectiveDate) && terminateTransferDto.getTerminationDate().longValue() < Long.parseLong(effectiveDate)) ||
                (StringUtils.isNotBlank(endDate) && terminateTransferDto.getTerminationDate().longValue() > Long.parseLong(endDate));
        PreCheck.preCheckArgument(b, ErrorMessage.fromCode("caidao.exception.tranfer.terminate.approved"));
        val defOptional = transferTypeService.getTypeList().stream().filter(e -> e.getBid().equals(typeId)).findFirst();
        PreCheck.preCheckArgument(!defOptional.isPresent() || !defOptional.get().isTerminate(), ErrorMessage.fromCode("caidao.exception.tranfer.terminate.enabled"));
        val securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        PreCheck.preCheckArgument(Objects.isNull(securityUserInfo), "please login platform");
        transferApply.getProperties().put("terminationDate", new SimplePropertyValue(terminateTransferDto.getTerminationDate().toString()));
        transferEsService.updateTransfer(transferApply);
        transferPublish.terminateTransfer(new TransferTerminationEventDto(terminateTransferDto.getDataId(), securityUserInfo.getTenantId(), securityUserInfo.getUserId()));
    }

    /**
     * 增加隐藏历史调薪数据配置
     *
     * @param fieldVoList
     * @return
     */
    public List<TransferFieldVo> addPayrollPropertyData(List<TransferFieldVo> fieldVoList) {
        if(hideHisAmount) {
            if(CollectionUtils.isNotEmpty(fieldVoList)){
                for(TransferFieldVo item : fieldVoList){
                    if(item != null){
                        if("salary".equalsIgnoreCase(item.getProperty()) && PropertyDataType.Data_Table.equals(item.getPropertyDataType()) && item.getAfter() != null){
                            if(log.isDebugEnabled()) {
                                log.debug("addPayrollPropertyData request item:{}", FastjsonUtil.toJsonStr(item));
                            }
                            List<Map> payrollDataList = FastjsonUtil.toArrayList(item.getAfter().toString(), Map.class);
                            if(CollectionUtils.isNotEmpty(payrollDataList)){
                                BigDecimal totalAmount = BigDecimal.ZERO;
                                BigDecimal totalHisAmount = BigDecimal.ZERO;
                                List<Map> result = new ArrayList<>();
                                for(Map payrollDataItem : payrollDataList) {
                                    if(payrollDataItem != null) {
                                        String payslipName = ConvertHelper.stringConvert(payrollDataItem.get("payslipName"));
                                        if(!"合计".equalsIgnoreCase(payslipName)) {
                                            BigDecimal amount = ConvertHelper.bigDecimalConvert(payrollDataItem.get("amount"));
                                            BigDecimal hisAmount = ConvertHelper.bigDecimalConvert(payrollDataItem.get("hisAmount"));
                                            if (amount != null && hisAmount != null) {
                                                if (!(amount.compareTo(hisAmount) == 0)) {
                                                    totalAmount = totalAmount.add(amount);
                                                    totalHisAmount = totalHisAmount.add(hisAmount);
                                                    result.add(payrollDataItem);
                                                }
                                            } else {
                                                result.add(payrollDataItem);
                                            }
                                        }
                                    }
                                }
                                Map totalMap = new HashMap<>();
                                totalMap.put("payslipName", "合计");
                                totalMap.put("strucMainName", "");
                                totalMap.put("amount", totalAmount);
                                totalMap.put("hisAmount", totalHisAmount);
                                result.add(totalMap);

                                String resultJson = FastjsonUtil.toJson(result);
                                item.setAfter(resultJson);
                                item.setAfterTxt(resultJson);
                                if(log.isDebugEnabled()) {
                                    log.debug("addPayrollPropertyData response item:{}", FastjsonUtil.toJsonStr(item));
                                }
                            }
                        }
                    }
                }
            }
        }
        return fieldVoList;
    }
}
