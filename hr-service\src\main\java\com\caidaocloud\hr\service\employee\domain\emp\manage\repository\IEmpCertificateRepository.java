package com.caidaocloud.hr.service.employee.domain.emp.manage.repository;

import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpCertificateDo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IEmpCertificateRepository extends BaseRepository<EmpCertificateDo> {

    /**
     * 根据empId查员工证书技能
     *
     * @param empId
     * @param identifier
     * @param tenantId
     * @return
     */
    List<EmpCertificateDo> selectListByEmpId(String empId, String identifier, String tenantId);

    /**
     * 根据bid删除员工证书技能
     *
     * @param bid
     * @param identifier
     */
    void delete(String bid, String identifier);
}
