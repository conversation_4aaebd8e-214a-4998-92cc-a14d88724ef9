package com.caidaocloud.hr.service.employee.domain.workExperience.service;

import com.caidaocloud.hr.service.common.infrastructure.manager.cal.PriorCalculatorManager;
import com.caidaocloud.hr.service.employee.domain.workExperience.entity.WorkOverviewDo;
import com.caidaocloud.hr.service.employee.domain.workExperience.repository.IWorkOverviewRepository;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WorkOverviewDomainService {
    @Resource
    private WorkOverviewDo workOverviewDo;
    @Resource
    private PriorCalculatorManager calculatorManager;
    private static final float DEFAULT_ADJUST = 0F;
    @Value("${caidao.priorCal:defaultWorkOverview}")
    private String priorCal;

    public String save(WorkOverviewDo data) {
        calcWorkAge(data);
        return workOverviewDo.save(data);
    }

    private void calcWorkAge(WorkOverviewDo data) {
        data.setWorkAgeAdjust(Optional.ofNullable(data.getWorkAgeAdjust()).orElse(DEFAULT_ADJUST));
        if(null == data.getFirstWorkDate()){
            return;
        }
        long monthDiff = getMonthDiff(data.getFirstWorkDate(), DateUtil.getCurrentTimestamp());
        BigDecimal divide = new BigDecimal(monthDiff).divide(new BigDecimal("12"), 9, BigDecimal.ROUND_HALF_UP);
        divide = divide.add(BigDecimal.valueOf(data.getWorkAgeAdjust())).setScale(1, BigDecimal.ROUND_HALF_UP);
        data.setWorkAge(divide.toString());
    }

    public int update(WorkOverviewDo data) {
        calcWorkAge(data);
        return workOverviewDo.update(data);
    }

    public WorkOverviewDo selectByEmpId(String empId) {
        WorkOverviewDo data = this.workOverviewDo.selectById(empId);
        if (data.getFirstWorkDate() == null) {
            return data;
        }
        log.info("获取对数据data：{}", data.toString());
        calculatorManager.calculate(priorCal, data);
        return data;
    }

    public List<WorkOverviewDo> listByEmpIds(List<String> empIds){
        List<WorkOverviewDo> list = SpringUtil.getBean(IWorkOverviewRepository.class).selectList(empIds);
        for (WorkOverviewDo data : list) {
            calculatorManager.calculate(priorCal, data);
        }
        return list;
    }

    public long getMonthDiff(long startDate, long endDate) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        Date firstWorkTime = new Date(startDate);
        Date todayTimestamp = new Date(endDate);
        String format1 = sf.format(firstWorkTime);
        String format2 =sf.format(todayTimestamp);
        return ChronoUnit.MONTHS.between(LocalDate.parse(format1), LocalDate.parse(format2));
    }
}
