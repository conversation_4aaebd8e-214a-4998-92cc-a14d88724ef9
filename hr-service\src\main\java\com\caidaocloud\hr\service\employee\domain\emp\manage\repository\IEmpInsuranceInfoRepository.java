package com.caidaocloud.hr.service.employee.domain.emp.manage.repository;

import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepository;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpEduInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpInsuranceInfoDo;

import java.util.List;

public interface IEmpInsuranceInfoRepository extends BaseRepository<EmpInsuranceInfoDo> {
    EmpInsuranceInfoDo selectByEmpId(String empId, String identifier, String tenantId);


    List<EmpInsuranceInfoDo> selectByEmpIds(List<String> empIds, String identifier, String tenantId);

    List<EmpInsuranceInfoDo> selectByPayUnitId(String payUnitId, String identifier, String tenantId);
}
