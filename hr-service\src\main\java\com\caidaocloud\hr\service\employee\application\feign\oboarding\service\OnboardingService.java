package com.caidaocloud.hr.service.employee.application.feign.oboarding.service;

import com.caidaocloud.hr.service.employee.application.feign.oboarding.emp.EmpPrivateFeignClient;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPrivacyQueryDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpIdentificationVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class OnboardingService {
    @Resource
    private EmpPrivateFeignClient empPrivateFeignClient;

    public List<EmpIdentificationVo> getPrivacyInfoList(EmpPrivacyQueryDto dto) {
        Result result = empPrivateFeignClient.getPrivacyInfoList(dto);
        log.info("getPrivacyInfoList {}", FastjsonUtil.toJson(result));
        if (!result.isSuccess()) {
            return Lists.newArrayList();
        }
        Object data = result.getData();
        return FastjsonUtil.toArrayList(FastjsonUtil.toJson(data), EmpIdentificationVo.class);
    }

    public List<EmpIdentificationVo> getEmpProcessInfoList(EmpPrivacyQueryDto dto) {
        Result result = empPrivateFeignClient.getEmpProcessInfoList(dto);
        log.info("getEmpProcessInfoList {}", FastjsonUtil.toJson(result));
        if (!result.isSuccess()) {
            return Lists.newArrayList();
        }
        Object data = result.getData();
        return FastjsonUtil.toArrayList(FastjsonUtil.toJson(data), EmpIdentificationVo.class);
    }
}
