package com.caidaocloud.hr.service.organization.domain.post.entity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpEmpConcurrentPostRepository;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpWorkInfoRepository;
import com.caidaocloud.hr.service.organization.domain.post.repository.IPostRepository;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgOrPostQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.PostQueryDto;
import com.caidaocloud.hr.service.vo.organization.post.PostVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/3
 */
@Service
@Slf4j
@Data
public class PostDo extends DataEntity {

    /**
     * 岗位名称
     */
    private String name;

    /**
     * 岗位名称多语言
     */
    private String i18nName;

    /**
     * 岗位编码
     */
    private String code;

    /**
     * 所属组织ID
     */
    private String orgId;

    /**
     * 所属组织名称
     */
    private String orgName;

    /**
     * 工作地ID
     */
    private String workplaceId;

    /**
     * 工作地名称
     */
    private String workplaceName;

    /**
     * 是否关键岗位
     */
    private Boolean keyPost;

    /**
     * 职级关联
     */
    private JobGradeRange jobGrade;

    /**
     * 关联职务或基准岗位，0：未配置，1：职务，2：基准岗位
     */
    private Integer relation;

    /**
     * 关联基准岗位ID
     */
    private String benchmarkPositionId;

    /**
     * 关联基准岗位名称
     */
    private String benchmarkPositionName;

    /**
     * 关联的职务ID
     */
    private String jobId;

    /**
     * 关联的职务名称
     */
    private String jobName;

    /**
     * 岗位说明
     */
    private String desc;

    /**
     * 岗位说明书
     */
    private Attachment jobDescFiles;

    /**
     * 任职资格
     */
    private String qualification;

    /**
     * 是否更新该岗位下的员工工作地信息
     */
    private Boolean syncEmpPost;

    /**
     * 附件
     */
    private Attachment files;

    public final static String POST_IDENTIFIER = "entity.hr.Post";

    @Resource
    private IPostRepository postRepository;
    @Resource
    private IEmpWorkInfoRepository empWorkInfoRepository;
    @Resource
    private IEmpEmpConcurrentPostRepository empEmpConcurrentPostRepository;

    /**
     * 删除
     *
     * @param bid
     */
    public void delete(String bid, Long dataStartTime) {
        PostDo data = bulidNew();
        data.setBid(bid);
        data.setDataStartTime(dataStartTime);
        postRepository.delete(data);
    }

    public List<EmpWorkInfoDo> getEmpByPost(String bid, Long dateTime) {
        return empWorkInfoRepository.getEmpDisable(UserContext.getTenantId(), EmpWorkInfoDo.IDENTIFIER, null, bid, dateTime);
    }

    public List<EmpConcurrentPostDo> getEnableConcurrentByPost(String postId, Long dateTime) {
        return empEmpConcurrentPostRepository.getEnableConcurrent(UserContext.getTenantId(), EmpConcurrentPostDo.IDENTIFIER, null, postId, dateTime);
    }

    /**
     * 查看详情
     */
    public PostDo selectById(String bid, Long dateTime) {
        return postRepository.selectById(bid, POST_IDENTIFIER, getTimeIfNullSetDefault(dateTime));
    }

    /**
     * 更新状态
     *
     * @param data
     */
    public void updateStatus(PostDo data, BusinessEventTypeEnum eventTypeEnum) {
        PostDo dbData = selectById(data.getBid(), data.getDataStartTime());
        PreCheck.preCheckArgument(null == data || null == data.getBid(), "岗位不存在");
        initFieldValue(POST_IDENTIFIER, eventTypeEnum, data, dbData);
        postRepository.updateById(data);
    }

    /**
     * 新增
     */
    public String save(PostDo data) {
        initFieldValue(POST_IDENTIFIER, BusinessEventTypeEnum.CREATE, data, null);
        return postRepository.insert(data).getBid();
    }

    /**
     * 更新
     */
    public void update(PostDo data) {
        PostDo dbData = selectById(data.getBid(), data.getDataStartTime());
        initFieldValue(POST_IDENTIFIER, BusinessEventTypeEnum.UPDATE, data, dbData);
        postRepository.updateById(data);
    }

    /**
     * 分页
     */
    public PageResult<PostDo> selectPage(PostQueryDto page) {
        PostDo data = bulidNew();
        data.setDataStartTime(getTimeIfNullSetDefault(page.getDateTime()));

        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(null == page.getStatus() ? null : page.getStatus().toString());
        data.setStatus(enumSimple);

        return postRepository.selectPage(page, data);
    }

    /**
     * 列表
     *
     * @return
     */
    public List<PostDo> selectList(String orgId, Integer status, Long dataStartTime, String keyword) {
        PostDo data = bulidNew();
        data.setOrgId(orgId);
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(null == status ? null : status.toString());
        data.setStatus(enumSimple);
        data.setDataStartTime(null == dataStartTime ? System.currentTimeMillis() : dataStartTime);
        data.setName(keyword);
        List<PostDo> list = postRepository.selectList(data);
        return list;
    }

    public List<PostDo> selectListByOrgIdList(OrgOrPostQueryDto orgQueryDto) {
        PostDo data = bulidNew();
        if (null != orgQueryDto.getOrgIdList() && !orgQueryDto.getOrgIdList().isEmpty()) {
            orgQueryDto.setOrgIdList(orgQueryDto.getOrgIdList()
                    .stream().distinct().collect(Collectors.toList()));
        }
        return postRepository.selectListByOrgIdList(data, orgQueryDto);
    }

    public List<PostDo> selectPostDoByCodes(List<String> codes, BasePageQueryDto page) {
        PostDo data = bulidNew();
        data.setDataStartTime(getTimeIfNullSetDefault(page.getDateTime()));

        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(null == page.getStatus() ? null : page.getStatus().toString());
        data.setStatus(enumSimple);
        return postRepository.selectPostDoByCodes(codes, data, page);
    }

    public List<PostDo> getEnablePostByOrg(String orgId, Long dateTime) {
        return postRepository.getEnablePostByOrg(UserContext.getTenantId(), POST_IDENTIFIER, orgId, dateTime);
    }

    public List<PostDo> selectListByJobId(String jobId, Integer pageSize) {
        OrgOrPostQueryDto dto = new OrgOrPostQueryDto();
        dto.setPageNo(1);
        dto.setPageSize(pageSize);
        PostDo data = bulidNew();
        data.setJobId(jobId);
        dto.setDataStartTime(System.currentTimeMillis());
        return postRepository.selectListByOrgIdList(data, dto);
    }

    private PostDo bulidNew() {
        PostDo data = new PostDo();
        data.setIdentifier(POST_IDENTIFIER);
        data.setTenantId(UserContext.getTenantId());
        return data;
    }

    public PageResult<PostDo> selectPostPageListByOrgId(PostQueryDto page) {
        PostDo data = bulidNew();
        data.setDataStartTime(getTimeIfNullSetDefault(page.getDateTime()));
        EnumSimple enumSimple = new EnumSimple();
        enumSimple.setValue(null == page.getStatus() ? null : page.getStatus().toString());
        data.setStatus(enumSimple);
        return postRepository.selectPostPageListByOrgId(page, data);
    }

    public List<PostDo> selectByIds(List<String> postList, Long dataTime) {
        return postRepository.selectBatchIds(postList, POST_IDENTIFIER, dataTime);
    }

    public static void i18Convert(PostDo postDo, PostVo postVo) {
        postVo.setI18nName(StringUtil.isEmpty(postDo.getI18nName()) ?
                ImmutableMap.of("default", Optional.ofNullable(postDo.getName()).orElse("")) :
                FastjsonUtil.toObject(postDo.getI18nName(), new TypeReference<Map<String, Object>>(){})
        );
        if (StringUtil.isNotEmpty(postDo.getI18nName())) {
            postVo.setName(LangUtil.parseI18nValue(postDo.getName(), postDo.getI18nName()));            
        }
    }
}
