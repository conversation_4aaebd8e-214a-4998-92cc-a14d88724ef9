package com.caidaocloud.hr.service.search.application.service;

import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpConcurrentPostService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.search.infrastructure.repository.EmpSearchInfoRepository;
import com.caidaocloud.hr.service.search.infrastructure.repository.EsDataRepository;
import com.caidaocloud.hr.service.search.infrastructure.repository.po.EmpSearchInfoPo;
import com.caidaocloud.hr.service.search.interfaces.dto.EmpUpdateDto;
import com.caidaocloud.hr.service.transfer.application.dto.TransferApplyDto;
import com.caidaocloud.hr.service.transfer.application.service.TransferEsService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.workflow.enums.WfProcessStatusEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class EmpEsSearchService {
    @Resource
    private RestHighLevelClient client;
    @Resource
    private EmpSearchInfoRepository empSearchInfoRepository;
    @Resource
    private EmpWorkInfoSearchService empWorkInfoSearchService;
    @Resource
    private EmpConcurrentPostService empConcurrentPostService;
    @Resource
    private TransferEsService transferEsService;

    @SneakyThrows
    public Object searchEsEmpByWorkno(String workno) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.matchQuery("tenantId", UserContext.getTenantId()));
        boolQuery.must(QueryBuilders.termsQuery("workno.keyword", workno));

        SearchSourceBuilder builder = new SearchSourceBuilder()
                .query(boolQuery)
                //.collapse(collapse)
                .from(0)
                .size(100)
                .sort(SortBuilders.fieldSort("updateTime").order(SortOrder.DESC));

        SearchRequest request = new SearchRequest("index_emp_info")
                .source(builder)
                .searchType(SearchType.QUERY_THEN_FETCH);
        SearchResponse response = client.search(request, RequestOptions.DEFAULT);
        log.info("-------workno={}, response={}", workno, FastjsonUtil.toJson(response));
        return response.getHits();
    }

    @SneakyThrows
    public void deleteEsEmpById(String id){
        UserContext.preCheckUser();
        DeleteRequest getRequest = new DeleteRequest("index_emp_info", id);
        client.delete(getRequest, RequestOptions.DEFAULT);
    }

    @SneakyThrows
    public void updateEsEmpById(EmpUpdateDto empDto) {
        EmpSearchInfoPo empData = empSearchInfoRepository.getById(empDto.getId());
        if(null == empData){
            return;
        }

        Map dataMap = FastjsonUtil.convertObject(empData, Map.class);
        dataMap.put(empDto.getProp(), empDto.getValue());
        EmpSearchInfoPo saveData = FastjsonUtil.convertObject(dataMap, EmpSearchInfoPo.class);
        empSearchInfoRepository.save(saveData);
    }

    public void updateEmpConcurrentSyncEs(List<String> empIds){
        if(empIds.isEmpty()){
            return;
        }

        empIds.forEach(empId -> {
            empWorkInfoSearchService.dataChangeSyncEs(empId,
                    empConcurrentPostService.getEmpConcurrentPostList(empId));
        });
    }

    public void updateEmpTransfer(TransferApplyDto applyDto){
        String id = applyDto.getBid();
        if(StringUtil.isEmpty(id)){
            return;
        }

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("approvalStatus", WfProcessStatusEnum.APPROVE.value);
        Map<String, String> must = new HashMap<>();
        must.put("approvalStatus", WfProcessStatusEnum.IN_PROCESS.value);
        // 原单据状态为审批中的才更新
        transferEsService.updateTransferPartData(id, dataMap, must, null);
    }

    public void rmEsIdx(TransferApplyDto applyDto) {
        String id = applyDto.getBid();
        if(StringUtil.isEmpty(id)){
            return;
        }

        String idx = id + "_" + SecurityUserUtil.getSecurityUserInfo().getTenantId();
        SpringUtil.getBean(EsDataRepository.class).deleteIndex(idx);
    }
}
