package com.caidaocloud.hr.service.employee.domain.emp.manage.entity;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpEduInfoRepository;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpInsuranceInfoRepository;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Address;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DataSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/12
 */
@Slf4j
@Data
@Service
public class EmpInsuranceInfoDo extends DataSimple {
    /**
     * 员工ID
     */
    private String empId;

    /**
     * 缴纳单位bid
     */
    private String unitBid;

    /**
     * 缴纳单位
     */
    private String unitName;

    /**
     * 社保生效日期
     */
    private Long socialEffectiveDate;
    /**
     * 社保缴纳地
     */
    private Address socialSecurity;
    /**
     * 社保账号
     */
    private String socialAccount;

    /**
     * 社保方案id
     */
    private Integer socialPlanId;
    
    private String socialPlanName;

    /**
     * 基数
     */
    private BigDecimal socialBase;

    /**
     * 公积金生效日期
     */
    private Long providentEffectiveDate;

    /**
     * 公积金缴纳地
     */
    private Address providentFund;

    /**
     * 公积金账号
     */
    private String providentAccount;

    /**
     * 公积金方案id
     */
    private Integer providentPlanId;
    
    private String providentPlanName;

    /**
     * 公积金基数
     */
    private BigDecimal providentBase;

    private final static String IDENTIFIER = "entity.hr.EmpInsuranceInfo";

    @Resource
    private IEmpInsuranceInfoRepository empInsuranceInfoRepository;

    public void save(EmpInsuranceInfoDo data) {
        DataEntity.initDataSimpleBaseFieldValue(IDENTIFIER, data, null);
        empInsuranceInfoRepository.insert(data);
    }

    public void update(EmpInsuranceInfoDo data) {
        EmpInsuranceInfoDo source = selectById(data.getEmpId());
        update(data, source);
    }

    public EmpInsuranceInfoDo selectById(String empId) {
        return empInsuranceInfoRepository.selectByEmpId(empId, IDENTIFIER, UserContext.getTenantId());
    }

    public List<EmpInsuranceInfoDo> selectByEmpIds(List<String> empIds) {
        return empInsuranceInfoRepository.selectByEmpIds(empIds, IDENTIFIER, UserContext.getTenantId());
    }
    
    public List<EmpInsuranceInfoDo> selectByPayUnitId(String payUnitId) {
        return empInsuranceInfoRepository.selectByPayUnitId(payUnitId, IDENTIFIER, UserContext.getTenantId());
    }
    
    

    public void update(EmpInsuranceInfoDo data, EmpInsuranceInfoDo source) {
        PreCheck.preCheckArgument(source == null, "数据不存在");
        DataEntity.initDataSimpleBaseFieldValue(IDENTIFIER, data, source);
        empInsuranceInfoRepository.updateById(data);
    }

    public String getDoIdentifier(){
        return IDENTIFIER;
    }
}
