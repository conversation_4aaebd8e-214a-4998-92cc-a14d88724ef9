package com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.employee.domain.base.enums.ResignationStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel("员工分页列表查询条件DTO")
public class EmpPageQueryDto extends BasePage {

    @ApiModelProperty("日期时间（单位毫秒）")
    private Long dateTime;

    @ApiModelProperty("任职组织")
    private String organize;

    @ApiModelProperty("工号或姓名关键字搜索")
    private String keywords;

    @ApiModelProperty("兼岗")
    private boolean concurrentPost = false;

    @ApiModelProperty("员工状态")
    private Integer empStatus;

    @ApiModelProperty("员工类型")
    private String empType;

    @ApiModelProperty("离职日期")
    private Long leaveDate;

    @ApiModelProperty("离职状态")
    private ResignationStatusEnum resignationStatus;

    @ApiModelProperty("试用期截止日期范围")
    private Long[] probationPeriodRange;

    @ApiModelProperty("试用期截止日期")
    private Long probationPeriod;

    @ApiModelProperty("入职开始日期")
    private Long hireStartDate;

    @ApiModelProperty("入职结束日期")
    private Long hireEndDate;

    @ApiModelProperty("岗位")
    private List<String> posts;

    @ApiModelProperty("职务")
    private List<String> jobs;

    @ApiModelProperty("用工类型")
    private List<String> empTypeValues;

    @ApiModelProperty("合同公司")
    private List<String> companys;

    @ApiModelProperty("工作地ID")
    private List<String> workplaces;




}
