package com.caidaocloud.hr.service.organization.application.post.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.hr.service.dto.ruleset.CommonCodeRuleDto;
import com.caidaocloud.hr.service.dto.ruleset.OrgSettingsDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.constant.BaseConstant;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.application.tenant.feign.ITenantFeignClient;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.tenant.TenantRuleDto;
import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class PostCodeRuleService {
    @Resource
    private ITenantFeignClient tenantFeignClient;
    @Resource
    private CacheService cacheService;
    @Resource
    private PostService postService;

    public void save(CommonCodeRuleDto data) {
        KvDto kvDto = new KvDto();
        kvDto.setProperty(getCacheRuleKey());
        kvDto.setContent(FastjsonUtil.toJson(data));
        Result<KvDto> result = tenantFeignClient.saveKv(kvDto);
        if (null == result || !result.isSuccess()) {
            return;
        }

        String autoKey = String.format(HrConstant.AUTO_GENERATE_POST_CODE, UserContext.getTenantId());
        cacheService.remove(autoKey);
        if (null != data.getStartValue()) {
            cacheService.increment(autoKey, data.getStartValue());
        }
    }

    public CommonCodeRuleDto getRuleDetail() {
        return getRuleDetail(getCacheRuleKey());
    }

    public CommonCodeRuleDto getRuleDetail(String key) {
        Result<String> result = tenantFeignClient.getKv(key);
        CommonCodeRuleDto ruleDto = null;
        if (null == result || !result.isSuccess() || null == (ruleDto = FastjsonUtil.toObject(result.getData(), CommonCodeRuleDto.class))) {
            return new CommonCodeRuleDto();
        }

        return ruleDto;
    }

    public String nextPostCode(String inputPostCode, String oldPostCode) {
        return nextPostCode(UserContext.getTenantId(), inputPostCode, oldPostCode);
    }

    public String nextPostCode(String tenantId, String inputPostCode, String oldPostCode) {
        CommonCodeRuleDto rule = loadRule();
        if (null == rule || null == rule.getAutoCreate() || !rule.getAutoCreate()) {
            // 未开启组织编码自动生成
            if (StringUtil.isEmpty(inputPostCode)) {
                PreCheck.preCheckArgument(StringUtil.isEmpty(oldPostCode),
                        LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32035));
                return oldPostCode;
            }

            return inputPostCode;
        }

        if (null != oldPostCode) {
            return oldPostCode;
        }

        String cacheKey = String.format(HrConstant.AUTO_GENERATE_POST_CODE, tenantId);
//        Long currentNo = cacheService.increment(cacheKey);
//
//        Integer length = rule.getLength() - (null == rule.getPrefix() ? "" : rule.getPrefix()).length();
//        String format = String.format(HrConstant.AUTO_GENERATE_FORMAT, rule.getPrefix(), length);
//        return String.format(format, currentNo).replace(BaseConstant.SPACE_SEPARATOR, HrConstant.ZERO_FILLING_RULE);
        return joinPostCode(cacheKey, rule);
    }

    public String joinPostCode(String cacheKey, CommonCodeRuleDto rule) {
        Long currentNo = cacheService.increment(cacheKey);

        Integer length = rule.getLength() - (null == rule.getPrefix() ? "" : rule.getPrefix()).length();
        String format = String.format(HrConstant.AUTO_GENERATE_FORMAT, rule.getPrefix(), length);
        String postCode = String.format(format, currentNo).replace(BaseConstant.SPACE_SEPARATOR, HrConstant.ZERO_FILLING_RULE);

        List<PostDo> postDos = postService.selectPostDoByCodes(Arrays.asList(postCode), System.currentTimeMillis());

        if (postDos.isEmpty()) {
            return postCode;
        } else {
            return joinPostCode(cacheKey, rule);
        }
    }


    public CommonCodeRuleDto loadRule() {
        String key = getCacheRuleKey();
        String cacheKey = String.format("kv_%s", key);
        String value = cacheService.getValue(cacheKey);
        if (StringUtil.isNotEmpty(value)) {
            return FastjsonUtil.toObject(value, CommonCodeRuleDto.class);
        }

        return getRuleDetail(key);
    }

    private String getCacheRuleKey() {
        return HrConstant.POST_RULE + UserContext.getTenantId();
    }

    private String getCacheSettingKey() {
        return HrConstant.POST_SETTING + UserContext.getTenantId();
    }

    public void setting(OrgSettingsDto data) {
        TenantRuleDto rule = ObjectConverter.convert(data, TenantRuleDto.class);
        Result result = tenantFeignClient.orgRuleSet(rule);
        if (null == result || !result.isSuccess()) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32038));
        }

        KvDto kvDto = new KvDto();
        kvDto.setContent(FastjsonUtil.toJson(data));
        kvDto.setProperty(getCacheSettingKey());
        result = tenantFeignClient.saveKv(kvDto);
        if (null == result || !result.isSuccess()) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32038));
        }
    }

    public OrgSettingsDto getSettings() {
        Result<String> result = tenantFeignClient.getKv(getCacheSettingKey());
        OrgSettingsDto ruleDto = null;
        if (null == result || !result.isSuccess() || null == (ruleDto = FastjsonUtil.toObject(result.getData(), OrgSettingsDto.class))) {
            return new OrgSettingsDto();
        }

        return ruleDto;
    }
}
