package com.caidaocloud.hr.service.employee.application.workOverview.service;

import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.domain.workExperience.entity.WorkOverviewDo;
import com.caidaocloud.hr.service.employee.domain.workExperience.service.WorkOverviewDomainService;
import com.caidaocloud.hr.service.dto.WorkOverviewDto;
import com.caidaocloud.hr.service.vo.WorkOverviewVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.googlecode.totallylazy.Sequences;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class WorkOverviewService {
    @Resource
    private WorkOverviewDomainService workOverviewDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;
    @Resource
    private MetadataService metadataService;


    public String saveWorkOverview(WorkOverviewDto updateData) {
        WorkOverviewDo data = ObjectConverter.convert(updateData, WorkOverviewDo.class);
        doConverter(updateData, data);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), updateData.getExt(), data);

        WorkOverviewDo workOverviewDo = workOverviewDomainService.selectByEmpId(data.getEmpId());
        if (StringUtils.isNotEmpty(workOverviewDo.getEmpId())) {
            workOverviewDomainService.update(data);
            return data.getBid();
        }

        return workOverviewDomainService.save(data);
    }

    private void doConverter(WorkOverviewDto source, WorkOverviewDo target) {
        if (StringUtil.isNotEmpty(source.getContinuousWorking())) {
            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(source.getContinuousWorking());
            target.setContinuousWorking(enumSimple);
        }
    }

    public WorkOverviewVo selectByEmpId(String empId) {
        WorkOverviewDo data = workOverviewDomainService.selectByEmpId(empId);
        WorkOverviewVo vo = ObjectConverter.convert(data, WorkOverviewVo.class);

        // 自定义字段查询
        Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(data.getDoIdentifier(), data);
        vo.setExt(ext);

        return vo;
    }

    public List<WorkOverviewVo> listByEmpIds(List<String> empIds){
        List<WorkOverviewDo> data = workOverviewDomainService.listByEmpIds(empIds);
        MetadataVo metadataVo = metadataService.getMetadata(WorkOverviewDo.WORK_OVERVIEW_IDENTIFIER);
        return Sequences.sequence(data).map(d -> {
            WorkOverviewVo vo = ObjectConverter.convert(d, WorkOverviewVo.class);

            // 自定义字段查询
            Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(metadataVo, d);
            vo.setExt(ext);
            return vo;
        }).toList();
    }
}
