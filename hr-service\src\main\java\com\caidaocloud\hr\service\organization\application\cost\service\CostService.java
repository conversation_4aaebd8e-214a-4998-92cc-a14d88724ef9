package com.caidaocloud.hr.service.organization.application.cost.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.organization.domain.cost.entity.CostCenterDo;
import com.caidaocloud.hr.service.organization.domain.cost.service.CostCenterDomainService;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.organization.interfaces.dto.cost.CostCenterQueryDto;
import com.caidaocloud.hr.service.search.application.event.EntityDataChange;
import com.caidaocloud.hrpaas.metadata.sdk.dto.LabelData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 成本中心
 *
 * <AUTHOR>
 * @Date 2021/11/23
 */
@Service
public class CostService {
    @Resource
    private CostCenterDomainService costCenterDomainService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    /**
     * 新增成本中心
     *
     * @param costCenterDo
     * @return
     */
    public String saveCostCenter(CostCenterDo costCenterDo) {
        return costCenterDomainService.save(costCenterDo);
    }

    /**
     * 修改成本中心
     *
     * @param costCenterDo
     */
    public void updateCostCenter(CostCenterDo costCenterDo) {
        costCenterDomainService.update(costCenterDo);
    }

    /**
     * 删除成本中心
     *
     * @param costCenterDo
     */
    public void deleteCostCenterById(CostCenterDo costCenterDo) {
        //关联组织
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(orgDomainService.selectByCostCenterCurrentTime(costCenterDo.getBid())), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32021));
        //关联员工
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(empWorkInfoDomainService.selectByLikeCostId(costCenterDo.getBid(), System.currentTimeMillis())), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32023));
        costCenterDomainService.delete(costCenterDo);
    }

    private void repeatCheck(CostCenterDo costCenterDo){
        // 成本中心重复校验 DEV-11592
        List<CostCenterDo> repeatList = costCenterDomainService
                .selectByCode(costCenterDo.getBid(), costCenterDo.getCostCenterCode(), costCenterDo.getDataStartTime());
        PreCheck.preCheckArgument(null != repeatList && repeatList.isEmpty(),
                "成本中心编码不能重复");
    }

    public void enable(CostCenterDo data, Boolean updateChildren) {
        costCenterDomainService.updateStatus(data, BusinessEventTypeEnum.ENABLE, updateChildren);
    }

    public void disable(CostCenterDo data) {
        //关联组织
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(orgDomainService.selectByCostCenterCurrentTime(data.getBid())), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32022));
        //关联员工
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(empWorkInfoDomainService.selectByLikeCostId(data.getBid(), System.currentTimeMillis())), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32024));

        costCenterDomainService.updateStatus(data, BusinessEventTypeEnum.DISABLE, false);
    }

    /**
     * 获取成本中心详情
     *
     * @param bid
     * @param dateTime
     * @return
     */
    public CostCenterDo getCostCenterById(String bid, Long dateTime) {
        return costCenterDomainService.selectById(bid, dateTime);
    }

    /**
     * 获取成本中心列表
     */
    public PageResult<CostCenterDo> getCostCenterPageResult(BasePageQueryDto pageQueryDto) {
        return costCenterDomainService.selectPage(pageQueryDto);
    }

    public List getTreeList(CostCenterQueryDto queryDto) {
        if (StringUtils.isNotEmpty(queryDto.getCostCenterNameOrCode()) || (queryDto.getStatus() != null && queryDto.getStatus() == 1)) {
            PageResult<CostCenterDo> pageResult = costCenterDomainService.selectFilterPage(queryDto);
            return pageResult.getItems();
        }
        return costCenterDomainService.getTreeList(queryDto);
    }

    public List<CostCenterDo> listByFilter(CostCenterQueryDto queryDto) {
        PageResult<CostCenterDo> pageResult = costCenterDomainService.selectFilterPage(queryDto);
        return pageResult.getItems();
    }

    public List<TreeData<LabelData>> fetchSimpleTree(Long dateTime, Integer status) {
        return costCenterDomainService.fetchSimpleTree(dateTime, status);
    }

    public void sync(List<EntityDataChange> data) {
        costCenterDomainService.sync(data);
    }
}
