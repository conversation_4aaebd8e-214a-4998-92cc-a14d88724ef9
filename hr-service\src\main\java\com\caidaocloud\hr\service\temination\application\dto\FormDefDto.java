package com.caidaocloud.hr.service.temination.application.dto;

import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class FormDefDto {
    private String id;
    private String name;
    private List<FormDefMetadataDto> properties;

    public List<FormDefMetadataDto> getAttachmentPropertyDef() {
        return this.getProperties().stream().filter(o -> o.getDataType() == PropertyDataType.Attachment).collect(Collectors.toList());
    }
}