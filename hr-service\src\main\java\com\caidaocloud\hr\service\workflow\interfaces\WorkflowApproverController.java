package com.caidaocloud.hr.service.workflow.interfaces;

import com.caidaocloud.hr.service.employee.application.common.tool.SnowUtil;
import com.caidaocloud.hr.service.employee.domain.base.enums.ConditionTree;
import com.caidaocloud.hr.service.workflow.application.dto.WorkflowApproverSettingDto;
import com.caidaocloud.hr.service.workflow.application.service.WorkflowApproverService;
import com.caidaocloud.hr.service.workflow.domain.entity.WorkflowApproverSetting;
import com.caidaocloud.hr.service.workflow.domain.entity.WorkflowApproverSettingDetail;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.paas.match.*;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/hr/v1/workflow/approver")
@Api(value = "/api/hr/v1/workflow/approver", description = "工作流审批人设置")
public class WorkflowApproverController {

    @Resource
    private WorkflowApproverService workflowApproverService;


    @ApiOperation(value = "校验设置下的审批人是否存在")
    @GetMapping("/check")
    @ResponseBody
    public Result<Boolean> checkApprover(@RequestParam("settingBids") String settingBids,
                                         @RequestParam(value = "empId", required = false) String empId,
                                         @RequestParam(value = "preEmpId", required = false) String preEmpId,
                                         @RequestParam("approverEmpId") String approverEmpId) {
        log.info("-----checkApprover-------settingBids={},empId={},preEmpId={},approverEmpId={}", settingBids, empId, preEmpId, approverEmpId);
        return Result.ok(workflowApproverService.checkApprover(settingBids, empId, preEmpId, approverEmpId));
    }

    @ApiOperation(value = "查看设置下审批人（工作流）")
    @GetMapping
    @ResponseBody
    public Result<String> getApprovers(@RequestParam("applicantId") String applicantId,
                                       @RequestParam("initiatorId") String initiatorId,
                                       @RequestParam("code") String code,
                                       @RequestParam("value") String value) {
        log.info("-----getApprovers-------applicantId={},initiatorId={},code={},value={}", applicantId, initiatorId, code, value);
        val settingBid = StringUtils.substringAfter(code, WorkflowApproverService.approverRegisterCodePrefix);
        val approvers = workflowApproverService.getWfApprover(settingBid, applicantId);
        if (approvers.isEmpty()) {
            return Result.ok("");
        }
        return Result.ok(FastjsonUtil.toJson(approvers));
    }

    @ApiOperation(value = "查看设置下审批人")
    @GetMapping("/batch")
    @ResponseBody
    public Result<String> getApprovers(@RequestParam("empId") String empId,
                                       @RequestParam("settingBids") String settingBids,
                                       @RequestParam(value = "isFormOnboarging", defaultValue = "false") boolean isFormOnboarging) {
        log.info("-----getApprovers-------empId={},settingBids={},isFormOnboarging={}", empId, settingBids, isFormOnboarging);
        val approvers = workflowApproverService.getWfApproverList(settingBids, empId, isFormOnboarging);
        if (approvers.isEmpty()) {
            return Result.ok("");
        }
        return Result.ok(FastjsonUtil.toJson(approvers));
    }

    @ApiOperation(value = "审批人查看设置列表")
    @PostMapping("/setting/page")
    @ResponseBody
    public Result<List<WorkflowApproverSetting>> listSettings(@RequestBody WorkflowApproverSettingDto dto) {
        List<WorkflowApproverSetting> workflowApproverSettings = workflowApproverService.listSettings(dto);
        return Result.ok(workflowApproverSettings);
    }

    @ApiOperation(value = "查看设置列表")
    @GetMapping("setting/list")
    @ResponseBody
    public Result<List<WorkflowApproverSetting>> listSettings() {
        return Result.ok(workflowApproverService.listSettings());
    }

    @ApiOperation(value = "审批人管理数据清洗任务")
    @GetMapping("setting/approveSettingHandler")
    @ResponseBody
    public Result<String> approveSettingHandler() {
        log.info("approveSettingHandler---------start execution,time {}", System.currentTimeMillis());
        List<WorkflowApproverSetting> workflowApproverSettingList = workflowApproverService.listSettings();
        for (WorkflowApproverSetting setting : workflowApproverSettingList) {
            //查询出详情
            WorkflowApproverSetting settingAndDetails = WorkflowApproverSetting.load(setting.getBid());

            List<WorkflowApproverSettingDetail> settingDetails = settingAndDetails.getDetails();
            for (WorkflowApproverSettingDetail detail : settingDetails) {
//                if (detail.getMatchingCondition() != null) {
//                    continue;
//                }
                ConditionTree matchingCondition = new ConditionTree();
                List<String> approveTargetList = detail.getApproveTarget();
                matchingCondition.setId(SnowUtil.nextId());
                matchingCondition.setRelation(ConditionNodeRelationEnum.and);

                List<ConditionNode> children = Lists.newArrayList();
                ConditionNode conditionNode = new ConditionNode();
                conditionNode.setId(SnowUtil.nextId());
                conditionNode.setType(ConditionNodeTypeEnum.single);

                ConditionExp conditionExp = new ConditionExp();
                conditionExp.setName("workplace");
                conditionExp.setSymbol(ConditionOperatorEnum.IN);
                conditionExp.setValue(approveTargetList);
                conditionExp.setSimpleValue(StringUtils.join(approveTargetList, ","));
                conditionExp.setComponentType(ConditionComponentEnum.WORKPLACE);
                conditionNode.setCondition(conditionExp);

                children.add(conditionNode);
                matchingCondition.setChildren(children);

                detail.setMatchingCondition(matchingCondition);

            }
            //更新matchingCondition字段
            settingAndDetails.update();

        }
        return Result.ok("success");
    }

    @ApiOperation(value = "查看设置详情")
    @GetMapping("setting")
    @ResponseBody
    public Result<WorkflowApproverSetting> loadSetting(@RequestParam String bid) {
        return Result.ok(workflowApproverService.loadSetting(bid));
    }

    @ApiOperation(value = "新增设置")
    @PostMapping("setting/create")
    @ResponseBody
    public Result<String> createSetting(@RequestBody WorkflowApproverSettingDto setting) {
        return Result.ok(workflowApproverService.createSetting(setting));
    }

    @ApiOperation(value = "更新设置")
    @PostMapping("setting/update")
    @ResponseBody
    public Result<Boolean> updateSetting(@RequestBody WorkflowApproverSettingDto setting) {
        workflowApproverService.updateSetting(setting);
        return Result.ok();
    }

    @ApiOperation(value = "查看设置下的异动前审批人")
    @GetMapping("/transferBefore")
    @ResponseBody
    public Result<String> getTransferBeforeApprovers(@RequestParam("applicantId") String applicantId,
                                                     @RequestParam("code") String code, @RequestParam("businessKey") String businessKey) {
        log.info("getTransferBeforeApprovers applicantId={}, code={}, businessKey={}", applicantId, code, businessKey);
        val approvers = workflowApproverService.getWfTransferBeforeApprovers(code, applicantId, businessKey);
        if (approvers.isEmpty()) {
            return Result.ok("");
        }

        return Result.ok(FastjsonUtil.toJson(approvers));
    }

    @ApiOperation(value = "查看设置下的异动后审批人")
    @GetMapping("/transfer")
    @ResponseBody
    public Result<String> getTransferApprovers(@RequestParam("applicantId") String applicantId,
                                               @RequestParam("code") String code, @RequestParam("businessKey") String businessKey) {
        log.info("-------applicantId={}, code={}, businessKey={}", applicantId, code, businessKey);
        val approvers = workflowApproverService.getWfTransferApprovers(code, applicantId, businessKey);
        if (approvers.isEmpty()) {
            return Result.ok("");
        }

        return Result.ok(FastjsonUtil.toJson(approvers));
    }

    @ApiOperation("工作流-汇报线审批人详情")
    @GetMapping("/leader")
    public Result<String> getWfApprover(@RequestParam("applicantId") String applicantId,
                                        @RequestParam("code") String code, @RequestParam("businessKey") String businessKey, @RequestParam("value") String value) {
        return Result.ok(workflowApproverService.getWfApprover(applicantId, businessKey, code, value));
    }

    @ApiOperation("工作流-异动后汇报线审批人详情")
    @GetMapping("/reportLine")
    public Result<String> getWfTransferApprovers(@RequestParam("applicantId") String applicantId,
                                                 @RequestParam("code") String code, @RequestParam("businessKey") String businessKey, @RequestParam("value") String value) {
        return Result.ok(workflowApproverService.getWfTransferApprovers(applicantId, businessKey, code, value));
    }

    @ApiOperation("流程通知变量-申请人（主岗）直接下级员工")
    @GetMapping("/Lower/Level")
    public Result<String> getEmpLowerLevel(@RequestParam("empId") String empId) {
        return Result.ok(workflowApproverService.getEmpLowerLevel(empId));
    }

    @ApiOperation("流程通知变量-申请人（兼岗）直接下级员工")
    @GetMapping("/concurrent/Lower/Level")
    public Result<String> getEmpConcurrentLowerLevel(@RequestParam("empId") String empId) {
        return Result.ok(workflowApproverService.getEmpConcurrentLowerLevel(empId));
    }

    @ApiOperation("流程通知变量-申请人负责组织")
    @GetMapping("/org/own")
    public Result<String> getOrgOwn(@RequestParam("empId") String empId) {
        return Result.ok(workflowApproverService.getOrgOwn(empId));
    }

    @GetMapping("/emp/approver")
    public Result<String> getApproverEmpList(@RequestParam("empId") String empId, @RequestParam("settingBid")String settingBid) {
        List<EmpSimple> approverEmpList = workflowApproverService.getApproverEmpList(empId, settingBid);
        if(CollectionUtils.isEmpty(approverEmpList)) {
            return Result.ok("");
        }
        List<String> nameList = approverEmpList.stream().map(e -> String.format("%s(%s)", e.getName(), e.getWorkno())).collect(Collectors.toList());
        return Result.ok(Joiner.on(",").join(nameList));
    }
}
