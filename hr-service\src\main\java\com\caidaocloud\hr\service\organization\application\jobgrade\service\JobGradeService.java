package com.caidaocloud.hr.service.organization.application.jobgrade.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.organization.application.job.service.JobService;
import com.caidaocloud.hr.service.organization.application.post.service.BenchmarkPositionService;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobTypeDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeChannelDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.service.JobGradeChannelDomainService;
import com.caidaocloud.hr.service.organization.domain.jobgrade.service.JobGradeDomainService;
import com.caidaocloud.hr.service.organization.interfaces.vo.jobgrade.JobGradeNodeVo;
import com.caidaocloud.hr.service.organization.interfaces.vo.jobgrade.JobGradeTreeVo;
import com.caidaocloud.hr.service.organization.interfaces.vo.jobgrade.JobGradeVo;
import com.caidaocloud.hr.service.search.application.event.EntityDataChange;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/1
 */
@Slf4j
@Service
public class JobGradeService {
    @Resource
    private JobGradeDomainService jobGradeDomainService;
    @Resource
    private JobGradeChannelDomainService jobGradeChannelDomainService;
    @Resource
    private JobService jobService;
    @Resource
    private BenchmarkPositionService benchmarkPositionService;

    /**
     * 职级-保存
     *
     * @param data
     * @return
     */
    public String saveJobGrade(JobGradeDo data) {
        return jobGradeDomainService.saveJobGrade(data);
    }

    /**
     * 职级-修改
     *
     * @param data
     */
    public void updateJobGrade(JobGradeDo data) {
        jobGradeDomainService.updateJobGrade(data);
    }

    /**
     * 职级-删除
     *
     * @param data
     */
    public void deleteJobGrade(JobGradeDo data) {

        // 校验是否有基准岗位依赖此职级 不允许删除
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(benchmarkPositionService.selectListByStartGrade(data.getBid())), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32011));

        // 校验是否有职务依赖此职级    不允许删除
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(jobService.selectListByStartLevel(data.getBid())), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32009));

        jobGradeDomainService.deleteJobGrade(data);
    }

    public void enable(JobGradeDo data) {
        jobGradeDomainService.updateStatus(data, BusinessEventTypeEnum.ENABLE);
    }

    public void disable(JobGradeDo data) {
        // 校验是否有基准岗位依赖此职级 不允许停用
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(benchmarkPositionService.selectListByStartGrade(data.getBid())), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32010));

        // 校验是否有职务依赖此职级    不允许停用
        PreCheck.preCheckArgument(CollectionUtils.isNotEmpty(jobService.selectListByStartLevel(data.getBid())), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32008));

        jobGradeDomainService.updateStatus(data, BusinessEventTypeEnum.DISABLE);
    }

    /**
     * 职级-查看明细
     *
     * @param bid
     * @return
     */
    public JobGradeDo selectJobGradeById(String bid) {
        return jobGradeDomainService.selectJobGradeById(bid);
    }

    /**
     * 职级-根据职级通道ID查询职级列表
     *
     * @return
     */
    public List<JobGradeDo> selectJobGradeList(String channelId, Integer status) {
        return jobGradeDomainService.selectJobGradeList(channelId, status);
    }

    public List<JobGradeDo> selectJobGradeListByIds(List<String> bids) {
        return jobGradeDomainService.selectJobGradeListByIds(bids);
    }

    public void dragSort(List<JobGradeDo> dataList) {
        jobGradeDomainService.dragSort(dataList);
    }



    public List<JobGradeTreeVo> getTreeList(){
        List<JobGradeTreeVo> list = new ArrayList<>();
        List<JobGradeChannelDo> jobTypeList = jobGradeChannelDomainService.selectList(0);
        if(null == jobTypeList || jobTypeList.isEmpty()){
            return list;
        }

        Map<String, JobGradeTreeVo> collect = jobTypeList.stream().map(jobTypeDo -> {
            JobGradeTreeVo treeVo = new JobGradeTreeVo();
            treeVo.setText(LangUtil.parseI18nValue(jobTypeDo.getChannelName() , jobTypeDo.getI18nChannelName()));
            treeVo.setValue(jobTypeDo.getBid());
            return treeVo;
        }).collect(Collectors.toMap(JobGradeTreeVo::getValue, tree -> tree, (k1, k2) -> k2));

        List<JobGradeDo> jobList = getAllJobGrade();
        if(null == jobList || jobList.isEmpty()){
            list.addAll(collect.values());
            return list;
        }

        Map<String, List<JobGradeDo>> jobMap = jobList.stream().collect(Collectors.groupingBy(JobGradeDo::getChannelId));
        collect.values().forEach(tree -> {
            List<JobGradeDo> jobDos = jobMap.get(tree.getValue());
            list.add(tree);
            if(null == jobDos || jobDos.isEmpty()){
                return;
            }

            List<JobGradeNodeVo> nodeList = new ArrayList<>();
            jobDos.forEach(node -> {
                JobGradeNodeVo nodeVo = new JobGradeNodeVo();
                nodeVo.setValue(node.getBid());
                nodeVo.setText(LangUtil.parseI18nValue(node.getJobGradeName(), node.getI18nJobGradeName()));
                JobGradeVo jobGrade = ObjectConverter.convert(node, JobGradeVo.class);
                if(null != jobGrade){
                    jobGrade.setChannelName(tree.getText());
                }
                nodeVo.setJobGrade(jobGrade);
                nodeList.add(nodeVo);
            });
            tree.setChildren(nodeList);
        });
        return list;
    }

    public List<JobGradeDo> getAllJobGrade() {
        return jobGradeDomainService.selectAll(StatusEnum.ENABLED.getIndex());
    }

    public void sync(List<EntityDataChange> data) {
        jobGradeDomainService.sync(data);
    }

}
