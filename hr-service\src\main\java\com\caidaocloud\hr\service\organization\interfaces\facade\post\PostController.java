package com.caidaocloud.hr.service.organization.interfaces.facade.post;

import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.common.infrastructure.utils.ObjectConvertUtil;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.infrastructure.utils.ExcelUtils;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.SelectDataDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.StatusOptDto;
import com.caidaocloud.hr.service.organization.application.job.service.JobService;
import com.caidaocloud.hr.service.organization.application.post.service.BenchmarkPositionService;
import com.caidaocloud.hr.service.organization.application.post.service.PostService;
import com.caidaocloud.hr.service.organization.domain.job.entity.JobDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.BenchmarkPositionDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgOrPostQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.PostDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.PostQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.SelectPostDataDto;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.hr.service.vo.organization.post.PostVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/3
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/postmanage/v1")
@Api(value = "/api/hr/postmanage/v1", description = "岗位管理", tags = "v0.1")
public class PostController {
    @Resource
    private PostService postService;

    @Resource
    private BenchmarkPositionService benchmarkPositionService;

    @Resource
    private JobService jobService;

    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    @ApiOperation("删除岗位")
    @DeleteMapping("/delete")
    @LogRecordAnnotation(success = "删除了岗位设置{{#name}}", category = "删除", menu = "组织-岗位管理-岗位设置", condition = "{{#condition}}")
    public Result delete(@RequestParam("bid") String bid, @RequestParam("dataTime") Long dataTime) {
        postService.deleteOrgById(bid, dataTime);
        LogRecordContext.putVariable("condition", true);
        return Result.ok(true);
    }

    @ApiOperation("查看岗位设置详情")
    @GetMapping("/detail")
    public Result<PostVo> getDetail(@RequestParam("bid") String bid,
                                    @RequestParam(value = "dataTime", required = false) Long dataTime) {
        PostDo data = postService.getById(bid, dataTime);
        PostVo postVo = ObjectConvertUtil.convert(data, PostVo.class, PostDo::i18Convert);
        if (null == postVo.getRelation()) {
            return Result.ok(postVo);
        }

        if (postVo.getRelation() == 1) {
            JobDo jobDo = jobService.getById(postVo.getJobId());
            if (jobDo != null) {
                postVo.setLinkJobDescFiles(jobDo.getFiles());
            }
        } else {
            BenchmarkPositionDo benchmarkPositionDo = benchmarkPositionService.getById(postVo.getBenchmarkPositionId());
            if (benchmarkPositionDo != null) {
                postVo.setLinkBenchmarkDescFiles(benchmarkPositionDo.getJobDescFiles());
            }
        }

        return Result.ok(postVo);
    }

    @ApiOperation("新增岗位")
    @PostMapping("/save")
    @LogRecordAnnotation(success = "新增了岗位设置{{#name}}", category = "新增", menu = "组织-岗位管理-岗位设置", condition = "{{#condition}}")
    public Result save(@RequestBody PostDto dto) {
        dto.peekPostDto().check();
        LogRecordContext.putVariable("name", dto.getName());
        PostDo postDo = ObjectConverter.convert(dto.peekPostDto(), PostDo.class);
        postDo.setI18nName(LangUtil.getI18nValue(postDo.getName(), dto.getI18nName()));
        String bid = postService.save(postDo);
        LogRecordContext.putVariable("condition", true);
        return Result.ok(bid);
    }

    @ApiOperation("编辑岗位")
    @PostMapping("/update")
    @LogRecordAnnotation(success = "编辑了岗位设置{{#name}}", category = "编辑", menu = "组织-岗位管理-岗位设置", condition = "{{#condition}}")
    public Result update(@RequestBody PostDto dto) {
        dto.peekPostDto().check();
        LogRecordContext.putVariable("name", dto.getName());
        PostDo postDo = ObjectConverter.convert(dto, PostDo.class);
        postDo.setI18nName(LangUtil.getI18nValue(postDo.getName(), dto.getI18nName()));
        postService.update(postDo);
        LogRecordContext.putVariable("condition", true);
        return Result.ok(true);
    }

    @ApiOperation("查询岗位分页列表")
    @PostMapping("/list")
    public Result<PageResult<PostVo>> getList(@RequestBody PostQueryDto queryDto) {
        PageResult<PostDo> pageResult = postService.getPageResult(queryDto);
        List<PostVo> items = ObjectConvertUtil.convertList(pageResult.getItems(), PostVo.class, PostDo::i18Convert);
        return Result.ok(new PageResult(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation("分页查询组织下的岗位接口")
    @PostMapping("/selectPostPageListByOrgId")
    public Result<PageResult<PostVo>> selectPostPageListByOrgId(@RequestBody PostQueryDto queryDto) {
        PageResult<PostDo> pageResult = postService.getPostPageListByOrgId(queryDto);
        List<PostVo> items = ObjectConvertUtil.convertList(pageResult.getItems(), PostVo.class,
                (it, v1) -> {
                    v1.setI18nName(FastjsonUtil.toObject(it.getI18nName(), Map.class));
                });
        return Result.ok(new PageResult(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation("启用或停用岗位")
    @PostMapping("/updateStatus")
    @LogRecordAnnotation(success = "{{#operate}}了岗位设置{{#name}}", category = "{{#operate}}", menu = "组织-岗位管理-岗位设置")
    public Result updateStatus(@RequestBody StatusOptDto dto) {
        dto.preCheckTimelineArgument();
        PostDo data = ObjectConverter.convert(dto, PostDo.class);
        PostDo byId = postService.getById(data.getBid(), data.getDataStartTime());
        LogRecordContext.putVariable("name", byId.getName());
        if (StatusEnum.ENABLED.getIndex().equals(dto.getStatus())) {
            LogRecordContext.putVariable("operate", "启用");
            postService.enable(data);
        } else {
            LogRecordContext.putVariable("operate", "停用");
            return postService.disable(data, dto.getDataStartTime());
        }
        return Result.ok(true);
    }

    @ApiOperation("查询岗位下拉列表")
    @GetMapping("/selectList")
    public Result getSelectList(@RequestParam(value = "orgId", required = false) String orgId,
        @RequestParam(value = "dataStartTime", required = false) Long dataStartTime,
        @RequestParam(value = "keyword", required = false) String keyword) {
        List<PostDo> list = postService.selectList(orgId, StatusEnum.ENABLED.getIndex(), dataStartTime, keyword);
        if (null == list || list.isEmpty()) {
            return Result.ok(new ArrayList<>());
        }

        List<SelectDataDto> keyValues = list.stream()
                .map(o -> new SelectPostDataDto().setJob(o.getJobId()).setJobTxt(o.getJobName())
                        .setText(LangUtil.parseI18nValue(o.getName(), o.getI18nName()))
                        .setValue(o.getBid())
                        .setCode(o.getCode())
                        .setJobGrade(o.getJobGrade()))
                .collect(Collectors.toList());
        keyValues.forEach(it->{
            if(!"enabled".equals(postTxtShowCode)){
                String postTxt = it.getText();
                if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
                    postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                    it.setText(postTxt);
                }
            }
        });
        return Result.ok(keyValues);
    }

    @ApiOperation("根据部门id列表获取岗位列表")
    @PostMapping("/selectPostListByOrgIdList")
    public Result selectPostListByOrgIdList(@RequestBody OrgOrPostQueryDto orgQueryDto) {
        return Result.ok(postService.selectPostListByOrgIdList(orgQueryDto));
    }

    @ApiOperation("导入同步创建岗位")
    @PostMapping("/importSyncSave")
    public Result importSyncSave(@RequestBody List<PostDto> dataList) {
        return Result.ok(postService.importSyncSave(dataList));
    }

    @ApiOperation("查询岗位下拉列表")
    @GetMapping("/selectList/kv")
    public Result<List<KeyValue>> getSelectKVList() {
        List<PostDo> list = postService.selectList(null, StatusEnum.ENABLED.getIndex(), null, null);
        if (null == list || list.isEmpty()) {
            return Result.ok(new ArrayList<>());
        }
        var keyValue = new KeyValue();
        List<KeyValue> keyValues = list.stream()
                .map(o -> new KeyValue(LangUtil.parseI18nValue(o.getName(), o.getI18nName()), o.getBid()))
                .collect(Collectors.toList());
        return Result.ok(keyValues);
    }

    @ApiOperation("导出岗位列表")
    @PostMapping("/exportPostList")
    public void exportNewlySignedList(@RequestBody PostQueryDto dto, HttpServletResponse response) {
        PageResult<PostVo> page = postService.getEmpPageListFromExport(dto);
        List<ExcelExportEntity> colList = new ArrayList<>();
        for (TagProperty tagProperty : postService.installNewlySignedExportProperty()) {
            ExcelExportEntity exprortEntity = new ExcelExportEntity(tagProperty.getPropertyTxt(), tagProperty.getProperty(), 15);
            exprortEntity.setOrderNum(tagProperty.getOrder());
            colList.add(exprortEntity);
        }
        List<Map<String, Object>> dataList = org.apache.commons.collections.CollectionUtils.isEmpty(page.getItems()) ? Lists.newArrayList() : installNewlySignedDataList(page.getItems(), colList);
        try {
            ExcelUtils.downloadDataMapExcel(colList, dataList, "岗位信息", response);
        } catch (Exception e) {
            log.error("download NewlySignedList excel error.{}", e.getMessage(), e);
        }
    }

    private List<Map<String, Object>> installNewlySignedDataList(List<PostVo> items, List<ExcelExportEntity> colList) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (PostVo vo : items) {
            Map<String, Object> map = new HashMap<>();
            JSONObject json = JSONObject.parseObject(JSONObject.toJSONString(vo));
            for (ExcelExportEntity entity : colList) {
                map.put(entity.getKey().toString(), json.get(entity.getKey()));
            }
            dataList.add(map);
        }
        return dataList;
    }

}
