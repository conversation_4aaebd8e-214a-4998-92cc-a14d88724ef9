package com.caidaocloud.hr.service.search.interfaces.facade;

import com.caidaocloud.hr.service.search.application.service.EmpEsSearchService;
import com.caidaocloud.hr.service.search.interfaces.dto.EmpUpdateDto;
import com.caidaocloud.hr.service.transfer.application.dto.TransferApplyDto;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/hr/search/v1")
@Api(value = "/api/hr/search/v1", description = "es查询工具", tags = "v3.13")
public class EsSearchController {
    @Resource
    private EmpEsSearchService empEsSearchService;

    @ApiOperation("根据工号查询ES员工详情")
    @GetMapping("/es/emp/workno")
    public Result getEsEmpByWorkno(@RequestParam("workno") String workno) {
        return Result.ok(empEsSearchService.searchEsEmpByWorkno(workno));
    }

    @ApiOperation("删除ES中的指定记录")
    @DeleteMapping("/es/emp/id")
    public Result deleteEsEmpById(@RequestParam("id") String id) {
        empEsSearchService.deleteEsEmpById(id);
        return Result.ok();
    }

    @ApiOperation("修改ES中的指定记录")
    @PostMapping("/es/emp/change")
    public Result updateEsEmpById(@RequestBody EmpUpdateDto empDto) {
        if(StringUtil.isEmpty(empDto.getId())
                || StringUtil.isEmpty(empDto.getProp())
                || null == empDto.getValue()){
            return Result.fail();
        }

        empEsSearchService.updateEsEmpById(empDto);
        return Result.ok();
    }

    @ApiOperation("同步指定员工的兼岗数据到es中")
    @PostMapping("/es/emp/post")
    public Result updateEmpConcurrentSyncEs(@RequestBody List<String> empIds){
        empEsSearchService.updateEmpConcurrentSyncEs(empIds);
        return Result.ok();
    }

    @ApiOperation("按指定条件更新异动数据")
    @PostMapping("/es/emp/transfer")
    public Result updateEmpTransfer(@RequestBody TransferApplyDto applyDto){
        empEsSearchService.updateEmpTransfer(applyDto);
        return Result.ok();
    }

    @ApiOperation("删除指定索引")
    @DeleteMapping("/es/idx")
    public Result updateEsIdx(@RequestBody TransferApplyDto applyDto){
        empEsSearchService.rmEsIdx(applyDto);
        return Result.ok();
    }
}
