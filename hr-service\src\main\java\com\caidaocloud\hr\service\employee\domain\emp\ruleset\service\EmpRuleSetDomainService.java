package com.caidaocloud.hr.service.employee.domain.emp.ruleset.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.hr.service.dto.ruleset.EmpRuleSetDto;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.enums.ruleset.EmpListOrderEnum;
import com.caidaocloud.hr.service.organization.application.tenant.feign.ITenantFeignClient;
import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/2/17
 */
@Slf4j
@Service
public class EmpRuleSetDomainService {
    @Resource
    private CacheService cacheService;
    @Resource
    private ITenantFeignClient tenantFeignClient;

    public void save(EmpRuleSetDto data) {
        if (data.getEmpListOrderEnum() == null) {
            data.setEmpListOrderEnum(EmpListOrderEnum.UPDATE_TIME_DESC);
        }
        KvDto kvDto = new KvDto();
        kvDto.setProperty(getCacheRuleKey());
        kvDto.setContent(FastjsonUtil.toJson(data));
        Result<KvDto> result = tenantFeignClient.saveKv(kvDto);
        if(null != result && result.isSuccess()){
            String autoKey = String.format(HrConstant.AUTO_GENERATE_JOB_NUMBER, UserContext.getTenantId());
            cacheService.remove(autoKey);
            if(null == data.getWorknoAutoCreate()
                    || !data.getWorknoAutoCreate()
                    || null == data.getWorknoStartValue()){
                return;
            }
            cacheService.increment(autoKey, data.getWorknoStartValue());
        }
    }

    public EmpRuleSetDto getRuleSet() {
        return getRuleDetail(getCacheRuleKey());
    }

    public EmpRuleSetDto getRuleDetail(String key) {
        Result<String> result =  tenantFeignClient.getKv(key);
        EmpRuleSetDto ruleDto = null;
        if(null == result || !result.isSuccess() || null == (ruleDto = FastjsonUtil.toObject(result.getData(), EmpRuleSetDto.class))){
            return new EmpRuleSetDto();
        }

        return ruleDto;
    }

    private String getCacheRuleKey(){
        return HrConstant.WORKNO_RULE + UserContext.getTenantId();
    }

    public EmpRuleSetDto loadRule(){
        String key = getCacheRuleKey();
        String cacheKey = String.format("kv_%s", key);
        String value = cacheService.getValue(cacheKey);
        if(StringUtil.isNotEmpty(value)){
            return FastjsonUtil.toObject(value, EmpRuleSetDto.class);
        }

        return getRuleDetail(key);
    }
}
