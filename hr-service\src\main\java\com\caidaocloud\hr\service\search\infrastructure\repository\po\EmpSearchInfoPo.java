package com.caidaocloud.hr.service.search.infrastructure.repository.po;

import com.caidaocloud.hr.service.employee.application.emp.dto.EmpPageDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import org.zxp.esclientrhl.annotation.ESID;
import org.zxp.esclientrhl.annotation.ESMapping;
import org.zxp.esclientrhl.annotation.ESMetaData;
import org.zxp.esclientrhl.enums.DataType;

import java.util.List;
import java.util.Map;

@Data
@ESMetaData(indexName = "index_emp_info", number_of_shards = 3, number_of_replicas = 0, printLog = true,suffix = true)
public class EmpSearchInfoPo extends DataSimple {
    @ESID
    @ESMapping(datatype =DataType.keyword_type)
    private String id;

    @ESMapping(datatype = DataType.keyword_type)
    private String bid;

    /**
     * 员工ID
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String empId;

    /**
     * 员工工号
     */
    @ESMapping(datatype = DataType.keyword_type)
    private String workno;

    /**
     * 员工姓名（个人信息冗余字段）
     */
    @ESMapping(datatype = DataType.text_type)
    private String name;

    /**
     * 员工英文名（个人信息冗余字段）
     */
    @ESMapping(datatype = DataType.text_type)
    private String enName;

    /**
     * 员工状态
     */
    private String empStatusValue;

    /**
     * 员工状态
     */
    private String empStatusText;

    /**
     * 用工类型\员工类型
     */
    private String empTypeText;

    /**
     * 用工类型\员工类型
     */
    private String empTypeValue;

    /**
     * 合同公司ID
     */
    private String company;

    /**
     * 合同公司名称
     */
    @ESMapping
    private String companyTxt;

    /**
     * 所属组织Id
     */
    private String organize;

    /**
     * 所属组织名称
     */
    private String organizeTxt;

    /**
     * 所属组织全路径
     */
    private String organizePath;

    /**
     * 关联的职务ID
     */
    private String job;

    /**
     * 关联的职务名称
     */
    private String jobTxt;

    /**
     * 岗位ID
     */
    private String post;

    /**
     * 岗位名称
     */
    private String postTxt;

    /**
     * 入职日期
     */
    private Long hireDate;

    /**
     * 员工头像
     */
    @ESMapping(datatype = DataType.nested_type, nested_class = Attachment.class)
    private Attachment photo;

    /**
     * 工作地ID
     */
    private String workplace;

    /**
     * 工作地名称
     */
    private String workplaceTxt;

    /**
     * 员工性别（个人信息模型字段）
     */
    private String sex;

    /**
     * 员工性别（个人信息模型字段）
     */
    private String sexText;

    /**
     * 转正状态
     */
    private String confirmationStatus;

    /**
     * 转正状态
     */
    private String confirmationStatusText;

    /**
     * 数据生效时间
     */
    private long dataStartTime;

    /**
     * 数据失效时间
     */
    private long dataEndTime;

    /**
     * 直接上级
     */
    @ESMapping(datatype = DataType.nested_type, nested_class = EmpSimple.class)
    private EmpSimple leadEmpId;

    /**
     * 职级职等
     */
    @ESMapping(datatype = DataType.nested_type, nested_class = JobGradeRange.class)
    private JobGradeRange jobGrade;

    /**
     * 转正日期
     */
    private Long confirmationDate;

    /**
     * 离职日期
     */
    private Long leaveDate;

    /**
     * 工时制
     */
    private String workHour;

    /**
     * 工时制
     */
    private String workHourText;

    /**
     * 员工公司邮箱
     */
    private String companyEmail;

    /**
     * 入司途径
     */
    private String joinCompanyWay;

    /**
     * 入司途径
     */
    private String joinCompanyWayText;

    /**
     * 姓名拼音
     */
    private String namePinyin;

    /**
     * 岗位英文
     */
    private String enPostTxt;

    /**
     * 通讯地址邮编
     */
    private String mailAddressZipCode;

    /**
     * 工时制英文
     */
    private String enWorkHour;

    /**
     * 学号
     */
    private String studentNumber;

    @ESMapping(datatype = DataType.nested_type, nested_class = EmpTagPo.class)
    private List<EmpTagPo> empTags;

    /**
     * 成本中心
     */
    @ESMapping(datatype = DataType.nested_type, nested_class = CostCenterPo.class)
    private List<CostCenterPo> costCenters;

    @ESMapping(datatype = DataType.nested_type, nested_class = EmpConcurrentPostSearchPo.class)
    private List<EmpConcurrentPostSearchPo> concurrentPost;
    @ESMapping(datatype = DataType.nested_type, nested_class = DictSimple.class)
    private DictSimple contractType;
    /**
     * 离职状态
     */
    private String resignationStatus;

    @ESMapping(datatype = DataType.nested_type, nested_class = Map.class)
    private Map<String, Object> dynamicColumn;

    public EmpPageDto convertEnum() {
        EmpPageDto dto = ObjectConverter.convert(this, EmpPageDto.class);
        DictSimple sex = new DictSimple();
        sex.setValue(this.sex);
        sex.setText(this.sexText);
        dto.setSex(sex);
        DictSimple empType = new DictSimple();
        empType.setValue(this.empTypeValue);
        empType.setText(this.empTypeText);
        dto.setEmpType(empType);
        EnumSimple empStatus = new EnumSimple();
        empStatus.setValue(this.empStatusValue);
        empStatus.setText(this.empStatusText);
        EnumSimple confirmationStatus = new EnumSimple();
        confirmationStatus.setValue(this.confirmationStatus);
        confirmationStatus.setText(this.confirmationStatusText);
        dto.setEmpStatus(empStatus);
        dto.setConfirmationStatus(confirmationStatus);
        return dto;
    }

    /**
     * 基准岗位txt
     */
    private String benchPostTxt;
    /**
     * 基准岗位
     */
    private String benchPost;
}
