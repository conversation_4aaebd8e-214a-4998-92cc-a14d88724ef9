package com.caidaocloud.hr.service.employee.interfaces.vo.emp.ruleset;

import com.caidaocloud.hr.service.contract.application.enums.ContractSignSceneEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RuleSetVo {
    @ApiModelProperty("工号自动生成")
    private Boolean autoCreate;
    @ApiModelProperty("工号前缀")
    private String prefix;
    @ApiModelProperty("工号起始编码")
    private Long startValue;
    @ApiModelProperty("编码长度")
    private Integer length;
    @ApiModelProperty("是否启用工作流")
    private Boolean openWorkFlow;
    @ApiModelProperty("使用场景")
    private List<ContractSignSceneEnum> sceneEnumList;
    @ApiModelProperty("是否自动发起合同续签审批流程")
    private Boolean autoStart;
}
