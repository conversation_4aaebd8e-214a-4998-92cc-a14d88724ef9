package com.caidaocloud.hr.service.employee.interfaces.facade.emp.ruleset;

import com.caidaocloud.hr.service.contract.application.enums.ContractSignSceneEnum;
import com.caidaocloud.hr.service.employee.application.emp.ruleset.service.RuleSetService;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.entity.RuleSetDo;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.ruleset.RuleSetDto;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.ruleset.RuleSetVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/ruleset/v1")
@Api(value = "/api/hr/ruleset/v1", description = "规则设置", tags = "v2.0")
public class RuleSetController {
    @Resource
    private RuleSetService ruleSetService;

    @ApiOperation("保存规则设置")
    @PostMapping("/save")
    public Result save(@RequestBody RuleSetDto dto) {
        RuleSetDo ruleSetDo = ObjectConverter.convert(dto, RuleSetDo.class);
        ruleSetDo.setScene(FastjsonUtil.toJsonStr(dto.getSceneEnumList()));
        String bid = ruleSetService.save(ruleSetDo);
        return Result.ok(bid);
    }


    @ApiOperation("查询规则设置")
    @GetMapping("/getRuleSet")
    public Result<RuleSetVo> getRuleSet() {
        RuleSetDo ruleSetDo = ruleSetService.getRuleSet();
        RuleSetVo vo = ObjectConverter.convert(ruleSetDo, RuleSetVo.class);
        vo.setSceneEnumList(FastjsonUtil.toArrayList(ruleSetDo.getScene(), ContractSignSceneEnum.class));
        return ResponseWrap.wrapResult(vo);
    }

    @ApiOperation("是否开启工作流")
    @GetMapping("/getWorkFlowSwitch")
    public Result<Boolean> getWorkFlowSwitch(@RequestParam("code") ContractSignSceneEnum sceneEnum) {
        return Result.ok(ruleSetService.getWorkFlowSwitch(sceneEnum.name()));
    }
}
