package com.caidaocloud.hr.service.organization.infrastructure.repository.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.domain.cost.entity.CostCenterDo;
import com.caidaocloud.hr.service.organization.domain.cost.repository.ICostCenterRepository;
import com.caidaocloud.hr.service.organization.infrastructure.repository.po.CostCenterPo;
import com.caidaocloud.hr.service.organization.interfaces.dto.cost.CostCenterQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.LabelData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 成本中心
 *
 * <AUTHOR>
 * @Date 2021/11/26
 */
@Repository
public class CostCenterRepositoryImpl implements ICostCenterRepository {
    @Override
    public CostCenterDo insert(CostCenterDo data) {
        String bid = DataInsert.identifier(data.getIdentifier()).insert(ObjectConverter.convert(data, CostCenterPo.class));
        data.setBid(bid);
        return data;
    }

    @Override
    public int updateById(CostCenterDo data) {
        DataUpdate.identifier(data.getIdentifier()).update(ObjectConverter.convert(data, CostCenterPo.class));
        return 0;
    }

    @Override
    public CostCenterDo selectById(String id, String identifier) {
        return ObjectConverter.convert(DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().oneOrNull(id, CostCenterPo.class), CostCenterDo.class);
    }

    @Override
    public CostCenterDo selectById(String id, String identifier, Long dateTime) {
        return ObjectConverter.convert(DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().oneOrNull(id, CostCenterPo.class, dateTime), CostCenterDo.class);
    }

    @Override
    public int delete(CostCenterDo data) {
        DataDelete.identifier(data.getIdentifier()).softDelete(data.getBid(), data.getDataStartTime());
        return 0;
    }

    @Override
    public PageResult<CostCenterDo> selectPage(BasePage page, CostCenterDo data) {
        DataFilter dataFilter = DataFilter.eq("tenantId", data.getTenantId()).
                andNe("deleted", Boolean.TRUE.toString());
        if (data.getStatus() != null && data.getStatus().getValue() != null) {
            dataFilter = dataFilter.andEq("status", data.getStatus().getValue());
        }
        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(page.getPageSize(), page.getPageNo()).filter(dataFilter, CostCenterDo.class, data.getDataStartTime());
    }

    public PageResult<CostCenterDo> selectFilterPage(String identifier, CostCenterQueryDto queryDto) {
        Long dateTime = DataEntity.getTimeIfNullSetDefault(queryDto.getDateTime());

        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId()).
                andNe("deleted", Boolean.TRUE.toString());

        if (queryDto.getStatus() != null) {
            dataFilter = dataFilter.andEq("status", queryDto.getStatus().toString());
        }

        //成本中心名称/编码  模糊
        if (StringUtil.isNotEmpty(queryDto.getCostCenterNameOrCode())) {
            dataFilter = dataFilter.and(DataFilter.regex("costCenterName", queryDto.getCostCenterNameOrCode()).or(DataFilter.regex("costCenterCode", queryDto.getCostCenterNameOrCode())));
        }

        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(500, 1).filter(dataFilter, CostCenterDo.class, dateTime);
    }

    @Override
    public List<TreeData<CostCenterDo>> getTreeList(String identifier, CostCenterQueryDto queryDto) {
        Long dateTime = DataEntity.getTimeIfNullSetDefault(queryDto.getDateTime());
        Integer status = queryDto.getStatus();

        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId()).
                andNe("deleted", Boolean.TRUE.toString());

        if (status != null) {
            dataFilter = dataFilter.andEq("status", status.toString());
        }

        //成本中心名称/编码  模糊
        if (StringUtil.isNotEmpty(queryDto.getCostCenterNameOrCode())) {
            dataFilter = dataFilter.and(DataFilter.regex("costCenterName",queryDto.getCostCenterNameOrCode()).or(DataFilter.regex("costCenterCode", queryDto.getCostCenterNameOrCode())));
        }

        return DataQuery.identifier(identifier).tree(CostCenterDo.class, dateTime, dataFilter);
    }

    @Override
    public List<TreeData<LabelData>> getTreeSimpleList(String identifier, Long dateTime, Integer status) {
        if (status == null) {
            return DataQuery.identifier(identifier).tree(LabelData.class, dateTime);
        }
        DataFilter dataFilter = DataFilter.eq("status", status.toString());
        return DataQuery.identifier(identifier).tree(LabelData.class, dateTime, dataFilter);

    }

    @Override
    public List<CostCenterDo> selectChildrenList(Long dateTime, String bid, String identifier, String tenantId, Integer status) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId).andEq("pid$pid", bid).andNe("deleted", Boolean.TRUE.toString());
        if (status != null) {
            dataFilter = dataFilter.andEq("status", status.toString());
        }
        return ObjectConverter.convertList(DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().
                filter(dataFilter, CostCenterPo.class, dateTime).getItems(), CostCenterDo.class);
    }

    @Override
    public List<CostCenterDo> selectAllChildrenList(String identifier, String bid, Long dateTime) {
        return DataQuery.identifier(identifier).descendants(bid, CostCenterDo.class, dateTime);
    }

    @Override
    public List<CostCenterDo> selectByCode(String identifier, String bid, String code, Long dataTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andEq("costCenterCode", code).andNe("deleted", Boolean.TRUE.toString());
        if (StringUtil.isNotEmpty(bid)) {
            dataFilter = dataFilter.andNe("bid", bid);
        }
        return ObjectConverter.convertList(DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().
                filter(dataFilter, CostCenterPo.class, dataTime).getItems(), CostCenterDo.class);
    }
}
