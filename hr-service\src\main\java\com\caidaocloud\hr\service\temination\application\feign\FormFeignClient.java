package com.caidaocloud.hr.service.temination.application.feign;

import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.hr.service.temination.application.dto.FormDataDto;
import com.caidaocloud.hr.service.temination.application.dto.FormDataMapDto;
import com.caidaocloud.hr.service.temination.application.dto.FormDefDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(
        value = "caidaocloud-hr-paas-service",
        fallback = FormFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "hrPaasFormFeignClient"
)
public interface FormFeignClient {
    /**
     * 表单配置列表(无分页)
     *
     * @param formType
     * @param implantable
     * @return
     */
    @GetMapping("/api/hrpaas/v1/form/def/list")
    Result<List<FormDefDto>> getFormDefList(@RequestParam String formType,
                                            @RequestParam(required = false, defaultValue = "false") boolean implantable);

    /**
     * 表单配置详情(名称查询)
     *
     * @param name
     * @return
     */
    @GetMapping("/api/hrpaas/v1/form/def/one/:name")
    Result<FormDefDto> getFormDef(@RequestParam("name") String name);

    /**
     * 表单配置详情
     *
     * @param id
     * @return
     */
    @GetMapping("/api/hrpaas/v1/form/def/one")
    Result<FormDefDto> getFormDefById(@RequestParam("id") String id);

    /**
     * 查询表单数据
     *
     * @param formId
     * @param id
     * @return
     */
    @GetMapping("/api/hrpaas/v1/form/data/one/{formId}")
    Result<FormDataMapDto> getFormDataMap(@PathVariable("formId") String formId,
                                          @RequestParam("id") String id);

    /**
     * 保存表单数据
     *
     * @param formId
     * @param formDataDto
     * @return
     */
    @PostMapping("/api/hrpaas/v1/form/data/create/{formId}")
    JSONObject saveFormData(@PathVariable String formId, @RequestBody FormDataDto formDataDto);

    /**
     * 更新表单
     *
     * @param formId
     * @param formDataDto
     * @return
     */
    @PutMapping("/api/hrpaas/v1/form/data/update/{formId}")
    Result update(@PathVariable String formId, @RequestBody FormDataDto formDataDto);
}