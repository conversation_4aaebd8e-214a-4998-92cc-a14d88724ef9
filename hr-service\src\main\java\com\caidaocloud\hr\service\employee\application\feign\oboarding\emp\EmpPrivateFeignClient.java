package com.caidaocloud.hr.service.employee.application.feign.oboarding.emp;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.common.application.dto.MappingConfigDto;
import com.caidaocloud.hr.service.employee.application.feign.fallback.EmpPrivateFallBack;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpEntryProcessDisableDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpEntryProcessDisableVo;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPrivacyQueryDto;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "caidaocloud-on-boarding-service", fallback = EmpPrivateFallBack.class, configuration = FeignConfiguration.class)
public interface EmpPrivateFeignClient {
    @PostMapping("/api/onboarding/employee/private/v1/getPrivacyInfoList")
    Result getPrivacyInfoList(@RequestBody EmpPrivacyQueryDto dto);

    @PostMapping("/api/onboarding/employee/entry/process/v1/getEmpProcessInfoList")
    Result getEmpProcessInfoList(@RequestBody EmpPrivacyQueryDto dto);

    @PostMapping("/api/onboarding/employee/entry/process/v1/disable/page")
    Result<PageResult<EmpEntryProcessDisableVo>> getDisableEmpEntryProcessPage(@RequestBody EmpEntryProcessDisableDto disableDto);

    @GetMapping("/api/onboarding/v1/mapping/config/listByIds")
    Result<List<MappingConfigDto>> listByIds(@RequestParam(name = "ids") String ids);


}
