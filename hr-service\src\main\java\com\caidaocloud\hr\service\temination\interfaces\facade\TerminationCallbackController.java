package com.caidaocloud.hr.service.temination.interfaces.facade;

import com.caidaocloud.hr.service.temination.application.TerminationChangeService;
import com.caidaocloud.hr.service.temination.application.TerminationWorkflowService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @date 2023/1/13
 */
@RestController
@RequestMapping("/api/hr/v1/termination/callback")
@Api(value = "/api/hr/v1/termination/callback", description = "离职回调")
@Slf4j
public class TerminationCallbackController {
	@Autowired
	private TerminationChangeService terminationChangeService;
	@Resource
	private TerminationWorkflowService terminationWorkflowService;

	@PostMapping
	@Deprecated
	public Result callback(@RequestBody WfCallbackResultDto dto) {
		if (log.isDebugEnabled()) {
			log.debug("Work flow callback,{}", dto);
		}
		terminationChangeService.callback(dto.getBusinessKey(), dto.getTenantId(), dto.getCallbackType());
		return Result.ok(true);
	}

	/**
	 * 工作流回调-审批通过
	 * @param dto
	 * @return
	 */
	@PostMapping("approved")
	public Result approved(@RequestBody WfCallbackResultDto dto) {
		log.info("Work flow approved callback,{}", dto);
		terminationChangeService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.APPROVED);
		return Result.ok(true);
	}

	/**
	 * 工作流回调-审批拒绝
	 * @param dto
	 * @return
	 */
	@PostMapping("refused")
	public Result refused(@RequestBody WfCallbackResultDto dto) {
		log.info("Work flow refused callback,{}", dto);
		terminationChangeService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.REFUSED);
		return Result.ok(true);
	}

	/**
	 * 工作流回调-审批撤销
	 * @param dto
	 * @return
	 */
	@PostMapping("revoke")
	public Result revoke(@RequestBody WfCallbackResultDto dto) {
		log.info("Work flow refused callback,{}", dto);
		terminationChangeService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.REVOKE);
		return Result.ok(true);
	}

	/**
	 * 工作流回调-异常
	 * @param dto
	 * @return
	 */
	@PostMapping("error")
	public Result error(@RequestBody WfCallbackResultDto dto) {
		log.info("Work flow error callback,{}", dto);
		terminationChangeService.callback(dto.getBusinessKey(), dto.getTenantId(), WfCallbackTriggerOperationEnum.ERROR);
		return Result.ok(true);
	}

	/**
	 * 流程变量手动注册
	 */
	@PutMapping("/regNoticeVar")
	public Result regNoticeVar(@RequestParam(value="funCode") String funCode) {
		log.info("------------- regNoticeVar funCode={}", funCode);
		terminationWorkflowService.registerNoticeVar(funCode);
		return Result.ok(true);
	}

	@PostMapping("/writeBack")
	public Result writeBack(@RequestBody WfCallbackResultDto dto) {
		log.info("termination Work flow writeBack,{}", FastjsonUtil.toJson(dto));
		terminationChangeService.writeBack(dto.getBusinessKey());
		return Result.ok(true);
	}
}
