<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>caidao-hr-service</artifactId>
        <groupId>com.caidaocloud</groupId>
        <version>2.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hr-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
    
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>record-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>hr-core</artifactId>
            <scope>compile</scope>
            <version>2.0.6-SNAPSHOT</version>
        </dependency>
        
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>message-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>galaxy-service-mongo</artifactId>
                    <groupId>com.caidaocloud</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>morphia</artifactId>
                    <groupId>org.mongodb.morphia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-data-mongodb</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>workflow-sdk</artifactId>
            <version>2.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>fr.opensagres.xdocreport</groupId>
            <artifactId>fr.opensagres.xdocreport.converter.docx.xwpf</artifactId>
            <version>2.0.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>5.2.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>ooxml-schemas</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>5.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.12.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.26.1</version>
        </dependency>
        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-oss-service</artifactId>
            <version>2.4.1-SDK-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.jettison</groupId>
                    <artifactId>jettison</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>

        <dependency>
            <groupId>com.caidaocloud</groupId>
            <artifactId>caidaocloud-oss-service</artifactId>
            <version>2.4.1-SDK-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.codehaus.jettison</groupId>
                    <artifactId>jettison</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.caidaocloud.hr.service.HRApplication</mainClass>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
