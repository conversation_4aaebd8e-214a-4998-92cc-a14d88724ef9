package com.caidaocloud.hr.service.growthrecord.interfaces.facade;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.growthrecord.domain.entity.GrowthRecordDO;
import com.caidaocloud.hr.service.growthrecord.domain.service.GrowthRecordService;
import com.caidaocloud.hr.service.growthrecord.interfaces.dto.GrowthRecordQueryDTO;
import com.caidaocloud.hr.service.growthrecord.interfaces.vo.GrowthRecordVO;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 成长记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/hr/growthrecord/v1")
@Api(value = "/api/hr/growthrecord/v1", description = "成长记录", tags = "V1.0")
public class GrowthRecordController {

    @Resource
    private GrowthRecordService growthRecordService;

    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    @PostMapping("/list")
    @ApiOperation("获取员工成长记录")
    public Result<List<GrowthRecordVO>> listByEmpIdAndDueDateAndBusinessEventType(@RequestBody GrowthRecordQueryDTO growthRecordQueryDTO) {
        UserInfo userInfo = UserContext.preCheckUser();
        growthRecordQueryDTO.setTenantId(userInfo.getTenantId());
        List<GrowthRecordVO> growthRecordDOList = growthRecordService.listByEmpIdAndDueDateAndBusinessEventType(growthRecordQueryDTO);
        if (CollectionUtils.isEmpty(growthRecordDOList)) {
            return Result.ok(Collections.emptyList());
        }
        growthRecordDOList.forEach(growth->{
            growth.getDataList().forEach(it->{
                if("postTxt".equals(it.getProp())){
                    if(!"enabled".equals(postTxtShowCode)){

                        String postTxt = (String)it.getValue();
                        if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
                            postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                            it.setValue(postTxt);
                        }
                    }
                }
            });
        });
        return Result.ok(growthRecordDOList);
    }

    @PostMapping(value = "/create")
    public Result create(@RequestBody GrowthRecordDO growthRecordDO) {
        growthRecordService.create(growthRecordDO);
        return Result.ok();
    }

    @GetMapping(value = "/businessEventType/list")
    @ApiOperation("获取员工成长记录中的业务事件类型")
    public Result<Map<String, String>> listBusinessEventType(@RequestParam(value = "empId") String empId) {
        UserInfo userInfo = UserContext.preCheckUser();
        return Result.ok(growthRecordService.listBusinessEventType(empId, userInfo.getTenantId()));
    }

    @PostMapping(value = "/delete")
    public void deleteById(@RequestParam String id) {
        growthRecordService.deleteById(id);
    }

    @ApiOperation("成长记录导入")
    @PostMapping("/import")
    public Result<List<String>> importGrowthRecord(@RequestParam("file") MultipartFile multipartFile) {
        UserInfo userInfo = UserContext.preCheckUser();
        List<String> importFailGrowthRecordIdList = growthRecordService.importGrowthRecord(multipartFile, userInfo.getTenantId());
        return Result.ok(importFailGrowthRecordIdList);
    }
}
