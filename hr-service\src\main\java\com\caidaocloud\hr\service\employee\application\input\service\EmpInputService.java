package com.caidaocloud.hr.service.employee.application.input.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.core.dto.UserSyncDto;
import com.caidaocloud.hr.core.feign.UserFeignClient;
import com.caidaocloud.hr.service.contract.application.enums.ContractHideStatus;
import com.caidaocloud.hr.service.contract.application.service.ContractService;
import com.caidaocloud.hr.service.contract.domain.entity.ContractDo;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractDto;
import com.caidaocloud.hr.service.dto.*;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.*;
import com.caidaocloud.hr.service.employee.application.familyInfo.service.FamilyInfoService;
import com.caidaocloud.hr.service.employee.application.familyInfo.service.FamilyMemberService;
import com.caidaocloud.hr.service.employee.application.workOverview.service.WorkExperienceService;
import com.caidaocloud.hr.service.employee.application.workOverview.service.WorkOverviewService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.workExperience.entity.WorkExperienceDo;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpInfoDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.input.EmpInputDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EnumSimple;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工信息接入
 */
@Slf4j
@Service
public class EmpInputService {
    private static SnowflakeUtil snowFlake = new SnowflakeUtil(1L, 1L);
    @Resource
    private EmpBasicInfoService empBasicInfoService;
    @Resource
    private EmpWorkInfoService empWorkInfoService;
    @Resource
    private EmpConcurrentPostService empConcurrentPostService;
    @Resource
    private EmpPrivateInfoService empPrivateInfoService;
    @Resource
    private EmpBankService empBankService;
    @Resource
    private EmpEmergencyContactService empEmergencyContactService;
    @Resource
    private EmpOtherOrgService empOtherOrgService;
    @Resource
    private WorkOverviewService workOverviewService;
    @Resource
    private WorkExperienceService workExperienceService;
    @Resource
    private EmpEduInfoService empEduInfoService;
    @Resource
    private EmpEduExperienceService empEduExperienceService;
    @Resource
    private FamilyInfoService familyInfoService;
    @Resource
    private FamilyMemberService familyMemberService;
    @Resource
    private EmpRewardService empRewardService;
    @Resource
    private EmpFileAttachmentService empFileAttachmentService;
    @Resource
    private ContractService contractService;
    @Resource
    private UserFeignClient userFeignClient;

    /**
     * 添加员工前的参数检查
     *
     * @param empInputDto
     */
    private void preCheckBeforeAddEmp(EmpInputDto empInputDto) {
        // 员工兼岗信息
        if (null != empInputDto.getEmpConcurrentPost()) {
            EmpConcurrentPostDto empConcurrentPost = empInputDto.getEmpConcurrentPost();
            PreCheck.preCheckArgument(StringUtils.isNotBlank(empConcurrentPost.getRemark())
                    && empConcurrentPost.getRemark().length() > 200, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30050));
            PreCheck.preCheckArgument(empConcurrentPost.getEndDate() <= empConcurrentPost.getStartDate(),
                    LangUtil.getMsg(MsgCodeConstant.END_GREATER_STR));
        }
        // 新增非行政组织
        if (null != empInputDto.getEmpOtherOrg()) {
            EmpOtherOrgDto empOtherOrgDto = empInputDto.getEmpOtherOrg();
            PreCheck.preCheckArgument(null == empOtherOrgDto.getSchemaType(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30070));
            PreCheck.preCheckArgument(StringUtils.isBlank(empOtherOrgDto.getOrganize()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30071));
        }
        // 工作概况保存/更新
        if (null != empInputDto.getWorkOverview()) {
            WorkOverviewDto workOverviewDto = empInputDto.getWorkOverview();
            PreCheck.preCheckArgument(workOverviewDto.getFirstWorkDate() == null, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30097));
        }
    }


    /**
     * 添加员工
     * @param empInputDto
     * @return
     */
    public String addEmp(EmpInputDto empInputDto) {
        preCheckBeforeAddEmp(empInputDto);

        // 新增员工（任职信息、个人信息、档案附件信息）
        EmpInfoDto empInfoDto = ObjectConverter.convert(empInputDto, EmpInfoDto.class);

        String empId = empBasicInfoService.submitEmpInfo(empInfoDto);

        /*同步创建user*/
        if (empInputDto.isSyncUser()) {
            EmpWorkInfoDto workInfoDto = empInfoDto.getEmpWorkInfo();
            EmpPrivateInfoDto privateInfoDto = empInfoDto.getEmpPrivateInfo();
            UserSyncDto userSyncDto = new UserSyncDto();
            // 账号信息
            userSyncDto.setAccount(empInputDto.getAccount());
            userSyncDto.setEmpId(Long.valueOf(empId));
            userSyncDto.setUserId(Long.valueOf(empInputDto.getUserId()));
            userSyncDto.setOp("UPDATE");
            userSyncDto.setOptValue("UPDATE");
            userSyncDto.setEmail(workInfoDto.getCompanyEmail());
            userSyncDto.setMobNum(null != privateInfoDto.getPhone() ? privateInfoDto.getPhone().getValue() : "");
            userSyncDto.setUserName(workInfoDto.getName());
            userSyncDto.setTenantId(Long.valueOf(empInputDto.getTenantId()));
            userSyncDto.setStatus(1);
            userSyncDto.setDeleted(0);
            userFeignClient.syncUserInfo(userSyncDto);
        }

        // 新增员工兼岗信息
        if (null != empInputDto.getEmpConcurrentPost()) {
            EmpConcurrentPostDto empConcurrentPost = empInputDto.getEmpConcurrentPost();
            empConcurrentPost.setEmpId(empId);
            empConcurrentPostService.save(empConcurrentPost);
        }

        // 新增银行卡信息
        if (null != empInputDto.getEmpBank()) {
            EmpBankDto empBankDto = empInputDto.getEmpBank();
            empBankDto.setEmpId(empId);
            empBankService.save(empBankDto);
        }

        // 新增紧急联系人
        if (null != empInputDto.getEmpEmergencyContact()) {
            EmpEmergencyContactDto empEmergencyContactDto = empInputDto.getEmpEmergencyContact();
            empEmergencyContactDto.setEmpId(empId);
            empEmergencyContactService.save(empEmergencyContactDto);
        }

        // 新增非行政组织
        if (null != empInputDto.getEmpOtherOrg()) {
            EmpOtherOrgDto empOtherOrgDto = empInputDto.getEmpOtherOrg();
            empOtherOrgDto.setEmpId(empId);
            empOtherOrgService.save(empOtherOrgDto);
        }

        // 工作概况保存/更新
        if (null != empInputDto.getWorkOverview()) {
            WorkOverviewDto workOverviewDto = empInputDto.getWorkOverview();
            workOverviewDto.setEmpId(empId);
            workOverviewService.saveWorkOverview(workOverviewDto);
        }

        // 工作经历编辑/保存
        if (null != empInputDto.getWorkExperience()) {
            WorkExperienceDto workExperienceDto = empInputDto.getWorkExperience();
            workExperienceDto.setEmpId(empId);
            WorkExperienceDo data = ObjectConverter.convert(workExperienceDto, WorkExperienceDo.class);
            workExperienceService.saveWorkExperience(data);
        }

        // 编辑学历概况
        if (null != empInputDto.getEmpEduInfo()) {
            EmpEduInfoDto empEduInfoDto = empInputDto.getEmpEduInfo();
            empEduInfoDto.setEmpId(empId);
            empEduInfoService.update(empEduInfoDto);
        }

        // 新增教育经历
        if (null != empInputDto.getEmpEduExperience()) {
            EmpEduExperienceDto empEduExperienceDto = empInputDto.getEmpEduExperience();
            empEduExperienceDto.setEmpId(empId);
            empEduExperienceService.save(empEduExperienceDto);
        }

        // 家庭信息保存/更新
        if (null != empInputDto.getFamilyInfo()) {
            FamilyInfoDto familyInfoDto = empInputDto.getFamilyInfo();
            familyInfoDto.setEmpId(empId);
            familyInfoService.saveOrUpdate(familyInfoDto);
        }

        // 家庭成员保存/更新
        if (null != empInputDto.getFamilyMember()) {
            FamilyMemberDto familyMemberDto = empInputDto.getFamilyMember();
            familyMemberDto.setEmpId(empId);
            familyMemberService.save(familyMemberDto);
        }

        // 新增奖惩经历
        if (null != empInputDto.getEmpReward()) {
            EmpRewardDto empRewardDto = empInputDto.getEmpReward();
            empRewardDto.setEmpId(empId);
            empRewardService.save(empRewardDto);
        }

        return empId;
    }

    /**
     * 员工合同信息新增
     * @param empInputContractDto
     */
    public String addEmpContract(EmpInputContractDto empInputContractDto){
        if(null != empInputContractDto){
            ContractDto  contractDto = ObjectConverter.convert(empInputContractDto, ContractDto.class);
            contractDto.setBid(empInputContractDto.getContractBid());
            //审批状态 历史合同、续签都是通过
            if (empInputContractDto.getApprovalStatus() != null) {
                EnumSimple approvalSimple = new EnumSimple();
                approvalSimple.setValue(empInputContractDto.getApprovalStatus());
                contractDto.setApprovalStatus(approvalSimple);
            }
            //员工信息
            EmpSimple empSimple = new EmpSimple();
            empSimple.setName(empInputContractDto.getName());
            empSimple.setEnName(empInputContractDto.getEnName());
            empSimple.setEmpId(empInputContractDto.getEmpId());
            empSimple.setWorkno(empInputContractDto.getWorkNo());
            contractDto.setOwner(empSimple);

            DictSimple contractTypeSimple = new DictSimple();
            contractTypeSimple.setValue(empInputContractDto.getContractTypeDictValue());
            contractTypeSimple.setText(empInputContractDto.getContractTypeDictText());
            contractTypeSimple.setCode(empInputContractDto.getContractTypeDictCode());
            contractDto.setContractType(contractTypeSimple);

            EnumSimple empStatusSimple = new EnumSimple();
            empStatusSimple.setValue(empInputContractDto.getEmpStatus());
            contractDto.setEmpStatus(empStatusSimple);

            EnumSimple signTypeSimple = new EnumSimple();
            signTypeSimple.setValue(empInputContractDto.getSignType());
            contractDto.setSignType(signTypeSimple);

            EnumSimple periodSimple = new EnumSimple();
            periodSimple.setValue(empInputContractDto.getPeriodType());
            contractDto.setPeriodType(periodSimple);

            EnumSimple contractStatusSimple = new EnumSimple();
            contractStatusSimple.setValue(empInputContractDto.getContractStatus());
            contractDto.setContractStatus(contractStatusSimple);

            EnumSimple enumSimple = new EnumSimple();
            enumSimple.setValue(empInputContractDto.isHideInApproval() ? ContractHideStatus.HIDE.getCode() :
                    ContractHideStatus.SHOW.getCode());
            contractDto.setIsHideInApproval(enumSimple);

            contractDto.setContractNo(StringUtils.isNotEmpty(empInputContractDto.getContractNo()) ? empInputContractDto.getContractNo()
                    : "C" + snowFlake.createId());

            /**
             * 计算合同时长
             */
            if (contractDto.getContractPeriod() == null && (contractDto.getStartDate() != null &&
                    contractDto.getEndDate() != null)) {
                contractDto.setContractPeriod((int) DateUtil.getMonthDiff(
                        contractDto.getStartDate(), contractDto.getEndDate()
                ));
            }

            ContractDo contractDo = contractService.submitContract(contractDto);

            return contractDo == null ? null : contractDo.getBid();
        }
        return null;
    }

    /**
     * 获取员工是否有审核通过合同数据
     * @param empId
     * @return
     */
    public boolean getEmpCurrent(String empId){
        List<ContractDo> contractDoList = contractService.getEmpHistoryContract(empId);
        if(!CollectionUtils.isEmpty(contractDoList)){
            return true;
        }
        return false;
    }

    public EmpWorkInfoDto getEmpByWorkNo(String workno) {
        List<EmpWorkInfoDo> empListByWorkNo = empWorkInfoService.getEmpListByWorkno(Lists.newArrayList(workno));
        return CollectionUtils.isEmpty(empListByWorkNo) ? null : ObjectConverter.convert(empListByWorkNo.get(0), EmpWorkInfoDto.class);
    }

    public List<EmpWorkInfoDto> getEmpByWorkNo(List<String> workNos) {
        List<EmpWorkInfoDo> empListByWorkNo = empWorkInfoService.getEmpListByWorkno(workNos);
        return CollectionUtils.isEmpty(empListByWorkNo) ? Lists.newArrayList() : ObjectConverter.convertList(empListByWorkNo, EmpWorkInfoDto.class);
    }

    public ContractDto getContractByNo(String code) {
        List<ContractDo> contracts = contractService.getContractByContractNo(Lists.newArrayList(code));
        return CollectionUtils.isEmpty(contracts) ? null : ObjectConverter.convert(contracts.get(0), ContractDto.class);
    }

    /**
     * 迪士尼用 不验证
     * @param empInputDto
     * @return
     */
    public String onlyAddEmp(EmpInputDto empInputDto) {
        // 新增员工（任职信息、个人信息、档案附件信息）
        EmpInfoDto empInfoDto = ObjectConverter.convert(empInputDto, EmpInfoDto.class);
        return empBasicInfoService.saveEmpInfo(empInfoDto);
    }
}
