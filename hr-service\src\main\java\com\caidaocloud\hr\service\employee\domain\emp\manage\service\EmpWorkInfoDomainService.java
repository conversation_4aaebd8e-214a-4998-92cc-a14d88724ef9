package com.caidaocloud.hr.service.employee.domain.emp.manage.service;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.dto.growthrecord.GrowthRecordDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.core.feign.UserFeignClient;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpWorkInfoRepository;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.entity.RetireRuleDo;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.service.RetireRuleDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpConcurrentPostLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPageQueryDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpSearchColumnsDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.PhotoUploadDto;
import com.caidaocloud.hr.service.enums.growthrecord.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.growthrecord.application.event.publish.GrowthRecordPublish;
import com.caidaocloud.hr.service.organization.application.org.dto.OrgStructureEmpDto;
import com.caidaocloud.hr.service.util.FunUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.util.*;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class EmpWorkInfoDomainService {
    @Resource
    private EmpWorkInfoDo empWorkInfoDo;
    @Resource
    private EmpBasicInfoDo empBasicInfoDo;
    @Resource
    private WorknoAutoDomainService worknoAutoDomainService;
    @Resource
    private EmpPrivateInfoDomainService empPrivateInfoDomainService;
    @Resource
    private EmpBasicInfoDomainService empBasicInfoDomainService;
    @Resource
    private EmpPostRecordDomainService empPostRecordDomainService;
    @Resource
    private List<IWorkInfoProcessService> workInfoProcessServices;
    @Resource
    private RetireRuleDomainService retireRuleDomainService;
    @Resource
    private IEmpWorkInfoRepository empWorkInfoRepository;
    @Resource
    private GrowthRecordPublish growthRecordPublish;


    public EmpWorkInfoDo getEmpWorkInfo(String empId, Long dataTime) {
        return empWorkInfoDo.getEmpWorkInfo(empId, dataTime);
    }

    public EmpWorkInfoDo getEmpByWorkno(String workno, Long dataTime) {
        return empWorkInfoDo.getEmpByWorkno(workno, dataTime);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByTime(Long dateTime) {
        return empWorkInfoDo.getEmpWorkInfoByTime(dateTime);
    }

    public List<EmpWorkInfoDo> getEmpMainPostForLeader(String leaderEmpId, Long dateTime) {
        return empWorkInfoDo.getEmpMainPostForLeader(leaderEmpId, dateTime);
    }

    public EmpWorkInfoDo save(EmpWorkInfoDo data) {
        // 如果工号不为空，则根据工号配置策略进行修改或不允许修改
        data.setWorkno(worknoAutoDomainService.nextWorkno(data.getWorkno(), data.getWorkno()));
        EmpPrivateInfoDo privateInfo = empPrivateInfoDomainService.getByEmpId(data.getEmpId());
        calcRetireDate(data, privateInfo);
        empWorkInfoDo.save(data);
        return data;
    }

    @Resource
    private UserFeignClient userFeignClient;

    @Transactional(rollbackFor = Exception.class)
    public void deleteAllWorkInfo(EmpWorkInfoDo data) {
        // 删除工作基本信息
        empWorkInfoDo.deleteAll(data);
        // 删除个人信息
        empPrivateInfoDomainService.deleteByEmpId(data.getEmpId());
        // 删除基本信息
        empBasicInfoDomainService.delete(data.getEmpId());
        // 删除用户
        userFeignClient.deleteUserInfo(data.getEmpId(), data.getTenantId());
    }

    public void updateByEmpId(EmpWorkInfoDo data) {
        long dataTime = System.currentTimeMillis();
        // 任职信息
        EmpWorkInfoDo workInfo = empWorkInfoDo.getEmpWorkInfo(data.getEmpId(), dataTime);
        if (null != workInfo) {
            data.setBid(workInfo.getBid());
            // 校验工号是否重复
            List<EmpWorkInfoDo> worknoList = empWorkInfoDo.getByWorkno(data.getBid(), data.getWorkno(), data.getDataStartTime());
            PreCheck.preCheckArgument(null != worknoList && !worknoList.isEmpty(), LangUtil.getMsg(MsgCodeConstant.WORKNO_NOT_DUPLICATE));

            log.info("workInfo={}", FastjsonUtil.toJson(workInfo));
            empWorkInfoDo.update(data);
        }
    }

    public void update(EmpWorkInfoDo data, boolean checkGraduateDate) {
        updateEmpWorkInfo(data, checkGraduateDate);
    }

    public void update(EmpWorkInfoDo data) {
        updateEmpWorkInfo(data, true);
    }

    public void updateEmpWorkInfo(EmpWorkInfoDo data, boolean checkGraduateDate) {
        // 任职信息
        EmpWorkInfoDo original = empWorkInfoDo.getEmpWorkInfo(data.getEmpId(), data.getDataStartTime());
        data.setPhoto(original.getPhoto());
        if (original != null && original.getEmpId() != null) {
            data.setBid(original.getBid());
            if (StringUtils.isEmpty(data.getWorkno())) {
                // 如果工号为空，则使用 db 中的工号
                data.setWorkno(original.getWorkno());
            } else {
                // 如果工号不为空，则根据工号配置策略进行修改或不允许修改
                data.setWorkno(worknoAutoDomainService.nextWorkno(data.getWorkno(), original.getWorkno()));
            }
            DataEntity.initDataSimpleBaseFieldValue(original.getIdentifier(), data, original);
        } else {
            List<EmpWorkInfoDo> workInfoDoList = empWorkInfoDo.getEmpWorkInfoList(data.getEmpId());
            PreCheck.preCheckArgument(CollectionUtils.isEmpty(workInfoDoList), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30069));
            EmpWorkInfoDo oldWorkInfo = workInfoDoList.get(0);
            if (StringUtils.isEmpty(data.getWorkno())) {
                data.setWorkno(oldWorkInfo.getWorkno());
            }
            data.setBid(oldWorkInfo.getBid());
            data.setEmpId(oldWorkInfo.getEmpId());
            DataEntity.initDataSimpleBaseFieldValue(oldWorkInfo.getIdentifier(), data, oldWorkInfo);
        }

        // 校验工号是否重复
        List<EmpWorkInfoDo> worknoList = empWorkInfoDo.getByWorkno(data.getBid(), data.getWorkno(), data.getDataStartTime());
        PreCheck.preCheckArgument(null != worknoList && !worknoList.isEmpty(), LangUtil.getMsg(MsgCodeConstant.WORKNO_NOT_DUPLICATE));

        // 查询个人信息
        EmpPrivateInfoDo privateInfo = empPrivateInfoDomainService.getByEmpId(data.getEmpId());
        if (privateInfo != null) {
            data.setName(privateInfo.getName());
            data.setEnName(privateInfo.getEnName());
        }
        
        // DEV-21679
        if (data.getRetireDate() == null) {
            calcRetireDate(data, privateInfo);
        }

        // DEV-5228
        if (null == data.getLeadEmpId()) {
            data.setLeadEmpId(new EmpSimple());
        }

        // 更新任职信息
        beforeUpdate(data, original);
        empWorkInfoDo.updateEmpWorkInfo(data, checkGraduateDate);

        // 更新基本信息
        EmpBasicInfoDo basicInfo = new EmpBasicInfoDo();
        BeanUtil.copyProperties(data, basicInfo, "id", "identifier", "bid", "tenantId", "createTime", "createBy",
                "updateTime", "updateBy", "deleted");
        basicInfo.setBid(data.getEmpId());
        if (privateInfo != null) {
            basicInfo.setSex(privateInfo.getSex());
            basicInfo.setPhone(privateInfo.getPhone());
        }
        EmpBasicInfoDo oldBasicInfo = empBasicInfoDo.getEmpBasicInfo(data.getEmpId(), data.getDataStartTime());
        if (oldBasicInfo == null) {
            List<EmpBasicInfoDo> empBasicInfoList = empBasicInfoDo.getList(data.getEmpId());
            if (CollectionUtils.isNotEmpty(empBasicInfoList)) {
                oldBasicInfo = empBasicInfoList.get(0);
            }
        }
        DataEntity.initDataSimpleBaseFieldValue(EmpBasicInfoDo.IDENTIFIER, basicInfo, oldBasicInfo);
        empBasicInfoDo.update(basicInfo);
    }

    public void updateJoinTxt(EmpWorkInfoDo data) {
        // 任职信息
        DataEntity.initDataSimpleBaseFieldValue(data.getIdentifier(), data, data);
        // 查询个人信息
        EmpPrivateInfoDo privateInfo = empPrivateInfoDomainService.getByEmpId(data.getEmpId());
        if (privateInfo != null) {
            data.setName(privateInfo.getName());
            data.setEnName(privateInfo.getEnName());
        }
        // DEV-5228
        if (null == data.getLeadEmpId()) {
            data.setLeadEmpId(new EmpSimple());
        }

        // 更新任职信息
        empWorkInfoDo.updateEmpWorkInfo(data, false);

        // 更新基本信息
        EmpBasicInfoDo basicInfo = new EmpBasicInfoDo();
        BeanUtil.copyProperties(data, basicInfo, "id", "identifier", "bid", "tenantId", "createTime", "createBy",
                "updateTime", "updateBy", "deleted");
        basicInfo.setBid(data.getEmpId());
        if (privateInfo != null) {
            basicInfo.setSex(privateInfo.getSex());
            basicInfo.setPhone(privateInfo.getPhone());
        }
        DataEntity.initDataSimpleBaseFieldValue(EmpBasicInfoDo.IDENTIFIER, basicInfo, basicInfo);
        empBasicInfoDo.update(basicInfo);
    }

    public Long calcRetireDate(String empId, Long birthDate) {
        if (birthDate == null) {
            log.warn("birth data is empty,empId={}", empId);
            return null;
        }
        List<RetireRuleDo> retireRuleList = retireRuleDomainService.getList();
        retireRuleList.removeIf(rule -> {
            ConditionTree conditionTree = FastjsonUtil.convertObject(rule.getCondition(), ConditionTree.class);
            return !conditionTree.match(empId, rule.getMapping());
        });
        if (retireRuleList.size() != 1) {
            log.warn("retire rule not found,empId={}", empId);
            return null;
        }
        ZoneOffset offset = OffsetDateTime.now().getOffset();
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(birthDate), offset);
        dateTime = dateTime.plusYears(retireRuleList.get(0).getRetireAge());
        return dateTime.toInstant(offset).toEpochMilli();
    }

    public void calcRetireDate(EmpWorkInfoDo workInfo, EmpPrivateInfoDo privateInfo) {
        val date = calcRetireDate(workInfo.getEmpId(), privateInfo.getBirthDate());
        workInfo.setRetireDate(date);
    }

    private void beforeUpdate(EmpWorkInfoDo after, EmpWorkInfoDo before) {
        for (IWorkInfoProcessService service : workInfoProcessServices) {
            if (service.apply(after, before)) {
                return;
            }
        }
    }

    public void updateOnly(EmpWorkInfoDo data, EmpWorkInfoDo original, List<EmpWorkInfoDo> worknoList) {
        // 任职信息
//        EmpWorkInfoDo original = empWorkInfoDo.getEmpWorkInfo(data.getEmpId(), data.getDataStartTime());
        if (original != null && original.getEmpId() != null) {
            data.setBid(original.getBid());
            if (StringUtils.isEmpty(data.getWorkno())) {
                // 如果工号为空，则使用 db 中的工号
                data.setWorkno(original.getWorkno());
            } else {
                // 如果工号不为空，则根据工号配置策略进行修改或不允许修改
                data.setWorkno(worknoAutoDomainService.nextWorkno(data.getWorkno(), original.getWorkno()));
            }
            DataEntity.initDataSimpleBaseFieldValue(original.getIdentifier(), data, original);
        } else {
//            List<EmpWorkInfoDo> workInfoDoList = empWorkInfoDo.getEmpWorkInfoList(data.getEmpId());
//            PreCheck.preCheckArgument(CollectionUtils.isEmpty(workInfoDoList), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30069));
//            EmpWorkInfoDo oldWorkInfo = workInfoDoList.get(0);
            if (StringUtils.isEmpty(data.getWorkno())) {
                data.setWorkno(original.getWorkno());
            }
            data.setBid(original.getBid());
            data.setEmpId(original.getEmpId());
            DataEntity.initDataSimpleBaseFieldValue(original.getIdentifier(), data, original);
        }

        boolean flag = false;
        if (null != worknoList) {
            for (EmpWorkInfoDo infoDo : worknoList) {
                if (!infoDo.getEmpId().equals(data.getEmpId())) {
                    flag = true;
                    break;
                }
            }
        }
        // 校验工号是否重复
//        List<EmpWorkInfoDo> worknoList = empWorkInfoDo.getByWorkno(data.getBid(), data.getWorkno(), data.getDataStartTime());
//        List<EmpWorkInfoDo> worknoList = empWorkInfoDo.getByWorkno(data.getBid(), data.getWorkno(), data.getDataStartTime());
        PreCheck.preCheckArgument(flag, LangUtil.getMsg(MsgCodeConstant.WORKNO_NOT_DUPLICATE));

        // 查询个人信息， 导入一个入口，已经更新个人信息和基本信息
//        EmpPrivateInfoDo privateInfo = empPrivateInfoDomainService.getByEmpId(data.getEmpId());
//        if (privateInfo != null) {
//            data.setName(privateInfo.getName());
//            data.setEnName(privateInfo.getEnName());
//        }

        // 更新任职信息
        empWorkInfoDo.update(data);

        // 更新基本信息
//        EmpBasicInfoDo basicInfo = new EmpBasicInfoDo();
//        DataSimpleUtil.copyProperties(data, basicInfo, "id", "identifier", "bid", "tenantId", "createTime", "createBy",
//                "updateTime", "updateBy", "deleted");
//        basicInfo.setBid(data.getEmpId());
//        if (privateInfo != null) {
//            basicInfo.setSex(privateInfo.getSex());
//            basicInfo.setPhone(privateInfo.getPhone());
//        }
//        EmpBasicInfoDo oldBasicInfo = empBasicInfoDo.getEmpBasicInfo(data.getEmpId(), data.getDataStartTime());
//        if (oldBasicInfo == null) {
//            List<EmpBasicInfoDo> empBasicInfoList = empBasicInfoDo.getList(data.getEmpId());
//            if (CollectionUtils.isNotEmpty(empBasicInfoList)) {
//                oldBasicInfo = empBasicInfoList.get(0);
//            }
//        }
//        DataEntity.initDataSimpleBaseFieldValue(null, basicInfo, oldBasicInfo);
//        empBasicInfoDo.update(basicInfo);
    }

    public PageResult<EmpWorkInfoDo> selectPage(EmpPageQueryDto dto) {
        if (Objects.equals("0", dto.getOrganize())) {
            // 如果是公司节点，则查询所有员工
            dto.setOrganize(null);
        }

        return empWorkInfoDo.selectPage(dto);
    }


    public void photoUpload(PhotoUploadDto dto) {
        EmpWorkInfoDo data = ObjectConverter.convert(dto, EmpWorkInfoDo.class);
        EmpWorkInfoDo original = empWorkInfoDo.getEmpWorkInfo(data.getEmpId(), dto.getDataStartTime());
        PreCheck.preCheckArgument(null == original || StringUtils.isEmpty(original.getBid()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30069));
        // 更新workInfo的头像信息
        DataEntity.initDataSimpleBaseFieldValue(null, data, original);
        BeanUtil.copyWithNoValue(data, original);
        Attachment photo = data.getPhoto();
        if (null == photo) {
            photo = new Attachment();
            photo.setNames(new ArrayList<>());
            photo.setUrls(photo.getNames());
        } else if (null == photo.getUrls()) {
            photo.setNames(new ArrayList<>());
            photo.setUrls(photo.getNames());
        }

        original.setPhoto(photo);
        original.setDataStartTime(dto.getDataStartTime());
        empWorkInfoDo.update(original);
        // 更新basicInfo的头像信息
        EmpBasicInfoDo empBasicInfo = empBasicInfoDo.getEmpBasicInfo(data.getEmpId(), dto.getDataStartTime());
        BeanUtil.copyWithNoValue(data, empBasicInfo);
        empBasicInfo.setPhoto(photo);
        empBasicInfo.setDataStartTime(dto.getDataStartTime());
        empBasicInfoDo.update(empBasicInfo);
    }

    public List<EmpWorkInfoDo> getEmpListByCompanyEmail(List<String> companyEmailList) {
        return empWorkInfoDo.getEmpListByCompanyEmail(companyEmailList);
    }

    public List<EmpWorkInfoDo> getLeaveEmpList(Long leaveDate, Integer empStatus) {
        return empWorkInfoDo.getLeaveEmpList(leaveDate, empStatus);
    }


    public List<EmpWorkInfoDo> getEmpListByWorkNos(List<String> workNos) {
        return empWorkInfoDo.getEmpListByWorkNos(workNos);
    }

    public PageResult<Map<String, String>> queryEmpColumnsPage(EmpSearchColumnsDto empSearchColumnsDto) {
        return empWorkInfoDo.queryEmpColumnsPage(empSearchColumnsDto);
    }

    public EmpWorkInfoDo getEmpWorkInfoByExt(Map<String, String> extMap) {
        List<EmpWorkInfoDo> empWorkInfoDos = empWorkInfoDo.getEmpListByExt(extMap, System.currentTimeMillis());
        return CollectionUtils.isNotEmpty(empWorkInfoDos) ? empWorkInfoDos.get(0) : null;
    }

    public List<EmpWorkInfoDo> getEmpListByEmpIds(List<String> empIds, long dateTime) {
        return empWorkInfoDo.getEmpListByEmpIds(empIds, dateTime);
    }

    public List<EmpWorkInfoDo> getNoLeaveEmpListByEmpIds(List<String> empIds, long dateTime) {
        return empWorkInfoDo.getNoLeaveEmpListByEmpIds(empIds, dateTime);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpReportLeaderDto(EmpReportLeaderDto empReportLeaderDto, long dateTime) {
        return empWorkInfoDo.getEmpWorkInfoByEmpReportLeaderDto(empReportLeaderDto, dateTime);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpConcurrentPostLeaderDto(EmpConcurrentPostLeaderDto empConcurrentPostDto, long dateTime) {
        return empWorkInfoDo.getEmpWorkInfoByEmpConcurrentPostLeaderDto(empConcurrentPostDto, dateTime);
    }

    public void updateContractCompany(String company, String companyTxt, String empId, Long dataTime) {
        if (StringUtil.isEmpty(company) || StringUtil.isEmpty(companyTxt) || StringUtil.isEmpty(empId)) {
            return;
        }

        EmpWorkInfoDo workInfo = empWorkInfoDo.getEmpWorkInfo(empId, dataTime);
        if (null == workInfo || StringUtil.isEmpty(workInfo.getBid())) {
            return;
        }

        updateContractCompany(workInfo, company, companyTxt, workInfo.getProbation(), workInfo.getProbationPeriodEndDate(), dataTime);
    }

    public void updateContractCompany(EmpWorkInfoDo workInfo, String company, String companyTxt, EnumSimple probation, Long probationPeriodEndDate, Long dataTime) {
        // 如果合同公司不相同，则进行更新
        if (Objects.equals(company, workInfo.getCompany())
                && Objects.equals(companyTxt, workInfo.getCompanyTxt())) {
            return;
        }

        // 试用期期限、试用期截止日期
        if (FunUtil.valueNotNull(probation, EnumSimple::getValue)) {
            workInfo.setProbation(probation);
        }
        if (FunUtil.isNotNull(probationPeriodEndDate)) {
            workInfo.setProbationPeriodEndDate(probationPeriodEndDate);
        }

        workInfo.setCompany(company);
        workInfo.setCompanyTxt(companyTxt);
        workInfo.setDataStartTime(dataTime);
        empWorkInfoDo.update(workInfo);
    }

    public void saveContractRelation(String empId, String bid) {
        empWorkInfoDo.saveContractRelation(empId, bid);
    }

    public List<EmpWorkInfoDo> getEmpListByPosts(Long dateTime, List<String> postIds) {
        return empWorkInfoDo.getEmpListByPosts(dateTime, postIds);
    }

    public List<EmpWorkInfoDo> getNonLeaveEmpPage(long dateTime, BasePage basePage) {
        return empWorkInfoDo.getNonLeaveEmpPage(dateTime, basePage);
    }

    public List<EmpWorkInfoDo> selectByLikeCostId(String costId, Long dateTime) {
        return empWorkInfoDo.selectByLikeCostCenter(dateTime, costId);
    }

    public List<EmpWorkInfoDo> selectListByJob(String jobId) {
        return empWorkInfoDo.selectListByJob(System.currentTimeMillis(), jobId);
    }

    public List<EmpWorkInfoDo> getEmpListByOrgId(String orgBid, Long dateTime) {
        return empWorkInfoDo.getEmpListByOrgId(orgBid, dateTime);
    }

    public List<OrgStructureEmpDto> selectEmpCardInfoByOrgId(String orgId, EmpStatusEnum status, long datetime) {
        return empWorkInfoRepository.selectEmpCardInfo(orgId, status, datetime);
    }

    /**
     * 记录成长记录信息
     */
    public void doProcessGrowthRecord(NestPropertyValue properties, NestPropertyValue oldProperties, GrowthRecordDto growthRecordDto) {
        growthRecordDto.setBusinessEventType(BusinessEventTypeEnum.EDIT.toString());
        ArrayList<GrowthRecordDto.DataItem> dataList = new ArrayList<>();

        //记录数据变化情况
        properties.entrySet().stream().forEach(entry -> {
            GrowthRecordDto.DataItem dataItem = new GrowthRecordDto.DataItem();
            String propKey = entry.getKey();
            dataItem.setProp(propKey);
            PropertyValue pValue = properties.get(propKey);
            PropertyValue oValue = oldProperties.get(propKey);

            if (pValue instanceof SimplePropertyValue
                    || oValue instanceof SimplePropertyValue) {
                if (null != oValue) {
                    dataItem.setValue(((SimplePropertyValue) oValue).getValue());
                }
                if (null != pValue) {
                    dataItem.setNewValue(((SimplePropertyValue) pValue).getValue());
                }
            } else if (pValue instanceof DictSimple
                    || oValue instanceof DictSimple) {
                if (null != oValue) {
                    dataItem.setValue(((DictSimple) oValue).getValue());
                }
                if (null != pValue) {
                    dataItem.setNewValue(((DictSimple) pValue).getValue());
                }
            } else if (pValue instanceof EnumSimple
                    || oValue instanceof EnumSimple) {
                if (null != oValue) {
                    dataItem.setValue(((EnumSimple) oValue).getValue());
                }
                if (null != pValue) {
                    dataItem.setNewValue(((EnumSimple) pValue).getValue());
                }
            } else if (pValue instanceof Address
                    || oValue instanceof Address) {
                if (null != oValue) {
                    dataItem.setValue(((Address) oValue).doText());
                }
                if (null != pValue) {
                    dataItem.setNewValue(((Address) pValue).doText());
                }
            } else if (pValue instanceof EmpSimple
                    || oValue instanceof EmpSimple) {
                if (null != oValue) {
                    dataItem.setValue(((EmpSimple) oValue).getEmpId());
                }
                if (null != pValue) {
                    dataItem.setNewValue(((EmpSimple) pValue).getEmpId());
                }
            } else if (pValue instanceof JobGradeRange
                    || oValue instanceof JobGradeRange) {
                if (null != oValue) {
                    dataItem.setValue(((JobGradeRange) oValue).getStartGrade());
                }
                if (null != pValue) {
                    dataItem.setNewValue(((JobGradeRange) pValue).getStartGrade());
                }
            } else if (pValue instanceof ComponentPropertyValue
                    || oValue instanceof ComponentPropertyValue) {
                log.warn("propKey:{},propType:{},value:{}", propKey, pValue.getClass(), pValue);
            } else {
                log.warn("[other type component]propKey:{},value:{}", propKey, pValue);
            }
            dataList.add(dataItem);
        });

        growthRecordDto.setDataList(dataList);
        growthRecordPublish.publish(growthRecordDto);
    }

    public void doProcessGrowthRecord(EmpWorkInfoDo beforeEmpWorkInfo, EmpWorkInfoDo afterEmpWorkInfo) {
        NestPropertyValue properties = afterEmpWorkInfo.getProperties();
        NestPropertyValue oldProperties = beforeEmpWorkInfo.getProperties();
        GrowthRecordDto growthRecordDto = new GrowthRecordDto();
        growthRecordDto.setTenantId(UserContext.getTenantId());
        growthRecordDto.setEmpId(afterEmpWorkInfo.getEmpId());
        growthRecordDto.setCreateBy(afterEmpWorkInfo.getCreateBy());
        growthRecordDto.setEffectiveDate(afterEmpWorkInfo.getDataStartTime());
        doProcessGrowthRecord(properties, oldProperties, growthRecordDto);
    }

    public List<EmpWorkInfoDo> gerEmpWorkInfoRange(String empId, Long startTime, Long endTime) {
        return empWorkInfoDo.gerEmpWorkInfoRange(empId, startTime, endTime);
    }

    public List<EmpWorkInfoDo> getEmpWorkInfoAtWork(List<String> empIds) {
        return empWorkInfoDo.getEmpWorkInfoAtWork(empIds);
    }

    public List<EmpWorkInfoDo> getTimeLine(String empId) {
        return empWorkInfoDo.getEmpTimeLineListByEmpId(empId);
    }
}
