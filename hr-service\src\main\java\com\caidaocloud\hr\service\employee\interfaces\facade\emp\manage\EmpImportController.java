package com.caidaocloud.hr.service.employee.interfaces.facade.emp.manage;

import com.caidaocloud.dto.importdto.ImportExcelDto;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.dataimport.DataImportService;
import com.caidaocloud.hr.service.employee.application.dataimport.service.TmpEmpEmailImportService;
import com.caidaocloud.hr.service.employee.application.dataimport.service.TmpEmpPostImportService;
import com.caidaocloud.hr.service.employee.application.dataimport.service.TmpEmpStatusImportService;
import com.caidaocloud.hr.service.employee.application.dataimport.service.TmpOrgImportService;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.vo.ImportExcelVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/hr/common/import/v1")
@Api(value = "/api/hr/common/import/v1", description = "公用导入", tags = "v1.9")
public class EmpImportController {

    @Autowired
    private List<DataImportService> dataImportServices;

    @Autowired
    private TmpEmpEmailImportService tmpEmpEmailImportService;
    @Autowired
    private TmpEmpStatusImportService tmpEmpStatusImportService;
    @Autowired
    private TmpEmpPostImportService empPostImportService;
    @Autowired
    private TmpOrgImportService orgImportService;


    @ApiOperation(value = "Excel导入")
    @PostMapping(value = "/importData")
    @LogRecordAnnotation(menu = "{importName{#dto.excelCode}}", category = "导入", success = "导入了合同信息",
            condition = "{{T(org.apache.commons.lang3.StringUtils).isNotEmpty('{importName{#dto.excelCode}}')}}")
    public Result<ImportExcelVo> importData(ImportExcelDto dto) throws IOException {
        DataImportService service = getDataImportServiceByCode(dto.getExcelCode());
        String tenantId = UserContext.getTenantId();
        String userId = UserContext.getUserId();
        ImportExcelVo vo = service.importDataWithExcel(dto);
        service.prepareOperateDataFromInputStream(dto.getFile()
                .getInputStream(), vo.getProcessUUid(), tenantId, Long.valueOf(userId));
        return Result.ok(vo);
    }

    @ApiOperation(value = "根据导入id查询进度信息")
    @GetMapping("/getImportPercentage")
    public Result<ImportExcelProcessVo> getImportPercentage(@RequestParam("processId") String processId, @RequestParam("excelCode") String excelCode) {
        DataImportService service = getDataImportServiceByCode(excelCode);
        return Result.ok(service.getImportDataPercentage(processId));
    }

    private DataImportService getDataImportServiceByCode(String excelCode) {
        for (DataImportService importService : dataImportServices) {
            if (importService.matchExcelCode(excelCode)) {
                return importService;
            }
        }
        throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30042) + "：" + excelCode + LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30043));
    }

    @ApiOperation(value = "根据导入id下载错误数据", produces = "application/octet-stream")
    @GetMapping("/downloadErrorImportInfo")
    public void downloadErrorImportInfo(HttpServletResponse response, @RequestParam(value = "processId") String processId, @RequestParam("excelCode") String excelCode) {
        DataImportService service = getDataImportServiceByCode(excelCode);
        service.downloadErrorImportData(response, processId);
    }

    @ApiOperation("员工公司邮箱批量更新")
    @PostMapping("/company/email")
    public Result tmpImportCompanyEmail(@RequestParam("file") MultipartFile multipartFile) {
        tmpEmpEmailImportService.importCompanyEmail(multipartFile);
        return Result.ok(true);
    }

    @ApiOperation("员工状态批量更新")
    @PostMapping("/emp/status")
    public Result tmpImportEmpStatus(@RequestParam("file") MultipartFile multipartFile) {
        tmpEmpStatusImportService.updateStatus(multipartFile);
        return Result.ok(true);
    }

    @ApiOperation("员工组织岗位批量更新")
    @PostMapping("/emp/post")
    public Result tmpImportEmpPost(@RequestParam("file") MultipartFile multipartFile) {
        empPostImportService.updatePost(multipartFile);
        return Result.ok(true);
    }

    @ApiOperation("组织临时导入")
    @PostMapping("/org")
    public Result tmpImportOrg(@RequestParam("file") MultipartFile multipartFile) {
        orgImportService.importOrg(multipartFile);
        return Result.ok(true);
    }
}
