package com.caidaocloud.hr.service.organization.interfaces.dto.post;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.Attachment;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.util.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/12/3
 */
@Data
public class PostDto {
    @ApiModelProperty("岗位ID")
    private String bid;

    @ApiModelProperty("岗位名称")
    private String name;

    @ApiModelProperty("岗位名称多语言")
    private Map<String, Object> i18nName;

    @ApiModelProperty("岗位编码")
    private String code;

    @ApiModelProperty("所属组织ID")
    private String orgId;

    @ApiModelProperty("所属组织名称")
    private String orgName;

    @ApiModelProperty("工作地ID")
    private String workplaceId;

    @ApiModelProperty("工作地名称")
    private String workplaceName;

    @ApiModelProperty("是否关键岗位")
    private Boolean keyPost;

    @ApiModelProperty("职级关联")
    private JobGradeRange jobGrade;

    @ApiModelProperty("关联职务或基准岗位，0：未配置，1：职务，2：基准岗位")
    private Integer relation;

    @ApiModelProperty("关联基准岗位ID")
    private String benchmarkPositionId;

    @ApiModelProperty("关联基准岗位名称")
    private String benchmarkPositionName;

    @ApiModelProperty("关联的职务ID")
    private String jobId;

    @ApiModelProperty("关联的职务名称")
    private String jobName;

    @ApiModelProperty("岗位说明")
    private String desc;

    @ApiModelProperty("岗位说明书")
    private Attachment jobDescFiles;

    @ApiModelProperty("任职资格")
    private String qualification;

    @ApiModelProperty("是否更新该岗位下的员工工作地信息")
    private Boolean syncEmpPost;

    @ApiModelProperty("附件")
    private Attachment files;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("生效日期（单位毫秒）")
    private long dataStartTime;

    public void check(){
        PreCheck.preCheckArgument(StringUtil.isBlank(name), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30053));
//        PreCheck.preCheckArgument(StringUtil.isBlank(code), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30054));
        PreCheck.preCheckArgument(StringUtil.isBlank(orgId), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30055));
        PreCheck.preCheckArgument(desc != null && desc.length() > 200, LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_32030));
    }

    public PostDto peekPostDto() {
        setName(StringUtils.isEmpty(getName()) ? LangUtil.getDefaultValue(getI18nName()) :
                getName());
        return this;
    }
}
