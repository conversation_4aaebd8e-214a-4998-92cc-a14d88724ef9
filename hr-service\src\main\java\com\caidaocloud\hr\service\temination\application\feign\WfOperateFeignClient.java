package com.caidaocloud.hr.service.temination.application.feign;

import com.caidaocloud.hr.service.temination.application.dto.WfTaskApproveDTO;
import com.caidaocloud.hr.service.temination.application.dto.WfTaskBackDTO;
import com.caidaocloud.hr.service.temination.application.dto.WfTaskRevokeDTO;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfMetaFunNameDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 工作流流程操作
 */
@FeignClient(
        value = "caidaocloud-workflow-service-v2",
        fallback = WfOperateFeignFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "wfOperateFeignClient"
)
public interface WfOperateFeignClient {
    /**
     * 任务审批
     *
     * @param wfApproveTaskDTO
     * @return
     */
    @PostMapping("api/workflow/v2/operate/task/approve")
    Result<?> approveTask(@RequestBody WfTaskApproveDTO wfApproveTaskDTO);

    /**
     * 任务驳回
     *
     * @param wfTaskBackDTO
     * @return
     */
    @PostMapping("api/workflow/v2/operate/task/back")
    Result backTask(@RequestBody WfTaskBackDTO wfTaskBackDTO);

    /**
     * 任务流程撤回
     *
     * @param wfTaskRevokeDTO
     * @return
     */
    @PostMapping("api/workflow/v2/operate/task/revoke")
    Result<String> revokeProcessOfTask(@RequestBody WfTaskRevokeDTO wfTaskRevokeDTO);

    /**
     * wf_meta_fun
     * @param wfMetaFunNameDto
     * @return
     */
    @PostMapping("api/workflow/v2/meta/name/function/update")
    Result updateFunctionName(@RequestBody WfMetaFunNameDto wfMetaFunNameDto);

    /**
     * 修改 wf_hi_process
     * @param wfMetaFunNameDto
     * @return
     */
    @PostMapping("api/workflow/v2/view/name/function/update")
    Result updateFunName(@RequestBody WfMetaFunNameDto wfMetaFunNameDto);

//    /**
//     * 流程任务第三方业务状态更新
//     *
//     * @param wfTaskThirdStatusDTO
//     * @return
//     */
//    @PostMapping("api/workflow/v2/operate/task/third/status")
//    Result updateThirdStatus(@RequestBody WfTaskThirdStatusDTO wfTaskThirdStatusDTO);
//
//    /**
//     * 获取任务节点配置
//     *
//     * @param taskId 任务节点id
//     * @return
//     */
//    @GetMapping("api/workflow/v2/config/current/node")
//    Result<WfDefNodeUserTaskDTO> getConfigOfCurrentNodeByTaskId(@RequestParam("taskId") String taskId);
//
//    /**
//     * 获取流程发起人和申请人
//     *
//     * @param businessKey 流程业务key
//     * @return
//     */
//    @GetMapping("api/workflow/v2/business/getInitiatorAndApplicant")
//    Result<WfInitiatorAndApplicantVo> getInitiatorAndApplicant(@RequestParam("businessKey") String businessKey);
//
//    /**
//     * 检查流程是否已启用
//     *
//     * @param funCode
//     * @return
//     */
//    @GetMapping("api/workflow/v2/config/def/checkEnabled")
//    Result<Boolean> checkDefEnabled(@RequestParam("funCode") String funCode);
}
