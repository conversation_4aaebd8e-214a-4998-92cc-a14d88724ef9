package com.caidaocloud.hr.service.employee.application.workOverview.service;

import com.caidaocloud.hr.service.dto.WorkExperienceDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.domain.workExperience.entity.WorkExperienceDo;
import com.caidaocloud.hr.service.employee.domain.workExperience.service.WorkExperienceDomainService;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class WorkExperienceService {
    @Resource
    private WorkExperienceDomainService workExperienceDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;


    public String saveWorkExperience(WorkExperienceDo data) {
        if (StringUtils.isNotEmpty(data.getBid())) {
            workExperienceDomainService.update(data);
            return data.getBid();
        }else {
            return workExperienceDomainService.save(data);
        }
    }

    public String saveWorkExperienceByDto(WorkExperienceDto dto) {
        WorkExperienceDo data = ObjectConverter.convert(dto, WorkExperienceDo.class);
        empExtFieldService.doCusExtProps(data.getDoIdentifier(), dto.getExt(), data);
        if (StringUtils.isNotEmpty(data.getBid())) {
            LogRecordContext.putVariable("operate", "编辑");
            workExperienceDomainService.update(data);
            return data.getBid();
        }else {
            LogRecordContext.putVariable("operate", "新增");
            return workExperienceDomainService.save(data);
        }
    }

    public void deleteByBid(String bid) {
        WorkExperienceDo data = new WorkExperienceDo();
        data.setBid(bid);
        workExperienceDomainService.delete(data);
    }

    public List<WorkExperienceDo> selectList(String empId) {
        List<WorkExperienceDo> workExperienceDos = workExperienceDomainService.selectList(empId);
        if (CollectionUtils.isEmpty(workExperienceDos)) {
            return Lists.newArrayList();
        }
        workExperienceDos = workExperienceDos
                .stream()
                .sorted(Comparator.comparing(e -> e.getStartDate() == null ? 0 : e.getStartDate(), Comparator.reverseOrder()))
                .collect(Collectors.toList());
        return workExperienceDos;
    }

    public WorkExperienceDo selectById(String bid) {
        return workExperienceDomainService.selectById(bid);
    }
}
