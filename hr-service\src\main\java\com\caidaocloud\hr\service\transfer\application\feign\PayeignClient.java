package com.caidaocloud.hr.service.transfer.application.feign;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.hr.service.transfer.application.dto.inport.ImportFunctionDto;
import com.caidaocloud.hr.service.transfer.application.feign.impl.ImportFeignClientImpl;
import com.caidaocloud.hr.service.transfer.application.feign.impl.PayFeignClientImpl;
import com.caidaocloud.hr.service.transfer.application.feign.vo.PayFixItemByUpdateTimeVo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 报酬服务feign
 *
 */
@FeignClient(value = "caidaocloud-payroll-service", fallback = PayFeignClientImpl.class, configuration = FeignConfiguration.class)
public interface PayeignClient {

    /**
     * 保存固定薪资项目-用于组件
     *
     * @return
     */
    @PostMapping(value = "/api/payroll/fix/v1/saveForAss")
    Result savePayFixInfoAss(@RequestBody List<PayFixItemByUpdateTimeVo> vos) ;

//    @GetMapping("/api/payroll/social/v1/getNameById")
//    Result getPrivacyInfoList(@RequestParam("id") Integer id);
//
//    @GetMapping("/api/payroll/fundpolicy/v1/getHfPolicyName")
//    Result getHfPolicy(@RequestParam("id") Integer id);

    
      @PostMapping("/api/payroll/fundpolicy/v1/getHfPolicyList")
      Result getHfPlicyList(@RequestBody BasePage dto);
      
      @PostMapping("/api/payroll/social/v1/getSocialSecurityPolicies")
      Result getSocialSecurityPolicies(@RequestBody BasePage dto);
      
      

}
