package com.caidaocloud.hr.service.transfer.application.feign;

import com.caidaocloud.hr.service.transfer.application.dto.inport.ImportFunctionDto;
import com.caidaocloud.hr.service.transfer.application.feign.impl.ImportFeignClientImpl;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 导入服务feign
 *
 * <AUTHOR>
 */
@FeignClient(value = "caidaocloud-import-export-service", fallback = ImportFeignClientImpl.class, configuration = FeignConfiguration.class)
public interface ImportFeignClient {

    /**
     * 新增导入
     *
     * @param functionDto
     * @return
     */
    @PostMapping("/api/import/v1/config/function")
    Result<Boolean> createResources(@RequestBody ImportFunctionDto functionDto);
}
