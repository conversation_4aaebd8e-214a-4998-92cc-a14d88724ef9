package com.caidaocloud.hr.service.organization.infrastructure.repository.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.enums.org.EnterpriseStatusEnum;
import com.caidaocloud.hr.service.organization.domain.company.entity.CompanyDo;
import com.caidaocloud.hr.service.organization.domain.company.entity.PayUnitDo;
import com.caidaocloud.hr.service.organization.domain.company.repository.ICompanyRepository;
import com.caidaocloud.hr.service.organization.domain.company.repository.IPayUnitRepository;
import com.caidaocloud.hr.service.organization.infrastructure.repository.po.CompanyPo;
import com.caidaocloud.hr.service.organization.infrastructure.repository.po.PayUnitPo;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.CompanyQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.company.PayUnitQueryDto;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同公司
 *
 * <AUTHOR>
 * @Date 2021/11/24
 */
@Repository
@Slf4j
public class PayUnitRepositoryImpl implements IPayUnitRepository {
    @Override
    public PayUnitDo insert(PayUnitDo data) {
        String bid = DataInsert.identifier(data.getIdentifier()).insert(ObjectConverter.convert(data, PayUnitPo.class));
        data.setBid(bid);
        return data;
    }

    @Override
    public int updateById(PayUnitDo data) {
        DataUpdate.identifier(data.getIdentifier()).update(ObjectConverter.convert(data, PayUnitPo.class));
        return 0;
    }

    @Override
    public PayUnitDo selectById(String id, String identifier) {
        PayUnitDo payUnitDo = ObjectConverter.convert(DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().oneOrNull(id, PayUnitPo.class), PayUnitDo.class);
        return payUnitDo;
    }

    @Override
    public int delete(PayUnitDo data) {
        DataDelete.identifier(data.getIdentifier()).softDelete(data.getBid());
        return 0;
    }

    @Override
    public PageResult<PayUnitDo> selectPage(BasePage page, PayUnitDo data) {
        DataFilter filter = DataFilter.eq("tenantId", data.getTenantId()).andNe("deleted", Boolean.TRUE.toString());
        PayUnitQueryDto payUnitQueryDto = (PayUnitQueryDto) page;

        //名称/编码 单信息检索；
        if (StringUtil.isNotEmpty(payUnitQueryDto.getUnitNameOrCode())) {
            filter = filter.and(DataFilter.regex("unitName", payUnitQueryDto.getUnitNameOrCode()).or(DataFilter.regex("unitCode", payUnitQueryDto.getUnitNameOrCode())));
        }

        filter = (DataFilter) page.doDataFilter(page.getFilters(), filter);
        
        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(page.getPageSize(), page.getPageNo()).filter(filter, PayUnitDo.class, page.getOrder(),System.currentTimeMillis());
    }

    @Override
    public List<PayUnitDo> selectList(PayUnitDo data) {
        return ObjectConverter.convertList(DataQuery.identifier(data.getIdentifier())
                .decrypt().specifyLanguage().queryInvisible().limit(5000, 1)
                .filter(DataFilter.eq("tenantId", data.getTenantId())
                        .andNe("deleted", Boolean.TRUE.toString()), PayUnitPo.class)
                .getItems(), PayUnitDo.class);
    }

    @Override
    public List<PayUnitDo> selectBatchIds(List<String> ids, String identifier, String tenantId) {
        return ObjectConverter.convertList(DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().
                filter(DataFilter.eq("tenantId", tenantId).andIn("bid", ids), CompanyPo.class).getItems(), PayUnitDo.class);
    }

    @Override
    public List<PayUnitDo> selectListAll(PayUnitDo data) {
        List<PayUnitDo> list = new ArrayList<>();
        int pageNo = 1;
        int pageSize = 5000;
        boolean flag = true;
        int index = pageSize;
        while (flag) {
            PageResult<PayUnitDo> page = DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                    .limit(pageSize, pageNo).filter(DataFilter.eq("tenantId", data.getTenantId()).andNe("deleted", Boolean.TRUE.toString()), PayUnitDo.class);
            list.addAll(page.getItems());
            if (page.getTotal() > index) {
                pageNo++;
                index += pageSize;
            } else {
                flag = false;
            }
        }
        return list;
    }

    @Override
    public List<PayUnitDo> selectByCode(PayUnitDo data) {
        DataFilter filter = DataFilter.eq("tenantId", data.getTenantId())
                .andEq("deleted", Boolean.FALSE.toString())
                .andEq("unitCode", data.getUnitCode());

        if (StringUtil.isNotEmpty(data.getBid())) {
            filter = filter.andNe("bid", data.getBid());
        }

        PageResult<PayUnitDo> page = DataQuery.identifier(data.getIdentifier())
                .decrypt().specifyLanguage().queryInvisible()
                .limit(1, 1)
                .filter(filter, PayUnitDo.class);
        return page.getItems();
    }

    @Override
    public List<PayUnitDo> selectByCodes(List<String> codes, String identifier, String tenantId) {
        DataFilter filter = DataFilter.eq("tenantId", identifier)
                .andEq("deleted", Boolean.FALSE.toString());
        if (CollectionUtils.isNotEmpty(codes)) {
            filter = filter.andIn("unitCode", codes);
        }
        PageResult<PayUnitDo> page = DataQuery.identifier(identifier)
                .decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1)
                .filter(filter, PayUnitDo.class);
        return page.getItems();
    }
}
