package com.caidaocloud.hr.service.employee.interfaces.facade.emp.manage;

import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpBasicInfoService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpBasicInfoDo;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpALlInfoVo;
import com.caidaocloud.hr.service.vo.EmpBasicInfoVo;
import com.caidaocloud.hr.service.vo.EmpInfoVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/api/hr/emp/basic/v1")
@Api(value = "/api/hr/emp/basic/v1", description = "员工基本信息", tags = "v0.3")
public class EmpBasicInfoController {
    @Resource
    private EmpBasicInfoService empBasicInfoService;

    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    @ApiOperation("获取员工基本信息")
    @GetMapping("/detail")
    public Result<EmpBasicInfoVo> getEmpWorkInfo(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime) {
        EmpBasicInfoDo empBasicInfo = empBasicInfoService.getEmpBasicInfo(empId, dataTime);
        if (Objects.isNull(empBasicInfo)) {
            return Result.ok(new EmpBasicInfoVo());
        }
        EmpBasicInfoVo dataVo = ObjectConverter.convert(empBasicInfo, EmpBasicInfoVo.class);
        if (null != dataVo) {
            empBasicInfo.extendedField(dataVo.getExt(), new BeanMap(empBasicInfo));
            dataVo.setEmpId(empBasicInfo.getBid());
        }
        if(!"enabled".equals(postTxtShowCode)){
            String postTxt = dataVo.getPostTxt();
            if(StringUtils.isNotEmpty(postTxt) && postTxt.indexOf("(") >= 0){
                postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                dataVo.setPostTxt(postTxt);
            }
        }

        return Result.ok(dataVo);
    }

    /**
     * 获取员工基本信息，任职信息，个人信息
     *
     * @param empId
     * @param dataTime
     * @return
     */
    @ApiOperation("获取员工基本信息")
    @GetMapping("/info")
    public Result<EmpInfoVo> getEmpInfo(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime) {
        return Result.ok(empBasicInfoService.getEmpInfo(empId, dataTime));
    }

    /**
     * 获取员工基本信息，任职信息，个人信息
     *
     * @param empId
     * @param dataTime
     * @return
     */
    @ApiOperation("获取员工基本信息")
    @GetMapping("/info/simple")
    public Result<Map> getEmpInfoSimple(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime) {
        return Result.ok(empBasicInfoService.getEmpInfoSimple(empId, dataTime));
    }

    @PostMapping("/info/simple/list")
    public Result<List<Map>> listEmpInfoSimple(@RequestBody List<String> empIds, @RequestParam("dataTime") Long dataTime) {
        return Result.ok(empBasicInfoService.listEmpInfoSimple(empIds, dataTime));
    }

    /**
     * 获取员工基本信息，任职信息，个人信息
     * 按自定义基础信息数据结构返回
     *
     * @param empId
     * @param dataTime
     * @return
     */
    @ApiOperation("获取员工基本信息--所有模型")
    @GetMapping("/all")
    public Result<EmpALlInfoVo> getEmpInfoMap(@RequestParam("empId") String empId, @RequestParam("dataTime") Long dataTime) {
        return Result.ok(empBasicInfoService.getEmpInfoAll(empId, dataTime));
    }
}
