package com.caidaocloud.hr.service.employee.infrastructure.emp.repository.impl;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.contract.interfaces.dto.ContractQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpWorkInfoRepository;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpConcurrentPostLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.fieldset.EmpReportLeaderDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpSearchColumnsDto;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.organization.application.org.dto.OrgStructureEmpDto;
import com.caidaocloud.hr.service.transfer.infrastructure.utils.DataSimpleUtil;
import com.caidaocloud.hr.service.util.BeanUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.AbstractData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.LabelData;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.util.JsonEnhanceUtil;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class EmpWorkInfoRepositoryImpl extends BaseRepositoryImpl<EmpWorkInfoDo> implements IEmpWorkInfoRepository {
    @Override
    public EmpWorkInfoDo selectByEmpId(String empId, String identifier, Long dateTime) {
        List<EmpWorkInfoDo> dbList = DataQuery.identifier(identifier).decrypt().dept().specifyLanguage()
                .queryInvisible()
                .exp()
                // .queryRelatedProperties("lastContract.bid")
                .filter(DataFilter.eq("empId", empId)
                        .andNe("deleted", Boolean.TRUE.toString()), EmpWorkInfoDo.class, dateTime).getItems();

        return null == dbList || dbList.isEmpty() ? null : dbList.get(0);
    }

    @Override
    public List<EmpWorkInfoDo> getEmpMainPostForLeader(String leaderEmpId, String identifier, Long dateTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andEq("empStatus", EmpStatusEnum.IN_JOB.getIndex().toString())
                .andNe("deleted", Boolean.TRUE.toString()).andEq("leadEmpId$empId", leaderEmpId);
        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().
                filter(dataFilter, EmpWorkInfoDo.class, dateTime).getItems();
    }

    @Override
    public List<EmpWorkInfoDo> selectByEmpIdHistory(String empId, String identifier, Long dateTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId()).
                andNe("deleted", Boolean.TRUE.toString()).andEq("empId", empId);

        if (dateTime != null) {
            dataFilter = dataFilter.andLt("dataEndTime", String.valueOf(dateTime));
        }

        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().
                filter(dataFilter, EmpWorkInfoDo.class).getItems();
    }

    @Override
    public EmpWorkInfoDo selectByWorkno(String workno, Long dateTime, String identifier) {
        List<EmpWorkInfoDo> dbList = DataQuery.identifier(identifier).decrypt().dept().specifyLanguage()
                .queryInvisible()
                .exp()
                .filter(DataFilter.eq("workno", workno)
                        .andNe("deleted", Boolean.TRUE.toString()), EmpWorkInfoDo.class, dateTime).getItems();

        return null == dbList || dbList.isEmpty() ? null : dbList.get(0);
    }

    @Override
    public List<EmpWorkInfoDo> selectGeStartTime(EmpWorkInfoDo data, Long dateTime) {
        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible().filter(
                DataFilter.eq("tenantId", data.getTenantId()).andNe("deleted", Boolean.TRUE.toString())
                        .andGt("data_start_time", dateTime.toString()), EmpWorkInfoDo.class).getItems();
    }

    @Override
    public List<EmpWorkInfoDo> selectByTime(Long dataTime) {
        int pageSize = 500;
        int pageNo = 1;
        int total = 0;
        List<EmpWorkInfoDo> result = new ArrayList<>();
        do {
            PageResult<EmpWorkInfoDo> empWorkInfoDoPageResult = DataQuery.identifier(EmpWorkInfoDo.IDENTIFIER)
                    .decrypt().specifyLanguage().queryInvisible()
                    .limit(pageSize, pageNo)
                    .filter(DataFilter.eq("tenantId", SecurityUserUtil.getSecurityUserInfo().getTenantId())
                            .andNe("deleted", Boolean.TRUE.toString())
                            .andEq("empStatus", EmpStatusEnum.IN_JOB.getIndex().toString()), EmpWorkInfoDo.class, dataTime);
            total = empWorkInfoDoPageResult.getTotal();
            result.addAll(empWorkInfoDoPageResult.getItems());
        } while (pageNo++ * pageSize < total);
        return result;
    }

    @Override
    public List<EmpWorkInfoDo> selectListByPost(EmpWorkInfoDo data, Long dateTime) {
        PageResult<EmpWorkInfoDo> pageResult = DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage()
                .queryInvisible().filter(DataFilter.eq("post", data.getPost())
                        .andNe("deleted", Boolean.TRUE.toString()), EmpWorkInfoDo.class, dateTime);
        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }

    @Override
    public List<EmpWorkInfoDo> selectListByCompany(EmpWorkInfoDo data, Long dateTime) {
        PageResult<EmpWorkInfoDo> pageResult = DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage()
                .queryInvisible().filter(DataFilter.eq("company", data.getTenantId())
                        .andNe("deleted", Boolean.TRUE.toString()), EmpWorkInfoDo.class, dateTime);
        return null != pageResult ? pageResult.getItems() : Lists.newArrayList();
    }

    @Override
    public List<EmpWorkInfoDo> selectList(EmpWorkInfoDo data) {
        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible().
                filter(DataFilter.eq("tenantId", data.getTenantId()).andEq("empId", data.getEmpId()).
                        andNe("deleted", Boolean.TRUE.toString()), EmpWorkInfoDo.class).getItems();
    }
    @Override
    public List<EmpWorkInfoDo> selectListNoDataTime(EmpWorkInfoDo data) {
        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible().limit(5000, 1).
                filter(DataFilter.eq("tenantId", data.getTenantId()).andEq("empId", data.getEmpId()).
                        andNe("deleted", Boolean.TRUE.toString()), EmpWorkInfoDo.class, -1).getItems();
    }

    @Override
    public PageResult<EmpWorkInfoDo> selectPage(BasePage page, EmpWorkInfoDo data, String keywords, Long dateTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", data.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());
        if (data.getOrganize() != null) {
            dataFilter = dataFilter.andEq("organize", data.getOrganize());
        }
        if (StringUtils.isNotEmpty(keywords)) {
            dataFilter = dataFilter.and(DataFilter.regex("workno", keywords).orRegex("name", keywords));
        }
        if (data.getLeaveDate() != null) {
            dataFilter = dataFilter.andEq("leaveDate", String.valueOf(data.getLeaveDate()));
        }
        if (data.getEmpStatus() != null) {
            dataFilter = dataFilter.andEq("empStatus", data.getEmpStatus().getValue());
        }
        // 增加筛选条件
        dataFilter = (DataFilter) page.doDataFilter(page.getFilters(), dataFilter);
        return DataQuery.identifier(data.getIdentifier()).decrypt().specifyLanguage().queryInvisible()
                .limit(page.getPageSize(), page.getPageNo()).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
    }

    @Override
    public List<EmpWorkInfoDo> getEmpListByCompanyEmail(String tenantId, String identifier, Long dateTime, List<String> companyEmailList) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId)
                .andNe("deleted", Boolean.TRUE.toString())
                .andIn("companyEmail", companyEmailList);

        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(dataResult);
    }

    @Override
    public List<EmpWorkInfoDo> getLeaveEmpList(String tenantId, String identifier, Long dateTime, Long leaveDate, Integer empStatus) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId)
                .andNe("deleted", Boolean.TRUE.toString())
                .andNe("empStatus", String.valueOf(empStatus))
                //表字段leaveDate的值小于等于我传的参数
                .andLe("leaveDate", String.valueOf(leaveDate));
        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(dataResult);
    }

    @Override
    public List<EmpWorkInfoDo> getByWorkno(String tenantId, String identifier, String bid, String workno, Long dateStartTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId)
                .andNe("bid", bid)
                .andEq("deleted", Boolean.FALSE.toString())
                .andEq("workno", workno);

        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateStartTime);
        return getPageList(dataResult);
    }

    @Override
    public List<EmpWorkInfoDo> getEmpListByEmpIds(String identifier, Long dateTime, List<String> empIds) {
        DataFilter dataFilter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andIn("empId", empIds);

        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(dataResult);
    }

    @Override
    public List<EmpWorkInfoDo> getNoLeaveEmpListByEmpIds(String identifier, Long dateTime, List<String> empIds) {
        DataFilter dataFilter = DataFilter.ne("deleted", Boolean.TRUE.toString())
                .andIn("empId", empIds).andNe("empStatus", String.valueOf(EmpStatusEnum.LEAVE_JOB.getIndex()));

        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(dataResult);
    }

    @Override
    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpReportLeaderDto(String identifier, Long dateTime, EmpReportLeaderDto empReportLeaderDto) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString())
                .andEq("leadEmpId$empId", empReportLeaderDto.getEmpId())
                .andEq("leaderOrganize", empReportLeaderDto.getOrganizeId())
                .andEq("leaderPost", empReportLeaderDto.getPostId())
                .andNe("empStatus", EmpStatusEnum.LEAVE_JOB.getIndex().toString())
                .andGt("dataEndTime", String.valueOf(empReportLeaderDto.getDateTime()));
        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(dataResult);
    }

    @Override
    public List<EmpWorkInfoDo> getEmpWorkInfoByEmpConcurrentPostLeaderDto(String identifier, Long dateTime, EmpConcurrentPostLeaderDto empConcurrentPostLeaderDto) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString())
                .andEq("leadEmpId$empId", empConcurrentPostLeaderDto.getEmpId())
                .andEq("leaderOrganize", empConcurrentPostLeaderDto.getOrganizeId())
                .andEq("leaderPost", empConcurrentPostLeaderDto.getPostId())
                .andNe("empStatus", EmpStatusEnum.LEAVE_JOB.getIndex().toString())
                //ge:大于等于
                .and(DataFilter.ge("dataStartTime", String.valueOf(empConcurrentPostLeaderDto.getStartTime())).and(DataFilter.ge("dataEndTime", String.valueOf(empConcurrentPostLeaderDto.getEndTime()))));

        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(dataResult);
    }

    @Override
    public List<EmpWorkInfoDo> getEmpListByExt(Map<String, String> extMap, String identifier, Long dateTime) {
        if (extMap != null && !extMap.isEmpty()) {
            DataFilter dataFilter = null;
            for (String key :
                    extMap.keySet()) {
                if (dataFilter == null) {
                    dataFilter = DataFilter.eq(key, extMap.get(key));
                } else {
                    dataFilter.and(DataFilter.eq(key, extMap.get(key)));
                }
            }
            PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                    .limit(5000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
            return getPageList(dataResult);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<EmpWorkInfoDo> getEmpDisable(String tenantId, String identifier, String organize, String post, Long dateTime) {

        // CAIDAOM-1787  不获取数量 改为 获取信息；  默认获取 6条
        PageResult<EmpWorkInfoDo> page = null;
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId)
                .andEq("deleted", Boolean.FALSE.toString());

        //获取对应组织
        if (organize != null) {
            dataFilter = dataFilter.andEq("organize", organize);
        }

        //对应岗位
        if (post != null) {
            dataFilter = dataFilter.andEq("post", post);
        }

        //caidao -1787 生效中/未来生效； end_time >= datetime;
        dataFilter = dataFilter.andGe("dataEndTime", String.valueOf(dateTime));

        //caidao-1787 排查 离职日期；
        // 离职日期 无 或者 在生效日期之后 需要排查；
        dataFilter = dataFilter.and(DataFilter.eq("leaveDate", null).orGt("leaveDate", String.valueOf(dateTime)));

        dataFilter = dataFilter.and(DataFilter.eq("empStatus", "0").orEq("empStatus", null));

        int pageNo = 1;
        int pageSize = 6;
//        boolean flag = true;
//        int index = 0;
        List<EmpWorkInfoDo> list = new ArrayList<>();
//        while (flag) {
        page = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().limit(pageSize, pageNo)
                .filter(dataFilter, EmpWorkInfoDo.class, -1);
//            index += pageSize;
//            ++pageNo;
//            if(page.getTotal()<=index){
//                flag = false;
//            }
        list.addAll(page.getItems());
//        }
        return list;
    }

    @Override
    public void saveContractRelation(EmpWorkInfoDo workInfo, String contractId) {
        DataUpdate.identifier(workInfo.getIdentifier())
                .replaceRelations("lastContract", Lists.newArrayList(contractId))
                .update(workInfo);
    }

    @Override
    public List<EmpWorkInfoDo> getEmpListByWorkNos(String tenantId, String identifier, Long dateTime, List<String> workNos) {
        DataFilter dataFilter = DataFilter.eq("tenantId", tenantId)
                .andNe("deleted", Boolean.TRUE.toString())
                .andIn("workno", workNos);

        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(5000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(dataResult);
    }

    @Override
    public PageResult<Map<String, String>> queryEmpColumnsPage(EmpSearchColumnsDto empSearchColumnsDto, String identifier) {
        PageResult<Map<String, String>> empPage = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(empSearchColumnsDto.getPageSize(), empSearchColumnsDto.getPageNo())
                .filterProperties(
                        DataFilter.eq("tenantId", UserContext.getTenantId())
                                .andNe("deleted", Boolean.TRUE.toString())
                                .andNe("empStatus", EmpStatusEnum.LEAVE_JOB.getIndex().toString()),
                        empSearchColumnsDto.getColumns(),
                        System.currentTimeMillis());

        return empPage;
    }

    @Override
    public List<EmpWorkInfoDo> getEmpListByPosts(String identifier, Long dateTime, List<String> postIds) {
        DataFilter dataFilter = DataFilter.eq("deleted", Boolean.FALSE.toString())
                .andIn("post", postIds);

        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(1000, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(dataResult);
    }

    @Override
    public List<EmpWorkInfoDo> getNonLeaveEmpPage(String identifier, Long dateTime, BasePage basePage) {
        DataFilter dataFilter = DataFilter.eq("deleted", Boolean.FALSE.toString())
                .andNe("empStatus", EmpStatusEnum.LEAVE_JOB.getIndex().toString());

        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .limit(basePage.getPageSize(), basePage.getPageNo()).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(dataResult);
    }

    @Override
    public PageResult<Map<String, String>> newlySignedList(String identifier, ContractQueryDto query, Long dateTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString()).andEq("lastContract.bid", null);
        if (StringUtils.isNotEmpty(query.getOrganize())) {
            dataFilter = dataFilter.andEq("organize", query.getOrganize());
        }
        if (StringUtils.isNotEmpty(query.getKeyword())) {
            dataFilter = dataFilter.and(DataFilter.regex("workno", query.getKeyword())
                    .orRegex("name", query.getKeyword()));
        }

        dataFilter = dataFilter.and(DataFilter.eq("leaveDate", null).orGt("leaveDate", String.valueOf(dateTime)));

        dataFilter = (DataFilter) query.doDataFilter(query.getFilters(), dataFilter);

        PageResult<EmpWorkInfoDo> result = DataQuery.identifier(identifier)
                .limit(query.getPageSize(), query.getPageNo()).
                filter(dataFilter, EmpWorkInfoDo.class, dateTime);

        log.info("newlySignedList result ={}", FastjsonUtil.toJson(result));
        // 转换为map
        PageResult<Map<String, String>> mapResult = new PageResult<>();
        BeanUtils.copyProperties(result, mapResult);
        List<Map<String, String>> list = Sequences.sequence(result.getItems()).map(entity -> {
            Map<String, String> map = JsonEnhanceUtil.toObject(entity, Map.class);
            map.put("empType.dict.text", entity.getEmpType().getText());
            map.put("empStatus", entity.getEmpStatus().getValue());
            map.put("hireDate", entity.getHireDate() == null ? "" : entity.getHireDate().toString());
            return map;
        }).toList();
        mapResult.setItems(list);
        return mapResult;
    }

    @Override
    public List<EmpWorkInfoDo> selectByLikeCostCenter(String identifier, Long dateTime, String costCenterId) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());

        if (StringUtils.isNotEmpty(costCenterId)) {
            dataFilter = dataFilter.andRegex("costCenters", "\"cost\":\"" + costCenterId + "\"");
        }
        PageResult<EmpWorkInfoDo> pageResult = DataQuery.identifier(identifier).limit(100, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(pageResult);
    }

    @Override
    public List<EmpWorkInfoDo> selectByJob(String identifier, Long dateTime, String jobId) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());

        if (StringUtils.isNotEmpty(jobId)) {
            dataFilter = dataFilter.andEq("job", jobId);
        }
        PageResult<EmpWorkInfoDo> pageResult = DataQuery.identifier(identifier).limit(100, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(pageResult);
    }

    @Override
    public List<EmpWorkInfoDo> getEmpListByOrgId(String identifier, Long dateTime, String orgBid) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString());

        if (StringUtils.isNotEmpty(orgBid)) {
            dataFilter = dataFilter.andEq("organize", orgBid);
        }
        PageResult<EmpWorkInfoDo> pageResult = DataQuery.identifier(identifier).limit(100, 1).filter(dataFilter, EmpWorkInfoDo.class, dateTime);
        return getPageList(pageResult);
    }

    @Override
    public List<OrgStructureEmpDto> selectEmpCardInfo(String orgId, EmpStatusEnum status, long datetime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString())
                .andEq("organize", orgId);
        if (status != null) {
            dataFilter = dataFilter.andEq("empStatus", status.getIndex().toString());
        }

        PageResult<Map<String, String>> result;
        List<Map<String, String>> list = new ArrayList<>();
        int pageNo = 1;
        do {
            result = DataQuery.identifier(EmpWorkInfoDo.IDENTIFIER).queryInvisible()
                    .decrypt().specifyLanguage()
                    .limit(MAX_PAGE_SIZE, pageNo)
                    .filterProperties(dataFilter, BeanUtil.getPropName(EmpWorkInfoDo.class, '.', "empId", "name", "workno", "organize", "photo", "post", "postTxt", "jobGrade", "empStatus"), datetime);
            list.addAll(result.getItems());
        }
        while (pageNo++ * MAX_PAGE_SIZE < result.getTotal());
        Map<String, String> jobGradeMap = DataQuery.allJobGrade().stream()
                .filter(l->!l.isDeleted())
                .collect(Collectors.toMap(AbstractData::getBid, LabelData::getLabel));
        Map<String, String> channelMap = DataQuery.allJobGradeChannel().stream()
                .filter(l->!l.isDeleted())
                .collect(Collectors.toMap(AbstractData::getBid, LabelData::getLabel));
        return Sequences.sequence(list).map(item -> {
            OrgStructureEmpDto dto = DataSimpleUtil.filterMap2Dto(item, OrgStructureEmpDto.class);
            dto.getPhoto().setUrls(FastjsonUtil.toList(item.get("photo.urls"), String.class));
            dto.getPhoto().setNames(FastjsonUtil.toList(item.get("photo.names"), String.class));
            dto.getJobGrade().setChannelName(channelMap.get(dto.getJobGrade().getChannel()));
            dto.getJobGrade().setStartGradeName(jobGradeMap.get(dto.getJobGrade().getStartGrade()));
            dto.getJobGrade().setEndGradeName(jobGradeMap.get(dto.getJobGrade().getEndGrade()));
            return dto;
        }).toList();
    }

    @Override
    public List<OrgStructureEmpDto> selectEmpCardInfoByEmpIds(List<String> empIds, EmpStatusEnum status, long datetime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString())
                .andIn("empId", empIds)
                .andEq("empStatus", status.getIndex().toString());
        PageResult<Map<String, String>> result = DataQuery.identifier(EmpWorkInfoDo.IDENTIFIER).queryInvisible()
                .decrypt().specifyLanguage()
                .filterProperties(dataFilter, BeanUtil.getPropName(EmpWorkInfoDo.class, '_', "empId", "name", "workno", "organize", "photo", "post", "postTxt", "jobGrade", "empStatus"), datetime);
        Map<String, String> jobGradeMap = DataQuery.allJobGrade().stream()
                .collect(Collectors.toMap(AbstractData::getBid, LabelData::getLabel));
        Map<String, String> channelMap = DataQuery.allJobGradeChannel().stream()
                .collect(Collectors.toMap(AbstractData::getBid, LabelData::getLabel));
        return Sequences.sequence(result.getItems()).map(item -> {
            OrgStructureEmpDto dto = DataSimpleUtil.filterMap2Dto(item, OrgStructureEmpDto.class);
            dto.getPhoto().setUrls(FastjsonUtil.toList(item.get("photo.urls"), String.class));
            dto.getPhoto().setNames(FastjsonUtil.toList(item.get("photo.names"), String.class));
            dto.getJobGrade().setChannelName(channelMap.get(dto.getJobGrade().getChannel()));
            dto.getJobGrade().setStartGradeName(jobGradeMap.get(dto.getJobGrade().getStartGrade()));
            dto.getJobGrade().setEndGradeName(jobGradeMap.get(dto.getJobGrade().getEndGrade()));
            return dto;
        }).toList();
    }

    @Override
    public List<EmpWorkInfoDo> gerEmpWorkInfoRange(String identifier, String empId, Long startTime, Long endTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId())
                .andNe("deleted", Boolean.TRUE.toString())
                .andEq("empId", empId);
        if (startTime != null) {
            dataFilter = dataFilter.andGe("dataEndTime", String.valueOf(startTime));
        }
        if (endTime != null) {
            dataFilter = dataFilter.andLe("dataStartTime", String.valueOf(endTime));
        }
        PageResult<EmpWorkInfoDo> dataResult = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(dataFilter, EmpWorkInfoDo.class, -1);
        return getPageList(dataResult);
    }

    @Override
    public List<EmpWorkInfoDo> getEmpWorkInfoAtWork(String identifier, List<String> empIds, Long dateTime) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId()).
                andNe("deleted", Boolean.TRUE.toString()).andIn("empId", empIds).andEq("empStatus",EmpStatusEnum.IN_JOB.getIndex().toString());
        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().
                filter(dataFilter, EmpWorkInfoDo.class, dateTime).getItems();
    }

}
