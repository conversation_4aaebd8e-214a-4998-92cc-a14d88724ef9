package com.caidaocloud.hr.service;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.archive.anonation.EnableArchiveFile;
import com.caidaocloud.hr.service.enums.archive.ArchiveModule;
import com.caidaocloud.hr.service.search.infrastructure.repository.po.EmpSearchInfoPo;
import com.caidaocloud.record.core.annotation.EnableLogRecord;
import lombok.extern.slf4j.Slf4j;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.zxp.esclientrhl.annotation.EnableESTools;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/3/2021 10:59 PM
 */
@Slf4j
@SpringBootApplication
@EnableCircuitBreaker
@EnableDiscoveryClient
@EnableFeignClients
@EnableAsync
@EnableLogRecord(name = "hr")
@EnableArchiveFile(module = ArchiveModule.HR)
@ComponentScan(basePackages = {"com.caidaocloud.hr", "com.caidaocloud.config", "com.caidaocloud.message", "com.caidaocloud.util"})
@EnableESTools(basePackages={"com.caidaocloud.hr.service.search.infrastructure.repository",
        "com.caidaocloud.hr.service.archive"},
        entityPath = {"com.caidaocloud.hr.service.search.infrastructure.repository.po",
        "com.caidaocloud.hr.service.archive.infrastructure.po"})
public class HRApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(HRApplication.class, args);
    }
}
