package com.caidaocloud.hr.service.organization.interfaces.facade.cost;

import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.common.infrastructure.utils.ObjectConvertUtil;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.interfaces.dto.base.TreeDataStatusOptDto;
import com.caidaocloud.hr.service.organization.application.cost.service.CostService;
import com.caidaocloud.hr.service.organization.domain.cost.entity.CostCenterDo;
import com.caidaocloud.hr.service.organization.interfaces.dto.cost.CostCenterDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.cost.CostCenterPageQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.cost.CostCenterQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.vo.cost.CostCenterSimpleVo;
import com.caidaocloud.hr.service.organization.interfaces.vo.cost.CostCenterVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/hr/cost/v1")
@Api(value = "/api/hr/cost/v1", description = "成本中心", tags = "v0.1")
public class CostCenterController {
    @Resource
    private CostService costService;

    private void preCheckArgument(CostCenterDo dto) {
        PreCheck.preCheckArgument(StringUtils.isBlank(dto.getCostCenterName()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30066));
        PreCheck.preCheckArgument(StringUtils.isBlank(dto.getCostCenterCode()), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30067));
    }

    @ApiOperation("新增成本中心")
    @PostMapping("/save")
    public Result save(@RequestBody CostCenterDto dto) {
        CostCenterDo costCenterDo = ObjectConverter.convert(dto, CostCenterDo.class);
        setI18nProperty(costCenterDo,dto);
        preCheckArgument(costCenterDo);
        String id = costService.saveCostCenter(costCenterDo);
        return Result.ok(id);
    }

    private void setI18nProperty(CostCenterDo costCenterDo, CostCenterDto dto) {

        costCenterDo.setI18nCostCenterName(LangUtil.getI18nValue(dto.getCostCenterName(), dto.getI18nCostCenterName()));
        costCenterDo.setCostCenterName(String.valueOf(dto.getI18nCostCenterName().get("default")));

        if (dto.getControlRange()!=null && dto.getI18nControlRange()!=null && dto.getI18nControlRange().get("default")!=null) {
            costCenterDo.setI18nControlRange(LangUtil.getI18nValue(dto.getControlRange(), dto.getI18nControlRange()));
            costCenterDo.setControlRange(String.valueOf(dto.getI18nControlRange().get("default")));
        }
        if (dto.getControlRangeName()!=null && dto.getI18nControlRangeName()!=null && dto.getI18nControlRangeName().get("default")!=null) {
            costCenterDo.setI18nControlRangeName(LangUtil.getI18nValue(dto.getControlRangeName(), dto.getI18nControlRangeName()));
            costCenterDo.setControlRangeName(String.valueOf(dto.getI18nControlRangeName().get("default")));
        }
        if (dto.getCompanyName()!=null && dto.getI18nCompanyName()!=null && dto.getI18nCompanyName().get("default")!=null) {
            costCenterDo.setCompanyName(LangUtil.getI18nValue(dto.getCompanyName(), dto.getI18nCompanyName()));
            costCenterDo.setCompanyName(String.valueOf(dto.getI18nCompanyName().get("default")));
        }
    }

    @ApiOperation("编辑成本中心")
    @PostMapping("/update")
    public Result update(@RequestBody CostCenterDto dto) {
        PreCheck.preCheckArgument(null == dto.getBid(), LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30068));
        CostCenterDo costCenterDo = ObjectConverter.convert(dto, CostCenterDo.class);
        setI18nProperty(costCenterDo,dto);
        preCheckArgument(costCenterDo);
        costService.updateCostCenter(costCenterDo);
        return Result.ok(true);
    }

    @ApiOperation("删除成本中心")
    @DeleteMapping("/delete")
    public Result delete(@RequestParam("bid") String bid,
                         @RequestParam("dataStartTime") Long dataStartTime) {
        CostCenterDo costCenterDo = new CostCenterDo();
        costCenterDo.setBid(bid);
        costCenterDo.setDataStartTime(dataStartTime);
        costService.deleteCostCenterById(costCenterDo);
        return Result.ok(true);
    }

    @ApiOperation("启用或停用成本中心")
    @PostMapping("/updateStatus")
    public Result updateStatus(@RequestBody TreeDataStatusOptDto dto) {
        dto.preCheckTimelineArgument();
        CostCenterDo data = ObjectConverter.convert(dto, CostCenterDo.class);
        if (StatusEnum.ENABLED.getIndex().equals(dto.getStatus())) {
            costService.enable(data, dto.getUpdateChildren());
        } else {
            costService.disable(data);
        }
        return Result.ok(true);
    }

    @ApiOperation("查询成本中心信息")
    @GetMapping("/detail")
    public Result<CostCenterVo> getDetail(@RequestParam("bid") String bid,
                                          @RequestParam(value = "dateTime", required = false) Long dateTime) {
        CostCenterDo data = costService.getCostCenterById(bid, dateTime);
        return ResponseWrap.wrapResult(ObjectConvertUtil.convert(data, CostCenterVo.class, CostCenterDo::i18nConvert));
    }

    @ApiOperation("查询成本中心分页列表")
    @PostMapping("/list")
    public Result<PageResult<CostCenterVo>> getList(@RequestBody CostCenterPageQueryDto queryDto) {
        BasePageQueryDto pageQueryDto = ObjectConverter.convert(queryDto, BasePageQueryDto.class);
        PageResult<CostCenterDo> pageResult = costService.getCostCenterPageResult(pageQueryDto);
        List<CostCenterVo> items = ObjectConvertUtil.convertList(pageResult.getItems(), CostCenterVo.class,CostCenterDo::i18nConvert);
        return ResponseWrap.wrapResult(new PageResult(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @ApiOperation("才到二期：查询成本中心树列表")
    @PostMapping("/tree")
    public Result getTreeList(@RequestBody CostCenterQueryDto queryDto) {
        ItemsResult content = new ItemsResult<>(costService.getTreeList(queryDto));
        return ResponseWrap.wrapResult(content);
    }

    @ApiOperation("查询成本中心树结构下拉数据")
    @GetMapping("/simpleTree")
    public Result getSimpleTreeList(
            @RequestParam(value = "dateTime", required = false) Long dateTime,
            @RequestParam(value = "status", required = false, defaultValue = "0") Integer status) {
        return ResponseWrap.wrapResult(new ItemsResult<>(costService.fetchSimpleTree(dateTime, status)));
    }

    @ApiOperation("查询成本中心列表")
    @PostMapping("/simpleList")
    public Result<List<CostCenterSimpleVo>> getSimpleList(@RequestBody CostCenterQueryDto queryDto) {
        List<CostCenterDo> doList = costService.listByFilter(queryDto);
        if(CollectionUtils.isEmpty(doList)){
            return ResponseWrap.wrapResult(Lists.newArrayList());
        }
        List<CostCenterSimpleVo> voList = ObjectConvertUtil.convertList(doList, CostCenterSimpleVo.class, CostCenterDo::i18nConvert);
        return ResponseWrap.wrapResult(voList);
    }
}
