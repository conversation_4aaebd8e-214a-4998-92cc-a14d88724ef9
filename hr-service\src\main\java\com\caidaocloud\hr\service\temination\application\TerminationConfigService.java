package com.caidaocloud.hr.service.temination.application;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.core.feign.IDictFeignClient;
import com.caidaocloud.hr.service.common.application.feign.MaintenanceFeignClient;
import com.caidaocloud.hr.service.common.constant.HrConstant;
import com.caidaocloud.hr.service.common.infrastructure.utils.FormDataConvertValueUtil;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationChangeFieldDef;
import com.caidaocloud.hr.service.confirmation.domain.entity.ConfirmationConfig;
import com.caidaocloud.hr.service.employee.application.common.service.MetadataService;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.application.tenant.feign.ITenantFeignClient;
import com.caidaocloud.hr.service.temination.application.dto.*;
import com.caidaocloud.hr.service.temination.application.feign.FormFeignClient;
import com.caidaocloud.hr.service.temination.application.feign.WfOperateFeignClient;
import com.caidaocloud.hr.service.temination.domain.entity.TerminationConfig;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationApplyType;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationConfigStatus;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationType;
import com.caidaocloud.hr.service.transfer.interfaces.vo.ChangeFieldDefVo;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.hrpaas.metadata.sdk.transaction.annotation.PaasTransactional;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.dto.KvDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.configuration.WfFunctionConfiguration;
import com.caidaocloud.workflow.dto.*;
import com.caidaocloud.workflow.enums.*;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.caidaocloud.workflow.util.BusinessDetailUtil;
import com.google.common.collect.ImmutableMap;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Maps;
import com.googlecode.totallylazy.Pair;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TerminationConfigService {
    public static final String WORKFLOW_CODE_PREFIX = "TERMINATION-";
    public static final String WORKFLOW_CALLBACK_CODE_PREFIX = "TERMINATION-CALLBACK-";
    private static final String CALLBACK_CODE_TEMPLATE = "TERMINATION-CALLBACK-%s-%s";
    private static final String APPROVER_CODE_TEMPLATE = "TERMINATION-APPROVER-%s-%s";
    private static final String CALLBACK_ADDRESS_TEMPLATE = "/api/hr/v1/termination/callback/%s";
    public static final String[][] CALLBACK_APIS = {{"approved", "同意"}, {"refused", "拒绝"}, {"revoke", "撤销"}, {"error", "异常"}};
    @Resource
    private FormFeignClient formFeignClient;
    @Resource
    private IWfRegisterFeign iWfRegisterFeign;
    @Resource
    private IDictFeignClient iDictFeignClient;
    @Resource
    private TerminationWorkflowService terminationWorkflowService;
    @Resource
    private ITenantFeignClient tenantFeignClient;
    @Resource
    private WfOperateFeignClient wfOperateFeignClient;
    @Autowired
    private MaintenanceFeignClient maintenanceFeignClient;
    @Autowired
    private TerminationService terminationService;
    @Autowired
    private FormDataConvertValueUtil formDataConvertValueUtil;
    @Resource
    private MetadataService metadataService;


    @PaasTransactional
    public String create(TerminationConfigDto terminationConfig) {
        return FastjsonUtil.convertObject(terminationConfig, TerminationConfig.class).create();
    }

    @PaasTransactional
    public void update(TerminationConfigDto terminationConfig) {
        val entity = FastjsonUtil.convertObject(terminationConfig, TerminationConfig.class);
        entity.update();
        if (entity.getStatus().equals(TerminationConfigStatus.ENABLED)) {
            registerWorkflow(entity.getBid());
        }
    }

    public void updateName(TerminationConfigDto config) {
        SecurityUserInfo user = SecurityUserUtil.getSecurityUserInfo();
        String bid = config.getBid();
        String name = config.getName();
        TerminationConfig one = one(bid);
        one.setName(name);
        one.setUpdateBy(String.valueOf(user.getUserId()));
        one.setUpdateTime(System.currentTimeMillis());
        DataUpdate.identifier(one.getIdentifier()).update(one);
        //todo : 同步流程 信息；
        WfMetaFunNameDto dto = new WfMetaFunNameDto();
        dto.setName(name);
        dto.setCode("TERMINATION-" + bid);
        dto.setTenantId(user.getTenantId());
        wfOperateFeignClient.updateFunctionName(dto);
        wfOperateFeignClient.updateFunName(dto);
    }

    @PaasTransactional
    public void enable(String bid) {
        TerminationConfig.enable(bid);
        registerWorkflow(bid);
    }

    private void registerWorkflow(String bid) {
        val config = TerminationConfig.load(bid).getOrThrow(new ServerException("config not enabled"));
        val funCode = WORKFLOW_CODE_PREFIX + config.getBid();
        FormDefDto formDef = null;
        if (StringUtils.isNotEmpty(config.getFormDefId())) {
            val formDefResult = formFeignClient.getFormDefById(config.getFormDefId());
            if (formDefResult.isSuccess()) {
                formDef = formDefResult.getData();
            } else {
                throw new ServerException(formDefResult.getMsg());
            }
        }
        List<WfMetaFunFormFieldDto> formFields = Lists.list();
        List<WfMetaSeqConditionDto> seqList = Lists.list();
        List<WfMetaNoticeVarDto> noticeVarList = Lists.list();
        val seqCallAddress = "/api/hr/v1/termination/seq";
        if (null != formDef && !CollectionUtils.isEmpty(formDef.getProperties())) {
            formDef.getProperties().forEach(propDef -> {
                boolean isSeq = false;
                List<WfComponentValueDto> componentValueList = Lists.list();
                var seqCode = String.format(funCode.replace("-", "_") + "_" + propDef.getProperty());
                boolean isNoticeVar = true;
                val noticeVarBuilder =
                        WfMetaNoticeVarDto.builder().code(propDef.getProperty())
                                .name(propDef.getName()).funCode(funCode)
                                .url("/api/hr/v1/termination/notice/var")
                                .serviceName("caidaocloud-hr-service");
                try {
                    noticeVarBuilder.type(propDef.getDataType().toString());
                } catch (Exception e) {
                    isNoticeVar = false;
                }
                if (PropertyDataType.Enum.equals(propDef.getDataType())) {
                    if (!CollectionUtils.isEmpty(propDef.getEnumDef())) {
                        isSeq = true;
                        propDef.getEnumDef().forEach(it -> {
                            componentValueList.add(new WfComponentValueDto(it.getDisplay(), it.getValue()));
                            noticeVarBuilder.enums(it.getDisplay(), it.getValue());
                        });
                    }

                } else if (PropertyDataType.Boolean.equals(propDef.getDataType())) {
                    isSeq = true;
                    componentValueList.add(new WfComponentValueDto("开", "true"));
                    componentValueList.add(new WfComponentValueDto("关", "false"));
                    noticeVarBuilder.enums("开", "true");
                    noticeVarBuilder.enums("关", "false");
                } else if (PropertyDataType.Timestamp.equals(propDef.getDataType()) && "DatePicker".equals(propDef.getWidgetType())) {
                    if (StringUtils.isNotEmpty(propDef.getStyleExtras())) {
                        val style = FastjsonUtil.toObject(propDef.getStyleExtras(), Map.class);
                        if (null != style.get("format") && !style.get("format").toString().isEmpty()) {
                            noticeVarBuilder.dateFormat(style.get("format").toString());
                        }
                    }
                }
                if (isSeq) {
                    val seq = new WfMetaSeqConditionDto(propDef.getName(),
                            seqCode,
                            Lists.list(funCode),
                            "caidaocloud-hr-service",
                            seqCallAddress,
                            WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
                            SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                            Lists.list(WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                                    WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN),
                            WfValueComponentEnum.ENUM,
                            componentValueList,
                            "", false);
                    seqList.add(seq);
                }
                formFields.add(new WfMetaFunFormFieldDto(propDef.getProperty(), propDef.getName(),
                        PropertyDataType.Timestamp.equals(propDef.getDataType()) ? WfFieldDataTypeEnum.Timestamp : WfFieldDataTypeEnum.Text));
                if (isNoticeVar) {
                    noticeVarList.add(noticeVarBuilder.build());
                }
            });
        }
        val enabledProperties = config.getEnabledTerminationProperties();
        enabledProperties.stream().map(it -> it.getProperty()).forEach(it -> {
            it.toWorkflowSeqDef(funCode, seqCallAddress, iDictFeignClient).forEach(seq -> seqList.add(seq));
            formFields.add(it.toWorkflowFormPropDef(funCode));
            List<WfMetaNoticeVarDto> noticeVar = it.toWorkflowNoticeVar(funCode);
            if (null != noticeVar) {
                noticeVarList.addAll(noticeVar);
            }
        });
        var dto = new WfMetaFunDto(config.getName(), funCode,
                WfFunctionPageJumpType.RELATIVE_PATH, SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                "caidaocloud-hr-service",
                "", "", "", formFields);
        doSeqList(seqList, bid);
        terminationWorkflowService.regEventInfoVar(noticeVarList, funCode);
        iWfRegisterFeign.registerFunction(dto);
        iWfRegisterFeign.registerSeqCondition(seqList);
        try {
            iWfRegisterFeign.registerNoticeVar(noticeVarList);
        } catch (Exception e) {
            log.error("流程消息变量注册异常", e);
        }
        registerCallback(config, funCode);
        registerApprover(formDef, funCode);
    }

    private void doSeqList(List<WfMetaSeqConditionDto> seqList, String defId) {
        String funCode = WORKFLOW_CODE_PREFIX + defId;
        String code = WORKFLOW_CODE_PREFIX.replace("-", "_") + defId;
        Map<String, String> propMap = Maps.map(
                Sequences.sequence(
                        Pair.pair("EmpOtherContract_signAgreement", "申请人竞业协议")
                )
        );
        val seqCallAddress = "/api/hr/v1/termination/seq";
        propMap.forEach((property, propName) -> {
            var seqCode = String.format(code + "_" + property);
            WfMetaSeqConditionDto seq = null;
            List<WfComponentValueDto> componentValueList = Lists.list();
            componentValueList.add(new WfComponentValueDto("是", "true"));
            componentValueList.add(new WfComponentValueDto("否", "false"));
            seq = new WfMetaSeqConditionDto(propName,
                    seqCode,
                    Lists.list(funCode),
                    "caidaocloud-hr-service",
                    seqCallAddress,
                    WfSeqConditionCallTypeEnum.INSIDER_SERVICE,
                    SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                    Lists.list(WfSeqConditionOperatorEnum.EQ, WfSeqConditionOperatorEnum.NE,
                            WfSeqConditionOperatorEnum.CONTAIN, WfSeqConditionOperatorEnum.NOT_CONTAIN),
                    WfValueComponentEnum.ENUM,
                    componentValueList,
                    "", false);

            seqList.add(seq);
        });
    }


    /**
     * 离职回调注册
     *
     * @param config  离职配置
     * @param funCode 工作流fun code
     */
    private void registerCallback(TerminationConfig config, String funCode) {
        for (String[] api : CALLBACK_APIS) {
            String code = String.format(CALLBACK_CODE_TEMPLATE, api[0].toUpperCase(), config.getBid());
            String address = String.format(CALLBACK_ADDRESS_TEMPLATE, api);

            WfMetaCallbackDto dto = new WfMetaCallbackDto(String.format("%s-%s", config.getName(), api[1]),
                    code, Lists.list(funCode),
                    SecurityUserUtil.getSecurityUserInfo().getTenantId(),
                    address,
                    "caidaocloud-hr-service",
                    "",
                    WfCallbackTypeEnum.RELATIVE_PATH,
                    WfCallbackTimeTypeEnum.NOW);
            iWfRegisterFeign.registerCallback(dto);
        }
    }

    private void registerApprover(FormDefDto formDef, String funCode) {
        if (formDef == null || CollectionUtils.isEmpty(formDef.getProperties())) {
            return;
        }
        var propertyDefList = formDef.getProperties().stream()
                .filter(e -> BooleanUtils.toBooleanDefaultIfNull(e.getApprover(), false))
                .collect(Collectors.toList());
        var tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        for (FormDefMetadataDto formPropDef : propertyDefList) {
            var approverCode = String.format(APPROVER_CODE_TEMPLATE, formPropDef.getProperty(), formDef.getId());
            var wfMetaApproverDto = new WfMetaApproverDto(formPropDef.getName(), approverCode,
                    tenantId,
                    "caidaocloud-hr-service",
                    "/api/hr/v1/termination/approver",
                    WfApproverFetchType.RELATIVE_PATH,
                    WfValueComponentEnum.NONE,
                    Lists.list(),
                    "");
            wfMetaApproverDto.setFunCode(funCode);
            try {
                iWfRegisterFeign.registerApprover(wfMetaApproverDto);
            } catch (Exception e) {
                log.error(String.format("registryApprover occur error, approverPropertyList=%s formDefId=%s funCode=%s tenantId=%s",
                        FastjsonUtil.toJson(formPropDef), formDef.getId(), funCode, tenantId), e);
            }
        }
    }

    public List<TerminationConfig> list(TerminationApplyType applyType, boolean showDisabled) {
        return TerminationConfig.listAll().stream()
                .filter(it -> applyType.equals(it.getTerminationApplyType()) && (showDisabled || !it.getStatus().equals(TerminationConfigStatus.DISABLED))).sorted(
                        Comparator.comparingInt(c -> c.getStatus().ordinal())).collect(Collectors.toList());
    }

    public TerminationConfig one(TerminationApplyType applyType) {
        return TerminationConfig.listAll().stream()
                .filter(it -> applyType.equals(it.getTerminationApplyType()) && it.getStatus().equals(TerminationConfigStatus.ENABLED))
                .findFirst().orElseThrow(() -> new ServerException("配置未启用"));
    }

    public TerminationConfig one(String configId) {
        return TerminationConfig.listAll().stream()
                .filter(it -> it.getBid().equals(configId))
                .findFirst().orElseThrow(() -> new ServerException("配置不存在"));
    }

    public void ruleSave(TerminationRuleDto config) {
        KvDto kvDto = new KvDto();
        kvDto.setProperty(HrConstant.TERMINATION_DATE_RULE + UserContext.getTenantId());
        if (config.getVoluntaryTermination() != null) {
            config.getVoluntaryTermination().setTerminationType(TerminationType.VOLUNTARY);
        }
        if (config.getPassiveTermination() != null) {
            config.getPassiveTermination().setTerminationType(TerminationType.PASSIVE);
        }
        if (config.getCancelTermination() != null) {
            config.getCancelTermination().setTerminationType(TerminationType.CANCEL);
        }
        kvDto.setContent(FastjsonUtil.toJson(config));
        tenantFeignClient.saveKv(kvDto);
    }

    public TerminationRuleDto getRuleSet() {
        Result<String> result = tenantFeignClient.getKv(HrConstant.TERMINATION_DATE_RULE + UserContext.getTenantId());
        TerminationRuleDto ruleDto = null;
        if (null == result || !result.isSuccess()
                || null == (ruleDto = FastjsonUtil.toObject(result.getData(), TerminationRuleDto.class))) {
            TerminationBaseDto vol = new TerminationBaseDto();
            vol.setTerminationType(TerminationType.VOLUNTARY);

            TerminationBaseDto pass = new TerminationBaseDto();
            pass.setTerminationType(TerminationType.PASSIVE);

            TerminationBaseDto cancel = new TerminationBaseDto();
            pass.setTerminationType(TerminationType.CANCEL);
            return new TerminationRuleDto(vol, pass, cancel);
        }

        return ruleDto;
    }

    public List<MetadataVo> fetchTerminationAvailableColumn(){
        TerminationAvailableColumn terminationAvailableColumn = new TerminationAvailableColumn();
        val configs = TerminationConfig.listAll().stream().filter(it->
                TerminationConfigStatus.ENABLED == it.getStatus() ||
                        TerminationConfigStatus.DISABLED == it.getStatus()).collect(Collectors.toList());
        terminationAvailableColumn.getMetadataList().add(metadataService.getMetadata("entity.hr.EmpPrivateInfo"));
        terminationAvailableColumn.getMetadataList().add(metadataService.getMetadata("entity.hr.EmpWorkInfo"));
        terminationAvailableColumn.getMetadataList().add(metadataService.getMetadata("entity.hr.EmpWorkOverview"));
        terminationAvailableColumn.getMetadataList().add(metadataService.getMetadata("entity.hr.LastContract"));
        terminationAvailableColumn.getMetadataList().add(metadataService.getMetadata("entity.hr.TerminationApply"));
        val metadataList = configs.stream().map(it->
                it.getFormDefId()
        ).distinct().filter(it->StringUtils.isNotEmpty(it)).map(it->{
            MetadataVo metadata = new MetadataVo();
            val formDef = formFeignClient.getFormDefById(it).getData();
            metadata.setIdentifier("entity.form." + formDef.getId());
            metadata.setName(formDef.getName());
            metadata.setCustomProperties(FastjsonUtil.convertList(formDef.getProperties(), MetadataPropertyVo.class));
            return metadata;
        }).collect(Collectors.toList());
        terminationAvailableColumn.getMetadataList().addAll(metadataList);
        return terminationAvailableColumn.getMetadataList();
    }

    /**
     * 离职配置修改
     * @param configId
     */
    public void updateConfig(String configId) {
        TerminationConfig config = TerminationConfig.listAll().stream()
                .filter(it -> it.getBid().equals(configId))
                .findFirst().orElseThrow(() -> new ServerException("配置不存在"));

        MetadataVo metadataVoForWork = metadataService.getMetadata("entity.hr.EmpWorkInfo");
        // 员工信息
        List<MetadataPropertyVo> empStandList = metadataVoForWork.getStandardProperties();
        empStandList.addAll(metadataVoForWork.getCustomProperties());

        List<MetadataPropertyVo> displayWorkInfos = config.getDisplayWorkInfos();

        List<MetadataPropertyVo> displayWorkInfosNew= Lists.list();
        for (MetadataPropertyVo metadataPropertyVo : empStandList) {
            if (displayWorkInfos==null||CollectionUtils.isEmpty(displayWorkInfos)){
                break;
            }
            for (MetadataPropertyVo displayWorkInfo : displayWorkInfos) {
                if (metadataPropertyVo.getProperty().equals(displayWorkInfo.getProperty())){
                    displayWorkInfo.setI18nName(metadataPropertyVo.getI18nName());
                    displayWorkInfosNew.add(displayWorkInfo);
                    break;
                }
            }
        }

        val entity = FastjsonUtil.convertObject(config, TerminationConfig.class);

        log.info(" termination update entity:{}",entity.toString());
        entity.update();
    }
}