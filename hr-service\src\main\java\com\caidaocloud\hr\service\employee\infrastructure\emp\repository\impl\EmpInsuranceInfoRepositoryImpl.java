package com.caidaocloud.hr.service.employee.infrastructure.emp.repository.impl;

import com.caidaocloud.hr.service.employee.domain.base.repository.BaseRepositoryImpl;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpEduInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpInsuranceInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpEduInfoRepository;
import com.caidaocloud.hr.service.employee.domain.emp.manage.repository.IEmpInsuranceInfoRepository;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/12
 */
@Repository
public class EmpInsuranceInfoRepositoryImpl extends BaseRepositoryImpl<EmpInsuranceInfoDo> implements IEmpInsuranceInfoRepository {
    @Override
    public EmpInsuranceInfoDo selectByEmpId(String empId, String identifier, String tenantId) {
        List<EmpInsuranceInfoDo> dbList = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(DataFilter.eq("empId", empId).andEq("tenantId", tenantId)
                        .andNe("deleted", Boolean.TRUE.toString()), EmpInsuranceInfoDo.class).getItems();
        if (CollectionUtils.isEmpty(dbList)) {
            return null;
        }
        return dbList.get(0);
    }

    @Override
    public List<EmpInsuranceInfoDo> selectByEmpIds(List<String> empIds, String identifier, String tenantId) {
        List<EmpInsuranceInfoDo> dbList = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(DataFilter.in("empId", empIds).andEq("tenantId", tenantId)
                        .andNe("deleted", Boolean.TRUE.toString()), EmpInsuranceInfoDo.class).getItems();

        return dbList;
    }

    @Override
    public List<EmpInsuranceInfoDo> selectByPayUnitId(String payUnitId, String identifier, String tenantId) {
        List<EmpInsuranceInfoDo> dbList = DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible()
                .filter(DataFilter.eq("unitBid", payUnitId).andEq("tenantId", tenantId)
                        .andNe("deleted", Boolean.TRUE.toString()), EmpInsuranceInfoDo.class).getItems();

        return dbList;
    }

}
