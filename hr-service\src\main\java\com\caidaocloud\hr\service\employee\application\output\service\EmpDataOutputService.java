package com.caidaocloud.hr.service.employee.application.output.service;

import com.caidaocloud.hr.service.dto.EmpDataQueryDto;
import com.caidaocloud.hr.service.dto.EmpPageOutQueryDto;
import com.caidaocloud.hr.service.dto.onboarding.EmpOnDataQueryDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.domain.base.entity.DataEntity;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpConcurrentPostDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpPrivateInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.organization.domain.org.entity.CustomOrgRoleDo;
import com.caidaocloud.hr.service.organization.domain.org.entity.OrgDo;
import com.caidaocloud.hr.service.organization.domain.org.service.CustomOrgRoleDomainService;
import com.caidaocloud.hr.service.organization.domain.org.service.OrgDomainService;
import com.caidaocloud.hr.service.vo.EmpDataVo;
import com.caidaocloud.hr.service.vo.EmpPrivateInfoVo;
import com.caidaocloud.hr.service.vo.EmpWorkInfoVo;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class EmpDataOutputService {
    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;
    @Resource
    private CustomOrgRoleDomainService customOrgRoleDomainService;
    @Resource
    private EmpPrivateInfoDomainService empPrivateInfoDomainService;
    @Resource
    private OrgDomainService orgDomainService;
    @Resource
    private EmpConcurrentPostDomainService empConcurrentPostDomainService;
    @Resource
    private EmpExtFieldService empExtFieldService;

    public List<EmpDataVo> getOrgRoleInfo(EmpDataQueryDto dto) {
        if (dto.getDateTime() == null) {
            dto.setDateTime(System.currentTimeMillis());
        }
        List<String> empIds = new ArrayList<>();
        if (dto.getTypeEnum() != null) {
            switch (dto.getTypeEnum()) {
                //员工
                case EMP:
                    empIds = dto.getEmpIds();
                    break;
                //组织角色
                case ORGANIZATIONAL_ROLE:
                    empIds = getOrgRoleEmpIdsByOrgMembers(dto);
                    break;
                //直接上级
                case EMP_LEADER_EMP:
                    empIds = empWorkInfoDomainService.getEmpListByEmpIds(dto.getEmpIds(), dto.getDateTime()).stream()
                            .filter(st -> st.getLeadEmpId() != null && st.getLeadEmpId().getEmpId() != null)
                            .map(st -> st.getLeadEmpId().getEmpId())
                            .distinct().collect(Collectors.toList());
                    break;
                //组织负责人
                case ORG_LEADER_EMP:
                    //组织查负责岗位
                    List<String> posts = getPostIdsByOrgIds(dto.getEmpIds(), dto.getDateTime());
                    //兼岗查人
                    List<String> empConcurrentEmpIds = empConcurrentPostDomainService.selectListByPost(posts).stream().map(EmpConcurrentPostDo::getEmpId).collect(Collectors.toList());
                    //岗位查人
                    List<String> empWorkIds = empWorkInfoDomainService.getEmpListByPosts(dto.getDateTime(), posts).stream().map(EmpWorkInfoDo::getEmpId).collect(Collectors.toList());
                    Set<String> set = new HashSet<>(empConcurrentEmpIds);
                    set.addAll(empWorkIds);
                    empIds.addAll(set);
                    break;
                default:
                    break;
            }
        }
        return getEmpDataByEmpIds(dto.isQueryEmpPrivateInfo(), empIds, dto.getDateTime());
    }

    /**
     * 根据empIds查信息
     */
    private List<EmpDataVo> getEmpDataByEmpIds(boolean queryEmpPrivateInfo, List<String> empIds, long dateTime) {
        List<EmpDataVo> result = new LinkedList<>();

        if (CollectionUtils.isNotEmpty(empIds)) {
            List<EmpWorkInfoDo> workInfoDos = empWorkInfoDomainService.getEmpListByEmpIds(empIds, dateTime);
            //查privateInfo
            if (queryEmpPrivateInfo) {
                Map<String, EmpPrivateInfoDo> privateInfoDoMap = empPrivateInfoDomainService.getAllEmpPrivateInfoByEmpIds(empIds).stream().collect(
                        Collectors.toMap(EmpPrivateInfoDo::getEmpId, st -> st));
                for (EmpWorkInfoDo data : workInfoDos) {
                    EmpWorkInfoVo workInfoDto = ObjectConverter.convert(data, EmpWorkInfoVo.class);
                    EmpPrivateInfoVo privateInfoDto = ObjectConverter.convert(privateInfoDoMap.get(data.getEmpId()), EmpPrivateInfoVo.class);
                    result.add(new EmpDataVo(data.getEmpId(), workInfoDto, privateInfoDto));
                }
            } else {
                for (EmpWorkInfoDo data : workInfoDos) {
                    EmpWorkInfoVo workInfoDto = ObjectConverter.convert(data, EmpWorkInfoVo.class);
                    result.add(new EmpDataVo(data.getEmpId(), workInfoDto, null));
                }
            }
        }
        return result;
    }

    /**
     * 根据组织成员获取组织角色id
     * @return
     */
    private List<String> getOrgRoleEmpIdsByOrgMembers(EmpDataQueryDto dto) {
        long dateTime = dto.getDateTime();
        List<String> empIds = new ArrayList<>();
        List<String> orgIds = null != dto.getOrganize() ? dto.getOrganize() : getOrgIdsByOrgMembers(dto.getEmpIds(), dateTime);
        if (CollectionUtils.isNotEmpty(orgIds)) {
            List<CustomOrgRoleDo> orgRoleDos = customOrgRoleDomainService.selectRoleByOrgIds(orgIds, dto.getRoleCode(), dateTime);
            empIds = orgRoleDos.stream().map(st -> st.getLeaderEmp().getEmpId()).distinct().collect(Collectors.toList());
        }
        return empIds;
    }

    /**
     * 根据组织成员获取orgIds
     * @param orgMembers
     * @param dateTime
     * @return
     */
    private List<String> getOrgIdsByOrgMembers(List<String> orgMembers, long dateTime) {
        List<EmpWorkInfoDo> workInfoDos = empWorkInfoDomainService.getEmpListByEmpIds(orgMembers, dateTime);
        return workInfoDos.stream().map(EmpWorkInfoDo::getOrganize).distinct().collect(Collectors.toList());
    }

    private List<String> getPostIdsByOrgIds(List<String> orgMembers, long dateTime) {
        List<String> orgIds = getOrgIdsByOrgMembers(orgMembers, dateTime);
        return orgDomainService.selectAllByIds(orgIds, dateTime).stream().map(OrgDo::getLeaderPost).distinct().collect(Collectors.toList());
    }

    /**
     * 查非离职分页
     * @param dto
     * @return
     */
    public List<EmpDataVo> getNonLeaveEmpPage(EmpPageOutQueryDto dto) {
        List<EmpDataVo> result = new LinkedList<>();
        Long newDateTime = DataEntity.getTimeIfNullSetDefault(dto.getDateTime());
        List<EmpWorkInfoDo> nonLeaveEmpPage = empWorkInfoDomainService.getNonLeaveEmpPage(newDateTime, dto);
        if (CollectionUtils.isNotEmpty(nonLeaveEmpPage)) {
            List<String> empIds = nonLeaveEmpPage.stream().map(EmpWorkInfoDo::getEmpId).collect(Collectors.toList());
            //查privateInfo
            if (dto.isQueryEmpPrivateInfo()) {
                Map<String, EmpPrivateInfoDo> privateInfoDoMap = empPrivateInfoDomainService.getAllEmpPrivateInfoByEmpIds(empIds).stream().collect(
                        Collectors.toMap(EmpPrivateInfoDo::getEmpId, st -> st));
                for (EmpWorkInfoDo data : nonLeaveEmpPage) {
                    EmpWorkInfoVo workInfoDto = ObjectConverter.convert(data, EmpWorkInfoVo.class);
                    EmpPrivateInfoVo privateInfoDto = ObjectConverter.convert(privateInfoDoMap.get(data.getEmpId()), EmpPrivateInfoVo.class);
                    result.add(new EmpDataVo(data.getEmpId(), workInfoDto, privateInfoDto));
                }
            } else {
                for (EmpWorkInfoDo data : nonLeaveEmpPage) {
                    EmpWorkInfoVo workInfoDto = ObjectConverter.convert(data, EmpWorkInfoVo.class);
                    result.add(new EmpDataVo(data.getEmpId(), workInfoDto, null));
                }
            }
        }
        return result;
    }

    public EmpWorkInfoVo getEmpWorkInfo(String empId, Long dateTime){
        EmpWorkInfoDo data = empWorkInfoDomainService.getEmpWorkInfo(empId, dateTime);
        EmpWorkInfoVo workInfoVo = ObjectConverter.convert(data, EmpWorkInfoVo.class);
        // 自定义字段查询
        Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(data.getDoIdentifier(), data);
        workInfoVo.setExt(ext);
        return workInfoVo;
    }

    public List<EmpDataVo> getEmpDetails(EmpOnDataQueryDto dto) {
        if (dto.getDateTime() == null) {
            dto.setDateTime(System.currentTimeMillis());
        }
        List<String> empIds = new ArrayList<>();
        if (dto.getTypeEnum() != null) {
            switch (dto.getTypeEnum()) {
                // HRBP、Recruiters
                case HRBP:
                case RECRUITERS:
                    empIds = getOrgRoleEmpIdsByOrganize(dto.getOrganize(), dto.getDateTime(), dto.getRoleCode());
                    break;
                // 直接上级
                case EMP_LEADER_EMP:
                    empIds = dto.getEmpIds();
                    break;
                default:
                    break;
            }
        }
        return getEmpDataByEmpIds(true, empIds, dto.getDateTime());
    }

    private List<String> getOrgRoleEmpIdsByOrganize(List<String> organize, Long dateTime, String roleCode) {
        List<String> empIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(organize)) {
            List<CustomOrgRoleDo> orgRoleDos = customOrgRoleDomainService.selectRoleByOrgIds(organize, roleCode, dateTime);
            empIds = orgRoleDos.stream().map(st -> st.getLeaderEmp().getEmpId()).distinct().collect(Collectors.toList());
        }
        return empIds;
    }
}
