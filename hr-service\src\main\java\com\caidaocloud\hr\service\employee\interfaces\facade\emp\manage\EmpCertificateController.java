package com.caidaocloud.hr.service.employee.interfaces.facade.emp.manage;

import com.caidaocloud.hr.service.dto.EmpCertificateDto;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpCertificateService;
import com.caidaocloud.hr.service.employee.application.emp.manage.service.EmpExtFieldService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpCertificateDo;
import com.caidaocloud.hr.service.employee.interfaces.vo.emp.manage.EmpCertificateVo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/emp/certificate/v1")
@Api(value = "/api/hr/emp/certificate/v1", description = "员工证书及技能", tags = "v2.6")
public class EmpCertificateController {
    @Resource
    private EmpCertificateService empCertificateService;

    @Resource
    private EmpExtFieldService empExtFieldService;

    @ApiOperation(value = "查询证书及技能列表")
    @GetMapping(value = "/list")
    public Result<List<EmpCertificateVo>> list(String empId) {
        List<EmpCertificateVo> data = Lists.list();
        List<EmpCertificateDo> dataList = empCertificateService.getEmpCertificateList(empId);
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(item -> {
                Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(item.getDoIdentifier(), item);
                EmpCertificateVo empCertificateVo = ObjectConverter.convert(item, EmpCertificateVo.class);
                empCertificateVo.setExt(ext);
                data.add(empCertificateVo);
            });
        }
        return Result.ok(data);
    }

    @ApiOperation(value = "查询员工证书及技能详情")
    @GetMapping("/detail")
    public Result<EmpCertificateVo> detail(String bid) {
        EmpCertificateDo data = empCertificateService.getDetail(bid);
        if (Objects.isNull(data)) {
            return Result.ok(new EmpCertificateVo());
        }
        Map<String, Object> ext = empExtFieldService.getEmpCustomPropertyValue(data.getIdentifier(), data);
        EmpCertificateVo vo = ObjectConverter.convert(data, EmpCertificateVo.class);
        vo.setExt(ext);
        return Result.ok(vo);
    }

    @ApiOperation(value = "新增员工证书及技能")
    @PostMapping("/save")
    @LogRecordAnnotation(category = "新增", success = "新增了{workInfo{#empId}}的证书技能", menu = "人事-员工信息-证书技能", condition = "{{#condition}}")
    public Result save(@RequestBody EmpCertificateDto dto) {
        LogRecordContext.putVariable("empId", dto.getEmpId());
        String bid = empCertificateService.save(dto);
        LogRecordContext.putVariable("condition", true);
        return Result.ok(bid);
    }

    @ApiOperation("修改员工证书及技能")
    @PostMapping("/update")
    @LogRecordAnnotation(category = "编辑", success = "编辑了{workInfo{#empId}}的证书技能", menu = "人事-员工信息-证书技能", condition = "{{#condition}}")
    public Result update(@RequestBody EmpCertificateDto dto) {
        LogRecordContext.putVariable("empId", dto.getEmpId());
        empCertificateService.update(dto);
        LogRecordContext.putVariable("condition", true);
        return Result.ok(true);
    }

    @ApiOperation("删除员工证书及技能")
    @DeleteMapping("/delete")
    @LogRecordAnnotation(category = "删除", success = "删除了{workInfo{#empId}}的证书技能", menu = "人事-员工信息-证书技能", condition = "{{#condition}}")
    public Result delete(String bid) {
        EmpCertificateDo detail = empCertificateService.getDetail(bid);
        LogRecordContext.putVariable("empId", detail.getEmpId());
        empCertificateService.delete(bid);
        LogRecordContext.putVariable("condition", true);
        return Result.ok(true);
    }
}
