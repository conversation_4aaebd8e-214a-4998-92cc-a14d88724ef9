package com.caidaocloud.hr.service.employee.domain.emp.manage.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpEduInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpInsuranceInfoDo;
import com.caidaocloud.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/12
 */
@Service
public class EmpInsuranceInfoDomainService {
    @Resource
    private EmpInsuranceInfoDo empInsuranceInfoDo;

    public void save(EmpInsuranceInfoDo data) {
        PreCheck.preCheckArgument(StringUtil.isEmpty(data.getEmpId()), "员工不能为空");
        empInsuranceInfoDo.save(data);
    }

    public void update(EmpInsuranceInfoDo data) {
        PreCheck.preCheckArgument(StringUtil.isEmpty(data.getEmpId()), "员工不能为空");
        EmpInsuranceInfoDo source = getInsuranceInfo(data.getEmpId());
        if (source == null) {
            empInsuranceInfoDo.save(data);
            return;
        }
        data.setBid(source.getBid());
        empInsuranceInfoDo.update(data, source);
    }

    public EmpInsuranceInfoDo getInsuranceInfo(String empId) {
        return empInsuranceInfoDo.selectById(empId);
    }

    public List<EmpInsuranceInfoDo> getInsuranceInfos(List<String> empIds) {
        return empInsuranceInfoDo.selectByEmpIds(empIds);
    }
    
    public List<EmpInsuranceInfoDo> getInsuranceInfosByPayUnitId(String payUnitId) {
        return empInsuranceInfoDo.selectByPayUnitId(payUnitId);
    }
    
    

}
