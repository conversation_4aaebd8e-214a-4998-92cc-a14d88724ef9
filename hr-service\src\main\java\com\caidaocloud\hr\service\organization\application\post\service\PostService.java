package com.caidaocloud.hr.service.organization.application.post.service;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.hr.service.common.infrastructure.utils.ObjectConvertUtil;
import com.caidaocloud.hr.service.confirmation.application.service.ConfirmationRecordService;
import com.caidaocloud.hr.service.confirmation.interfaces.dto.ConfirmationRecordSearchDto;
import com.caidaocloud.hr.service.confirmation.interfaces.vo.ConfirmationRecordVo;
import com.caidaocloud.hr.service.dto.onboarding.ImportPostSyncDto;
import com.caidaocloud.hr.service.employee.application.feign.oboarding.emp.EmpPrivateFeignClient;
import com.caidaocloud.hr.service.employee.domain.base.dto.BasePageQueryDto;
import com.caidaocloud.hr.service.employee.domain.base.enums.BusinessEventTypeEnum;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpWorkInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpWorkInfoDomainService;
import com.caidaocloud.hr.service.employee.infrastructure.utils.TagProperty;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpEntryProcessDisableDto;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpEntryProcessDisableVo;
import com.caidaocloud.hr.service.organization.application.org.dto.DisAbleCommonErrorDto;
import com.caidaocloud.hr.service.organization.application.org.dto.DisAbleErrorDto;
import com.caidaocloud.hr.service.organization.application.org.enums.DisAbleErrorEnum;
import com.caidaocloud.hr.service.organization.domain.post.entity.BenchmarkPositionDo;
import com.caidaocloud.hr.service.organization.domain.post.entity.PostDo;
import com.caidaocloud.hr.service.organization.domain.post.service.PostDomainService;
import com.caidaocloud.hr.service.organization.interfaces.dto.org.OrgOrPostQueryDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.PostDto;
import com.caidaocloud.hr.service.organization.interfaces.dto.post.PostQueryDto;
import com.caidaocloud.hr.service.vo.organization.post.PostVo;
import com.caidaocloud.hr.service.search.application.event.EntityDataChange;
import com.caidaocloud.hr.service.temination.domain.enums.TerminationStatus;
import com.caidaocloud.hr.service.transfer.application.service.TransferService;
import com.caidaocloud.hr.service.transfer.interfaces.dto.TransferQueryDto;
import com.caidaocloud.hr.service.transfer.interfaces.vo.TransferListVo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.JobGradeRange;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.util.*;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/12/3
 */
@Slf4j
@Service
public class PostService {
    private final String msg = "停用失败";
    @Resource
    private PostDomainService postDomainService;
    @Resource
    private BenchmarkPositionService benchmarkPositionService;

    @Resource
    private EmpWorkInfoDomainService empWorkInfoDomainService;

    @Resource
    private ConfirmationRecordService confirmationRecordService;

    @Resource
    private TransferService transferService;

    @Resource
    private EmpPrivateFeignClient empPrivateFeignClient;

    @Resource
    PostCodeRuleService postCodeRuleService;


    public void enable(PostDo data) {
        postDomainService.updateStatus(data, BusinessEventTypeEnum.ENABLE);
    }

    public Result disable(PostDo data, long dateTime) {
        //todo : 在职员工 有生效中或未来生效的兼岗岗位 审批中异动流程 审批中转正流程 入职流程
        // 审批通过的未来生效日期的异动   审批通过的未来生效日期的转正异动


        //1. 在职员工
        Result<DisAbleErrorDto> empError = checkEmp(data, dateTime);
        if (empError != null) return empError;

        //2. 有生效中或未来生效的兼岗岗位
        Result<DisAbleErrorDto> concurrentError = checkConcurrent(data, dateTime);
        if (concurrentError != null) return concurrentError;

        //3. 审批中异动流程
        Result<DisAbleErrorDto> errorDto = checkTranaferInProcess(data);
        if (errorDto != null) return errorDto;

        //4. 转正流程（至此组织）
        Result<DisAbleErrorDto> confirmationInProcessError = checkConfirmationInProcess(data);
        if (confirmationInProcessError != null) return confirmationInProcessError;

        //5. 入职流程中（指未进入员工信息）的候选人组织
        Result<DisAbleErrorDto> empEntityProcessError = checkEmpEntityProcess(data);
        if (empEntityProcessError != null) return empEntityProcessError;

        //6. 审批通过的未来生效的异动（含异动/转正异动后的岗位至“该岗位”）
        //未来生效 比对时间为当前时间；
        Result<DisAbleErrorDto> tranaferApproveError = checkTranaferApprove(data, System.currentTimeMillis());
        if (tranaferApproveError != null) return tranaferApproveError;

        //7. 审批通过的未来生效的转正异动（含转正异动后的岗位至“该岗位”）
        Result<DisAbleErrorDto> confirmationApproveError = checkConfirmationApprove(data, System.currentTimeMillis());
        if (confirmationApproveError != null) return confirmationApproveError;


        postDomainService.updateStatus(data, BusinessEventTypeEnum.DISABLE);
        return Result.ok(true);
    }

    @Nullable
    private Result<DisAbleErrorDto> checkConfirmationApprove(PostDo data, Long dateTime) {
        ConfirmationRecordSearchDto searchDto = new ConfirmationRecordSearchDto();
        searchDto.setPageNo(1);
        searchDto.setPageSize(6);
        searchDto.setApprovalStatus("APPROVE");
        searchDto.setNewPost(data.getBid());
        searchDto.setConfirmationFutureDate(String.valueOf(dateTime));
        PageResult<ConfirmationRecordVo> page = confirmationRecordService.page(searchDto);
        if (!page.getItems().isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 转正数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            for (ConfirmationRecordVo item : page.getItems()) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getConfirmationDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.CONFIRMATION_APPROVE.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.CONFIRMATION_APPROVE.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkTranaferApprove(PostDo data, Long dateTime) {
        TransferQueryDto dto = new TransferQueryDto();
        //审批通过
        dto.setApprovalStatus(TerminationStatus.APPROVE.value);
        //默认获取6条
        dto.setPageNo(1);
        dto.setPageSize(6);
        dto.setShowRevoke(false);
        dto.setPostEnable(true);
        dto.setNewPostId(data.getBid());
        dto.setEffectiveFutureDate(String.valueOf(dateTime));
        PageResult<TransferListVo> pageResult = transferService.pageApply(dto);
        List<TransferListVo> items = pageResult.getItems();

        if (!items.isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 异动审批数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            //生效日期 选取 effectiveDate
            for (TransferListVo item : items) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getEffectiveDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.TRANAFER_APPROVE.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.TRANAFER_APPROVE.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }


    @Nullable
    private Result<DisAbleErrorDto> checkEmpEntityProcess(PostDo data) {
        EmpEntryProcessDisableDto dto = new EmpEntryProcessDisableDto();
        dto.setPost(data.getBid());
        dto.setPageNo(1);
        dto.setPageSize(6);
        Result<PageResult<EmpEntryProcessDisableVo>> disableEmpEntryProcessPage = empPrivateFeignClient.getDisableEmpEntryProcessPage(dto);
        List<EmpEntryProcessDisableVo> items = disableEmpEntryProcessPage.getData().getItems();
        if (!items.isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 转正数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            for (EmpEntryProcessDisableVo item : items) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getHireDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.EMP_ENTRY_PROCESS.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.EMP_ENTRY_PROCESS.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkConfirmationInProcess(PostDo data) {
        ConfirmationRecordSearchDto searchDto = new ConfirmationRecordSearchDto();
        searchDto.setPageNo(1);
        searchDto.setPageSize(6);
        searchDto.setApprovalStatus("IN_PROCESS");
        searchDto.setNewPost(data.getBid());
        PageResult<ConfirmationRecordVo> page = confirmationRecordService.page(searchDto);
        if (!page.getItems().isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 转正数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            for (ConfirmationRecordVo item : page.getItems()) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getConfirmationDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.CONFIRMATION_IN_PROCESS.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.CONFIRMATION_IN_PROCESS.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkTranaferInProcess(PostDo data) {
        TransferQueryDto dto = new TransferQueryDto();
        //审批中
        dto.setApprovalStatus(TerminationStatus.IN_PROCESS.value);
        //默认获取6条
        dto.setPageNo(1);
        dto.setPageSize(6);
        dto.setShowRevoke(false);
        dto.setPostEnable(true);
        dto.setNewPostId(data.getBid());
        PageResult<TransferListVo> pageResult = transferService.pageApply(dto);
        List<TransferListVo> items = pageResult.getItems();

        if (!items.isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接 异动审批数据 问题信息
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            //生效日期 选取 effectiveDate
            for (TransferListVo item : items) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(item.getName());
                commonErrorDto.setWorkNo(item.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(item.getEffectiveDate()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.TRANAFER_IN_PROCESS.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.TRANAFER_IN_PROCESS.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    @Nullable
    private Result<DisAbleErrorDto> checkConcurrent(PostDo data, long dateTime) {
        List<EmpConcurrentPostDo> concurrentPostDos = getEnableConcurrentByPost(data.getBid(), dateTime);
        if (!concurrentPostDos.isEmpty()) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接在职员工
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            List<String> concurrentEmpIds
                    = concurrentPostDos.stream().map(empConcurrentPostDo -> empConcurrentPostDo.getEmpId()).collect(Collectors.toList());
            Map<String, EmpWorkInfoDo> empIdToEmp = new HashMap<>();
            if (CollectionUtils.isNotEmpty(concurrentEmpIds)) {
                List<EmpWorkInfoDo> empListByEmpIds = empWorkInfoDomainService.getEmpListByEmpIds(concurrentEmpIds, dateTime);
                empIdToEmp = empListByEmpIds.stream().collect(Collectors.toMap(EmpWorkInfoDo::getEmpId, empWorkInfoDo -> empWorkInfoDo));
            }
            //拼接兼岗数据
            for (EmpConcurrentPostDo concurrentPostDo : concurrentPostDos) {
                String empId = concurrentPostDo.getEmpId();
                if (StringUtil.isNotEmpty(empId)) {
                    if (empIdToEmp.containsKey(empId)) {
                        DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                        EmpWorkInfoDo workInfoDo = empIdToEmp.get(empId);
                        if (null == workInfoDo.getName() && null == workInfoDo.getWorkno()) {
                            //问题数据不展示；
                            continue;
                        }
                        commonErrorDto.setName(workInfoDo.getName());
                        commonErrorDto.setWorkNo(workInfoDo.getWorkno());
                        commonErrorDto.setDateStartTime(String.valueOf(concurrentPostDo.getStartDate()));
                        commonErrorDto.setDateEndTime(String.valueOf(concurrentPostDo.getEndDate()));
                        commonErrorDtos.add(commonErrorDto);
                    }
                }
            }
            errorDto.setTypeName(DisAbleErrorEnum.EMP_CONCURRENT_POST.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.EMP_CONCURRENT_POST.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    /**
     * 兼岗生效中  的 对应人；
     */
    public List<EmpConcurrentPostDo> getEnableConcurrentByPost(String postId, Long dateTime) {
        // 兼岗
        return postDomainService.getEnableConcurrentByPost(postId, dateTime);
    }

    @Nullable
    private Result<DisAbleErrorDto> checkEmp(PostDo data, long dateTime) {
        List<EmpWorkInfoDo> emp = getEmpByPost(data.getBid(), dateTime);
        if (emp.size() > 0) {
            DisAbleErrorDto errorDto = new DisAbleErrorDto();
            //拼接在职员工
            List<DisAbleCommonErrorDto> commonErrorDtos = new ArrayList<>();
            for (EmpWorkInfoDo empWorkInfoDo : emp) {
                DisAbleCommonErrorDto commonErrorDto = new DisAbleCommonErrorDto();
                commonErrorDto.setName(empWorkInfoDo.getName());
                commonErrorDto.setWorkNo(empWorkInfoDo.getWorkno());
                commonErrorDto.setDateStartTime(String.valueOf(empWorkInfoDo.getDataStartTime()));
                commonErrorDtos.add(commonErrorDto);
            }
            errorDto.setTypeName(DisAbleErrorEnum.EMP_WORK_INFO.getName());
            errorDto.setTypeCode(DisAbleErrorEnum.EMP_WORK_INFO.getCode());
            errorDto.setErrorDatas(commonErrorDtos);
            return Result.status(errorDto, 0, msg);
        }
        return null;
    }

    public PostDo getPostById(String bid, Long dataTime) {
        return postDomainService.selectById(bid, dataTime);
    }

    private void doPostCode(PostDo data) {
        if (null == data.getBid()) {
            // 处理新增时的组织编码
            data.setCode(postCodeRuleService.nextPostCode(data.getCode(), data.getCode()));
            return;
        }

        PostDo postDo = getPostById(data.getBid(), data.getDataStartTime());
        //别的服务用雪花设置bid 实际是新增
        if (postDo == null) {
            data.setCode(postCodeRuleService.nextPostCode(data.getCode(), data.getCode()));
            return;
        }
        if (StringUtils.isEmpty(data.getCode())) {
            // 如果编码为空，则使用 db 中的编码
            data.setCode(postDo.getCode());
        } else {
            // 如果编码不为空，则根据编码配置策略进行修改或不允许修改
            data.setCode(postCodeRuleService.nextPostCode(data.getCode(), postDo.getCode()));
        }
    }

    private List<EmpWorkInfoDo> getEmpByPost(String bid, Long dateTime) {
        return postDomainService.getEmpByPost(bid, dateTime);
    }

    /**
     * 删除岗位
     *
     * @param bid
     */
    public void deleteOrgById(String bid, Long dataTime) {
        // 查询直属下级
        /*List<OrgDo> childrenList = orgDomainService.selectChildrenList(bid, dataTime);
        if (CollectionUtils.isNotEmpty(childrenList)) {
            throw new ServerException("存在下级，无法删除");
        }*/
        PostDo postDo = postDomainService.selectById(bid, dataTime);
        LogRecordContext.putVariable("name", postDo.getName());
        // todo 存在员工，则不能删除

        postDomainService.delete(bid, dataTime);
    }

    public PostDo getById(String bid, Long dateTime) {
        return postDomainService.selectById(bid, dateTime);
    }

    public String save(PostDo data) {
        doPostCode(data);
        return postDomainService.save(data);
    }

    public void update(PostDo data) {
        doPostCode(data);
        postDomainService.update(data);
    }

    public PageResult<PostDo> getPageResult(PostQueryDto pageQueryDto) {
        return postDomainService.selectPage(pageQueryDto);
    }

    public List<PostDo> selectList(String orgId, Integer status, Long dataStartTime, String keyword) {
        return postDomainService.selectList(orgId, status, dataStartTime, keyword);
    }

    public List<PostDo> selectPostListByOrgIdList(OrgOrPostQueryDto orgQueryDto) {
        orgQueryDto.setPageNo(Optional.ofNullable(orgQueryDto.getPageNo()).orElse(1));
        orgQueryDto.setPageSize(Optional.ofNullable(orgQueryDto.getPageSize()).orElse(1000));
        orgQueryDto.setStatus(Optional.ofNullable(orgQueryDto.getStatus()).orElse(0));
        orgQueryDto.setDataStartTime(Optional.ofNullable(orgQueryDto.getDataStartTime()).orElse(System.currentTimeMillis()));
        orgQueryDto.setOrgCodeList(Optional.ofNullable(orgQueryDto.getOrgIdList()).orElse(new ArrayList<>()));
        return postDomainService.selectPostListByOrgIdList(orgQueryDto);
    }

    public List<PostDo> selectListWithInIds(String orgId, Integer status, Long dataStartTime) {
        return postDomainService.selectList(orgId, status, dataStartTime, null);
    }

    public PageResult<PostDo> getPageResultAll(PostQueryDto pageQueryDto) {
        return postDomainService.selectPageAll(pageQueryDto);
    }

    public List<PostDo> selectPostDoByCodes(List<String> codes, BasePageQueryDto page) {
        return postDomainService.selectPostDoByCodes(codes, page);
    }

    public List<PostDo> selectPostDoByCodes(List<String> codes, Long datetime) {
        BasePageQueryDto dto = new BasePageQueryDto();
        dto.setDateTime(datetime);
        dto.setPageSize(codes.size());
        return postDomainService.selectPostDoByCodes(codes, dto);
    }

    public ImportPostSyncDto<PostDto, PostDto> importSyncSave(List<PostDto> dataList) {
        ImportPostSyncDto<PostDto, PostDto> ipsd = new ImportPostSyncDto();
        if (null == dataList || dataList.isEmpty()) {
            return ipsd;
        }

        Map<String, List<PostDto>> nameMap = getNameGroupMap(dataList);
        List<String> collect = nameMap.keySet().stream().filter(key -> null != key).collect(Collectors.toList());
        List<BenchmarkPositionDo> bpList = benchmarkPositionService.selectByNames(collect);
        if (null == bpList || bpList.isEmpty()) {
            ipsd.setFail(dataList);
            return ipsd;
        }

        List<PostDto> insertList = getInsertList(bpList, nameMap);
        collect = nameMap.keySet().stream().collect(Collectors.toList());
        if (null != collect && !collect.isEmpty()) {
            importBatchSave(insertList);
            ipsd.setSuccess(insertList);
            List<PostDto> failList = new ArrayList<>();
            collect.forEach(value -> {
                failList.addAll(nameMap.get(value));
            });
            ipsd.setFail(failList);
            return ipsd;
        }

        importBatchSave(insertList);
        ipsd.setSuccess(insertList);
        return ipsd;
    }

    private void importBatchSave(List<PostDto> insertList) {
        insertList.forEach(node -> {
            PostDo postDoData = ObjectConverter.convert(node, PostDo.class);
            postDoData.setDeleted(false);
            postDoData.setJobGrade(new JobGradeRange());
            postDoData.setUpdateTime(node.getDataStartTime());
            postDoData.setCreateTime(node.getDataStartTime());

            node.setBid(this.save(postDoData));
        });
    }

    private Map<String, List<PostDto>> getNameGroupMap(List<PostDto> dataList) {
        Map<String, List<PostDto>> nameMap = new HashMap<>();
        dataList.forEach(pData -> {
            String bpn = pData.getBenchmarkPositionName();
            List list = nameMap.get(bpn);
            if (null == list) {
                list = new ArrayList();
                list.add(pData);
                nameMap.put(bpn, list);
                return;
            }

            list.add(pData);
            nameMap.put(bpn, list);
        });

        return nameMap;
    }

    private List<PostDto> getInsertList(List<BenchmarkPositionDo> bpList, Map<String, List<PostDto>> nameMap) {
        List<PostDto> insertList = new ArrayList<>();
        bpList.forEach(bpData -> {
            String bpn = bpData.getName();
            List<PostDto> list = nameMap.get(bpn);
            list.forEach(pd -> {
                pd.setBenchmarkPositionName(bpn);
                pd.setBenchmarkPositionId(bpData.getBid());
            });
            insertList.addAll(list);
            nameMap.remove(bpn);
        });
        return insertList;
    }

    public PageResult<PostDo> getPostPageListByOrgId(PostQueryDto pageQueryDto) {
        return postDomainService.selectPostPageListByOrgId(pageQueryDto);
    }

    public String syncSavePost(PostDo syncData) {
        String st;
        List<PostDo> postDos = selectPostDoByCodes(Lists.list(syncData.getCode()), DateUtil.getCurrentTimestamp());
        if (CollectionUtils.isNotEmpty(postDos)) {
            PostDo oldData = postDos.get(0);
            BeanUtil.copyWithNoValue(syncData, oldData);
            postDomainService.onlyUpdate(oldData);
            st = oldData.getBid();
        } else {
            st = postDomainService.onlySave(syncData);
        }
        return st;
    }

    public void sync(List<EntityDataChange> data) {
        postDomainService.sync(data);
    }

    public List<PostDo> selectByIds(List<String> bids, long datetime) {
        return postDomainService.selectByIds(bids, datetime);
    }

    /**
     * 岗位导出
     * @return
     */

    public List<TagProperty> installNewlySignedExportProperty() {
        List<TagProperty> list = new ArrayList<>();
        addTagPropertyToList(list, "orgName", "所属组织", 1);
        addTagPropertyToList(list, "name", "岗位名称", 2);
        addTagPropertyToList(list, "jobName", "关联的职务", 3);
        addTagPropertyToList(list, "benchmarkPositionName", "基准岗位", 4);
        addTagPropertyToList(list, "code", "岗位编码", 5);
        addTagPropertyToList(list, "keyPostTxt", "是否关键岗位", 6);
        addTagPropertyToList(list, "jobLevelName", "职级", 7);
        addTagPropertyToList(list, "jobLevel", "职等", 8);
        addTagPropertyToList(list, "statusTxt", "状态", 9);
        addTagPropertyToList(list, "dataStartTimeTxt", "生效日期", 10);


        return list;
    }

    public void addTagPropertyToList(List<TagProperty> list, String property, String propertyTxt, int order) {
        list.add(new TagProperty(property, propertyTxt, order));
    }
    public PageResult<PostVo> getEmpPageListFromExport(PostQueryDto dto) {
        dto.setPageSize(1000);
        PageResult<PostDo> pageResult;
        List<PostDo> doList = new ArrayList<>();
        do {
            pageResult = getPageResult(dto);
            doList.addAll(pageResult.getItems());
            dto.setPageNo(dto.getPageNo()+1);
        }while (CollectionUtils.isNotEmpty(pageResult.getItems()));

        List<PostVo> items = ObjectConvertUtil.convertList(doList, PostVo.class,
                (it, v1) -> {
//                    v1.setI18nName(FastjsonUtil.toObject(it.getI18nName(), Map.class));
                    v1.setKeyPostTxt(it.getKeyPost() == null?"":(it.getKeyPost() ==true ? "是" : "否"));
                    v1.setStatusTxt(it.getStatus().getText());
                    v1.setJobLevelName(it.getJobGrade().getStartGradeName());
                    v1.setJobLevel(it.getJobGrade().getStartLevel());
                    if (ObjectUtil.isNotEmpty(it.getDataStartTime())){
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        v1.setDataStartTimeTxt(sdf.format(new Date(it.getDataStartTime())));
                    }

                });
        return new PageResult(items, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal());
    }
}
