package com.caidaocloud.hr.service.organization.infrastructure.repository.impl;

import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.organization.domain.jobgrade.entity.JobGradeDo;
import com.caidaocloud.hr.service.organization.domain.jobgrade.repository.IJobGradeRepository;
import com.caidaocloud.hr.service.organization.infrastructure.repository.po.JobGradePo;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataDelete;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataInsert;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataUpdate;
import com.caidaocloud.util.ObjectConverter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/30
 */
@Repository
public class JobGradeRepositoryImpl implements IJobGradeRepository {
    @Override
    public JobGradeDo insert(JobGradeDo data) {
        String bid = DataInsert.identifier(data.getIdentifier()).insert(ObjectConverter.convert(data, JobGradePo.class));
        data.setBid(bid);
        return data;
    }

    @Override
    public int updateById(JobGradeDo data) {
        DataUpdate.identifier(data.getIdentifier()).update(ObjectConverter.convert(data, JobGradePo.class));
        return 0;
    }

    @Override
    public JobGradeDo selectById(String id, String identifier) {
        return ObjectConverter.convert(DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().oneOrNull(id, JobGradePo.class), JobGradeDo.class);
    }

    @Override
    public int delete(JobGradeDo data) {
        DataDelete.identifier(data.getIdentifier()).softDelete(data.getBid());
        return 0;
    }

    @Override
    public List<JobGradeDo> selectList(JobGradeDo data) {
        DataFilter dataFilter = DataFilter.eq("tenantId", data.getTenantId()).
                andNe("deleted", Boolean.TRUE.toString()).andEq("channelId", data.getChannelId());
        if (data.getStatus() != null && data.getStatus().getValue() != null) {
            dataFilter = dataFilter.andEq("status", data.getStatus().getValue());
        }
        return ObjectConverter.convertList(DataQuery.identifier(data.getIdentifier()).limit(5000, 1)
                .decrypt().specifyLanguage().queryInvisible().
                filter(dataFilter, JobGradePo.class).getItems(), JobGradeDo.class);
    }

    @Override
    public List<JobGradeDo> selectBatchIds(List<String> ids, String identifier) {
        return ObjectConverter.convertList(DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().
                filter(DataFilter.in("bid", ids), JobGradePo.class).getItems(), JobGradeDo.class);
    }

    @Override
    public List<JobGradeDo> selectAll(String identifier, Integer status) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId()).
                andEq("deleted", Boolean.FALSE.toString());
        if(null != status){
            dataFilter = dataFilter.andEq("status", status.toString());
        }

        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().limit(5000, 1).
                filter(dataFilter, JobGradeDo.class).getItems();
    }

    @Override
    public List<JobGradeDo> selectByCodes(List<String> codes, String identifier) {
        DataFilter dataFilter = DataFilter.eq("tenantId", UserContext.getTenantId()).
                andEq("deleted", Boolean.FALSE.toString());
        if (CollectionUtils.isNotEmpty(codes)) {
            dataFilter = dataFilter.andIn("jobGradeCode", codes);
        }
        return DataQuery.identifier(identifier).decrypt().specifyLanguage().queryInvisible().limit(5000, 1).
                filter(dataFilter, JobGradeDo.class).getItems();
    }

}
