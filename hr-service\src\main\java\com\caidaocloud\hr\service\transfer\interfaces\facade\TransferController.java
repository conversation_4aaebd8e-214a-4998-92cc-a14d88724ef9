package com.caidaocloud.hr.service.transfer.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.hr.service.temination.application.enums.WfTaskActionEnum;
import com.caidaocloud.hr.service.transfer.application.dto.TransferApplyDto;
import com.caidaocloud.hr.service.transfer.application.service.TransferRefreshService;
import com.caidaocloud.hr.service.transfer.application.service.TransferService;
import com.caidaocloud.hr.service.transfer.interfaces.dto.*;
import com.caidaocloud.hr.service.transfer.interfaces.vo.TransferApplyVo;
import com.caidaocloud.hr.service.transfer.interfaces.vo.TransferTemplateVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 31/1/2023 11:26 上午
 */
@Slf4j
@RestController
@RequestMapping("/api/hr/transfer/v1")
@Api(value = "/api/hr/transfer/v1", description = "异动管理", tags = "V4.0")
public class TransferController {

    @Resource
    private TransferService transferService;
    @Resource
    private TransferRefreshService transferRefreshService;

    @PostMapping("/template/data")
    @ApiOperation("获取异动表单初始数据")
    public Result<TransferTemplateVo> getTemplateData(@RequestBody TransferTemplateDto templateDto) {
        PreCheck.preCheckArgument(StringUtils.isEmpty(templateDto.getEmpId()), "获取数据失败，缺失必要参数");
        return Result.ok(transferService.getTemplateData(templateDto));
    }

    @PostMapping("/apply")
    @ApiOperation("异动表单申请")
    public Result<?> apply(@RequestBody TransferApplyDto transferApplyDto) {
        transferService.saveApply(transferApplyDto);
        return Result.ok("保存成功");
    }

    @PostMapping("/list")
    @ApiOperation("获取异动分页")
    public Result<PageResult<?>> list(@RequestBody TransferQueryDto dto) {
        return Result.ok(transferService.pageApply(dto));
    }

    @PostMapping("emp/page")
    @ApiOperation("获取员工异动")
    public Result<PageResult<Map<String, Object>>> getByEmp(@RequestBody TransferQueryDto dto) {
        return Result.ok(transferService.getTransferOfEmp(dto));
    }

    /**
     * 查询异动表单详情
     *
     * @param applyId
     * @param paasMode
     * @return
     */
    @GetMapping("/detail")
    @ApiOperation("异动表单详情")
    public Result<TransferApplyVo> detail(@RequestParam String applyId,
                                          @RequestParam(value = "paasMode", required = false, defaultValue = "false") boolean paasMode) {
        return Result.ok(transferService.detailVo(applyId, paasMode));
    }

    @GetMapping("/workflow/detail")
    @ApiOperation("异动表单工作流详情回调")
    public Result workflowDetail(@RequestParam String businessKey) {
        return Result.ok(transferService.detailVo(StringUtils.substringBefore(businessKey, "_")));
    }

    @PostMapping("/revoke")
    @ApiOperation("撤销异动申请")
    public Result<?> revoke(@RequestBody TransferWorkflowDto dto) {
        transferService.revokeApply(dto);
        return Result.ok();
    }

    @PostMapping("/reApply")
    @ApiOperation("异动表单重新发起")
    public Result reApply(@RequestBody TransferApplyDto transferApplyDto) {
        transferService.saveReApply(transferApplyDto);
        return Result.ok();
    }

    @ApiOperation("审批同意")
    @PostMapping("/approve")
    public Result approve(@RequestBody TransferApplyApprovalDto dto) {
        transferService.approve(dto);
        return Result.ok();
    }

    @ApiOperation("审批拒绝")
    @PostMapping("/refuse")
    public Result refuse(@RequestBody TransferApplyApprovalDto dto) {
        transferService.refuse(dto, WfTaskActionEnum.REFUSE);
        return Result.ok();
    }

    @ApiOperation("删除单据")
    @DeleteMapping("/rm")
    public Result remove(@RequestParam("applyId") String applyId) {
        transferService.remove(applyId);
        return Result.ok();
    }

    @ApiOperation("删除单据")
    @PostMapping("/rm")
    public Result batchRemove(@RequestBody List<String> applyIds) {
        transferService.batchRemove(applyIds);
        return Result.ok();
    }

    @ApiOperation("更新单据")
    @PostMapping("/update")
    public Result update(@RequestBody TransferUpdateDto updateDto) {
        transferService.update(updateDto);
        return Result.ok();
    }

    @ApiOperation("员工状态数据清洗")
    @PostMapping("/refresh")
    public Result refresh() {
        transferRefreshService.refresh();
        return Result.ok();
    }

    @GetMapping("/last")
    @ApiOperation("异动表单详情")
    public Result<TransferApplyVo> getLastApply(@RequestParam String empId) {
        return Result.ok(transferService.getLastApply(empId));
    }

    @PostMapping("terminate")
    @ApiOperation("终止异动")
    public Result terminateApply(@RequestBody TerminateTransferDto terminateTransferDto) {
        transferService.terminateTransfer(terminateTransferDto);
        return Result.ok();
    }
}