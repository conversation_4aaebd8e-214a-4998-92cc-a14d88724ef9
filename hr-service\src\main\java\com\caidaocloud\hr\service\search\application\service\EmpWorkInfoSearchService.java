package com.caidaocloud.hr.service.search.application.service;

import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.service.common.base.DataScopeService;
import com.caidaocloud.hr.service.common.infrastructure.utils.EsclientrhlUtil;
import com.caidaocloud.hr.service.dict.DictService;
import com.caidaocloud.hr.service.dto.auth.AuthRoleScopeFilterDetail;
import com.caidaocloud.hr.service.dto.auth.EsOperate;
import com.caidaocloud.hr.service.dto.auth.EsScopeQuery;
import com.caidaocloud.hr.service.dto.ruleset.EmpRuleSetDto;
import com.caidaocloud.hr.service.employee.application.common.constant.MsgCodeConstant;
import com.caidaocloud.hr.service.employee.application.emp.dto.EmpStatisticsDto;
import com.caidaocloud.hr.service.employee.application.emp.dto.StatisticsBaseDto;
import com.caidaocloud.hr.service.employee.application.emp.fieldset.service.EmpDynamicService;
import com.caidaocloud.hr.service.employee.domain.base.enums.ResignationStatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.enums.StatusEnum;
import com.caidaocloud.hr.service.employee.domain.base.util.LangUtil;
import com.caidaocloud.hr.service.employee.domain.base.util.UserContext;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpConcurrentPostDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPostRecordDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.entity.EmpPrivateInfoDo;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpPostRecordDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.EmpPrivateInfoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.manage.service.WorknoAutoDomainService;
import com.caidaocloud.hr.service.employee.domain.emp.ruleset.service.EmpRuleSetDomainService;
import com.caidaocloud.hr.service.employee.interfaces.dto.emp.manage.EmpPageQueryDto;
import com.caidaocloud.hr.service.enums.ruleset.EmpListOrderEnum;
import com.caidaocloud.hr.service.enums.system.EmpStatusEnum;
import com.caidaocloud.hr.service.enums.system.RegularStatusEnum;
import com.caidaocloud.hr.service.enums.system.WorkStatusEnum;
import com.caidaocloud.hr.service.organization.application.org.dto.OrgLabelData;
import com.caidaocloud.hr.service.organization.application.org.service.OrgService;
import com.caidaocloud.hr.service.search.application.dto.EmpSearchInfoDto;
import com.caidaocloud.hr.service.search.application.dto.EntityDataChangeDto;
import com.caidaocloud.hr.service.search.application.event.EntityDataChange;
import com.caidaocloud.hr.service.search.infrastructure.repository.EmpSearchInfoRepository;
import com.caidaocloud.hr.service.search.infrastructure.repository.po.EmpConcurrentPostSearchPo;
import com.caidaocloud.hr.service.search.infrastructure.repository.po.EmpSearchInfoPo;
import com.caidaocloud.hr.service.search.infrastructure.repository.po.EmpTagPo;
import com.caidaocloud.hr.service.util.EntityDataUtil;
import com.caidaocloud.hrpaas.metadata.sdk.dto.DictSimple;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EntityDataDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.LabelData;
import com.caidaocloud.hrpaas.metadata.sdk.dto.TreeData;
import com.caidaocloud.hrpaas.metadata.sdk.enums.AuthRoleScopeRestriction;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONTokener;
import org.springframework.stereotype.Service;

import org.jetbrains.annotations.NotNull;
import org.zxp.esclientrhl.enums.AggsType;
import org.zxp.esclientrhl.repository.ElasticsearchTemplate;
import org.zxp.esclientrhl.repository.PageList;
import org.zxp.esclientrhl.repository.PageSortHighLight;
import org.zxp.esclientrhl.repository.Sort;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpWorkInfoSearchService {
    @Value("${caidaocloud.dataScope.empList:false}")
    private boolean dataFilter;
    @Resource
    private EmpSearchInfoRepository empSearchInfoRepository;
    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;
    @Resource
    private OrgService orgService;
    @Resource
    private EmpPrivateInfoDomainService empPrivateInfoDomainService;
    @Resource
    private DictService dictService;
    @Resource
    private DataScopeService dataScopeService;
    @Resource
    private EmpPostRecordDomainService empPostRecordDomainService;
    @Resource
    private WorknoAutoDomainService worknoAutoDomainService;
    @Resource
    private EmpDynamicService empDynamicColumnService;
    @Resource
    private EsclientrhlUtil esclientrhlUtil;
    @Resource
    private EmpRuleSetDomainService empRuleSetDomainService;

    public void dataChangeHandle(List<EntityDataChange> data) throws Exception {
        EntityDataChangeDto<EmpSearchInfoDto> entityDataChangeDto = new EntityDataChangeDto<>();
        List concurrentPostList = new ArrayList(50), empTags = new ArrayList(50);
        Map<String, RegularStatusEnum> regularMap = new HashMap<>();
        for (RegularStatusEnum c : RegularStatusEnum.values()) {
            regularMap.put(String.valueOf(c.getIndex()), c);
        }
        Map<String, EmpStatusEnum> statusMap = new HashMap<>();
        for (EmpStatusEnum c : EmpStatusEnum.values()) {
            statusMap.put(String.valueOf(c.getIndex()), c);
        }
        data.stream().forEach(entityData -> {
            EntityDataDto after = entityData.getAfter();
            EntityDataDto before = entityData.getBefore();
            EmpSearchInfoDto empData;
            if (null == before && null != after) {
                // 新增
                empData = EntityDataUtil.convertEntityData(after, EmpSearchInfoDto.class);
                empDynamicColumnService.buildEmpEsData(entityDataChangeDto, concurrentPostList, regularMap, statusMap, empData, empTags);
                entityDataChangeDto.getInsertDataList().add(empData);
                log.info("empData info show,{}:", empData);
            } else if (after != null && !after.isDeleted()) {
                // 修改
                empData = EntityDataUtil.convertEntityData(after, EmpSearchInfoDto.class);
                empDynamicColumnService.buildEmpEsData(entityDataChangeDto, concurrentPostList, regularMap, statusMap, empData, empTags);
                entityDataChangeDto.getUpdateDataList().add(empData);
            }
        });
        List<EmpSearchInfoDto> saveListData = Lists.newArrayList();
        // insertDataList、updateDataList 排重
        List unique = uniqueEmpDataList(entityDataChangeDto.getInsertDataList());
        entityDataChangeDto.setInsertDataList(unique);
        saveListData.addAll(unique);
        unique = uniqueEmpDataList(entityDataChangeDto.getUpdateDataList());
        entityDataChangeDto.setUpdateDataList(unique);
        saveListData.addAll(unique);
        List<EmpSearchInfoPo> dataList = ObjectConverter.convertList(saveListData, EmpSearchInfoPo.class);
        // 保存新增的数据到 es
        esclientrhlUtil.saveOrUpdate(dataList);
        String tenantId = null, workno = null;
        for (EmpSearchInfoPo po : dataList) {
            tenantId = po.getTenantId();
            workno = po.getWorkno();
            EmpPostRecordDo convert = ObjectConverter.convert(po, EmpPostRecordDo.class);
            convert.setStartTime(po.getDataStartTime());
            empPostRecordDomainService.saveData(convert, po.getLeaveDate(), po.getHireDate());
        }
        worknoAutoDomainService.updateWorknoMax(tenantId, workno);
    }

    private List uniqueEmpDataList(List<EmpSearchInfoDto> dataList) {
        List unique = dataList.stream().collect(
                Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EmpSearchInfoDto::getId))), ArrayList::new)
        );
        return unique;
    }

    /**
     * 同步员工的兼岗信息到 ES
     *
     * @param empId      员工ID
     * @param postDoList 员工兼岗列表
     */
    public void dataChangeSyncEs(String empId, List<EmpConcurrentPostDo> postDoList) {
        try {
            List<EmpConcurrentPostSearchPo> concurrentPost = ObjectConverter.convertList(postDoList, EmpConcurrentPostSearchPo.class);
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery().must(QueryBuilders.matchQuery("empId", empId)).must(QueryBuilders.matchQuery("tenantId", UserContext.getTenantId()));
            List<EmpSearchInfoPo> pageEs = empSearchInfoRepository.search(boolQuery);
            if (null != pageEs) {
                pageEs.forEach(empData -> empData.setConcurrentPost(concurrentPost));
                // 覆盖索引更新
                elasticsearchTemplate.saveBatch(pageEs);
            }
        } catch (Exception e) {
            log.error("data = [{}] Change Sync Es err,{}", FastjsonUtil.toJson(postDoList), e.getMessage(), e);
        }
    }

    /**
     * 同步员工的标签信息到 ES
     *
     * @param empId   员工ID
     * @param empTags 员工标签列表
     */
    public void dataChangeEmpTagsSyncEs(String empId, List<EmpTagPo> empTags) {
        if (null == empTags || empTags.isEmpty()) {
            return;
        }
        try {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("empId", empId))
                    .must(QueryBuilders.matchQuery("tenantId", UserContext.getTenantId()));
            List<EmpSearchInfoPo> pageEs = empSearchInfoRepository.search(boolQuery);
            if (null != pageEs) {
                pageEs.forEach(empData -> empData.setEmpTags(empTags));
                // 覆盖索引更新
                elasticsearchTemplate.saveBatch(pageEs);
            }
        } catch (Exception e) {
            log.error("data = [{}] empTags Change Sync Es err,{}", FastjsonUtil.toJson(empTags), e.getMessage(), e);
        }
    }

    /**
     * 同步员工个人信息到ES
     *
     * @param data 员工个人信息
     */
    public void syncEmpPrivateInfo(EmpPrivateInfoDo data) {
        try {
            List<EmpSearchInfoPo> list = empSearchInfoRepository.search(QueryBuilders.matchQuery("empId", data.getEmpId()));
            EmpSearchInfoPo empSearchInfo = null;
            for (EmpSearchInfoPo po : list) {
                if (po.getDataEndTime() > System.currentTimeMillis()) {
                    empSearchInfo = po;
                    break;
                }
            }
            if (null == empSearchInfo) {
                return;
            }
            // 更新员工的姓名，性别
            empSearchInfo.setEmpId(data.getEmpId());
            empSearchInfo.setName(data.getName());
            empSearchInfo.setEnName(data.getEnName());
            empSearchInfo.setUpdateTime(data.getUpdateTime());
            empSearchInfo.setUpdateBy(data.getUpdateBy());
            DictSimple sex = data.getSex();
            if (null == sex) {
                empSearchInfo.setSex("");
                empSearchInfo.setSexText("");
            } else {
                empSearchInfo.setSex(sex.getValue());
                if (StringUtil.isEmpty(sex.getText())) {
                    String genderDictCode = "Gender";
                    Map<String, KeyValue> map = getEmployDictMap(genderDictCode);
                    for (String key : map.keySet()) {
                        if (map.get(key).getValue().equals(sex.getValue())) {
                            sex.setText(key);
                            break;
                        }
                    }
                }
                empSearchInfo.setSexText(sex.getText());
            }
            empSearchInfoRepository.save(empSearchInfo);
        } catch (Exception e) {
            log.error("data = [{}] EmpPrivateInfo Change Sync Es err,{}", FastjsonUtil.toJson(data), e.getMessage(), e);
        }
    }

    private Map<String, KeyValue> getEmployDictMap(String typeCode) {
        List<KeyValue> list = dictService.getEnableDictList(typeCode, "Employee");
        Map<String, KeyValue> map = new HashMap<>();
        for (KeyValue kv : list) {
            map.put(kv.getText(), kv);
        }
        return map;
    }

    private void putIfNotExsit(Map<String, Object> map, String key, String obj) throws JSONException {
        if (map.get(key) == null) {
            if (null == obj || "".equals(obj)) {
                return;
            }
            Object json = new JSONTokener(obj).nextValue();
            if (json instanceof org.springframework.boot.configurationprocessor.json.JSONObject || json instanceof JSONArray) {
                map.put(key, json);
            } else {
                map.put(key, obj);
            }
        }
    }

    private List<String> getOrganizeWithChild(String organize, StatusEnum status) {
        val tree = orgService.getCompanyTree(System.currentTimeMillis(), "", status);
        List<String> orgIds = new ArrayList<String>();
        for (TreeData<OrgLabelData> data : tree) {
            checkOrganizeId(data, organize, orgIds);
        }
        if (orgIds.size() == 0) {
            orgIds.add(organize);
        }
        return orgIds;
    }

    private <T extends LabelData> void checkOrganizeId(TreeData<T> data, String organize, List<String> orgIds) {
        List<TreeData<T>> child = data.getChildren();
        if (data.getData().getBid().equals(organize)) {
            orgIds.add(organize);
            if (child != null && child.size() > 0) {
                for (TreeData<T> childData : child) {
                    checkOrganizeId(childData, childData.getData().getBid(), orgIds);
                }
            }
        } else {
            if (child != null && child.size() > 0) {
                for (TreeData<T> childData : child) {
                    checkOrganizeId(childData, organize, orgIds);
                }
            }
        }
    }


    public PageResult<EmpSearchInfoPo> selectPage(EmpPageQueryDto dto) {
        BoolQueryBuilder boolQuery = pageQueryBuilder(dto);
        PageList<EmpSearchInfoPo> page = null;
        try {
            PageSortHighLight pageParam = new PageSortHighLight(dto.getPageNo(), dto.getPageSize());
            boolean totalFlag = null != dto.getKeywords() && dto.getKeywords().trim().length() > 0;
            if (null == dto.getKeywords() || dto.getKeywords().trim().length() == 0) {
//                Sort.Order order = new Sort.Order(SortOrder.DESC, "updateTime");
//                Sort.Order desc = new Sort.Order(SortOrder.DESC, "empId.keyword");
//                Sort sort = new Sort(order, desc);
                EmpRuleSetDto ruleSet = empRuleSetDomainService.getRuleSet();
                if (ruleSet.getEmpListOrderEnum() == null) {
                    ruleSet.setEmpListOrderEnum(EmpListOrderEnum.UPDATE_TIME_DESC);
                }
                pageParam.setSort(getEmpSort(ruleSet.getEmpListOrderEnum()));
            }
            page = empSearchInfoRepository.search(boolQuery, pageParam);
            long count = totalFlag ? page.getTotalElements() : empSearchInfoRepository.count(boolQuery);
            int pages = ((Long) (count / page.getPageSize())).intValue();
            page.setTotalElements(count);
            page.setTotalPages(pages);
        } catch (Exception e) {
            log.error("search by es err,{}", e.getMessage(), e);
            if (e instanceof ElasticsearchStatusException) {
                page = new PageList<>();
            } else {
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30034));
            }
        }

        return new PageResult<>(page.getList(), page.getCurrentPage(), page.getPageSize(), (int) page.getTotalElements());
    }

    @NotNull
    public BoolQueryBuilder pageQueryBuilder(EmpPageQueryDto dto) {
        String tenantId = UserContext.getTenantId();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.matchQuery("tenantId", tenantId)).must(QueryBuilders.matchPhraseQuery("deleted", false));
        RangeQueryBuilder startQuery = QueryBuilders.rangeQuery("dataStartTime").lte(dto.getDateTime());
        RangeQueryBuilder endQuery = QueryBuilders.rangeQuery("dataEndTime").gte(dto.getDateTime());
        boolQuery.must(startQuery).must(endQuery);
        if (dto.isConcurrentPost()) {
            RangeQueryBuilder postStartQuery = QueryBuilders.rangeQuery("concurrentPost.startDate")
                    .lte(dto.getDateTime());
            RangeQueryBuilder postEndQuery = QueryBuilders.rangeQuery("concurrentPost.endDate").gte(dto.getDateTime());
            boolQuery.must(postStartQuery).must(postEndQuery);
        }

        if (null != dto.getOrganize() && !"0".equals(dto.getOrganize())) {
            List<String> orgIds = getOrganizeWithChild(dto.getOrganize(), null);
            boolQuery.must(QueryBuilders.termsQuery("organize", orgIds));
        }

        if (null != dto.getKeywords() && dto.getKeywords().trim().length() > 0) {
            BoolQueryBuilder kwBoolQuery = QueryBuilders.boolQuery().
                    should(QueryBuilders.matchQuery("name.keyword", dto.getKeywords())).
                    should(QueryBuilders.matchQuery("workno", dto.getKeywords())).
                    should(QueryBuilders.wildcardQuery("name.keyword", "*" + dto.getKeywords() + "*")).
                    should(QueryBuilders.wildcardQuery("workno", "*" + dto.getKeywords() + "*"))
                    .minimumShouldMatch(1);
            boolQuery.must(kwBoolQuery);
        }

        if (dto.getLeaveDate() != null) {
            var leaveDateQuery = QueryBuilders.matchQuery("leaveDate", dto.getLeaveDate());
            boolQuery.must(leaveDateQuery);
        }

        if (dto.getEmpType() != null) {
            var empTypeQuery = QueryBuilders.matchQuery("empTypeValue", dto.getEmpType());
            boolQuery.must(empTypeQuery);
        }

        if (dto.getResignationStatus() != null) {
            var resignationStatusQuery = QueryBuilders.matchQuery("resignationStatus", dto.getResignationStatus());
            boolQuery.must(resignationStatusQuery);
        }

        handleFilterElement(boolQuery, dto);

        doDataScope(Long.valueOf(UserContext.getUserId()), boolQuery);
        return boolQuery;
    }

    private Sort getEmpSort(EmpListOrderEnum orderEnum) {
        SortOrder orderBy = SortOrder.ASC;
        if ("desc".equals(orderEnum.getOrderStr())) {
            orderBy = SortOrder.DESC;
        }
        Sort.Order order = new Sort.Order(orderBy, orderEnum.getProperty());
        return new Sort(order);
    }

    /**
     * 导出用的查询接口
     *
     * @param dto
     * @return
     */
    public PageResult<EmpSearchInfoPo> selectPageForExport(EmpPageQueryDto dto) {
        String tenantId = UserContext.getTenantId();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.matchQuery("tenantId", tenantId));
        RangeQueryBuilder startQuery = QueryBuilders.rangeQuery("dataStartTime").lte(dto.getDateTime());
        RangeQueryBuilder endQuery = QueryBuilders.rangeQuery("dataEndTime").gte(dto.getDateTime());
        boolQuery.must(startQuery).must(endQuery);
        if (dto.isConcurrentPost()) {
            RangeQueryBuilder postStartQuery = QueryBuilders.rangeQuery("concurrentPost.startDate").lte(dto.getDateTime());
            RangeQueryBuilder postEndQuery = QueryBuilders.rangeQuery("concurrentPost.endDate").gte(dto.getDateTime());
            boolQuery.must(postStartQuery).must(postEndQuery);
        }

        if (null != dto.getOrganize() && !"0".equals(dto.getOrganize())) {
            List<String> orgIds = getOrganizeWithChild(dto.getOrganize(), StatusEnum.ENABLED);
            boolQuery.must(QueryBuilders.termsQuery("organize", orgIds));
        }

        boolean totalFlag = false;
        if (null != dto.getKeywords() && dto.getKeywords().trim().length() > 0) {
            totalFlag = true;
            BoolQueryBuilder kwBoolQuery = QueryBuilders.boolQuery().
                    should(QueryBuilders.matchQuery("name.keyword", dto.getKeywords())).
                    should(QueryBuilders.matchQuery("workno.keyword", dto.getKeywords())).
                    should(QueryBuilders.wildcardQuery("name.keyword", "*" + dto.getKeywords() + "*")).
                    should(QueryBuilders.wildcardQuery("workno.keyword", "*" + dto.getKeywords() + "*")).minimumShouldMatch(1);
            boolQuery.must(kwBoolQuery);
        }

        if (dto.getLeaveDate() != null) {
            var leaveDateQuery = QueryBuilders.matchQuery("leaveDate", dto.getLeaveDate());
            boolQuery.must(leaveDateQuery);
        }

        if (dto.getEmpType() != null) {
            var empTypeQuery = QueryBuilders.matchQuery("empTypeValue", dto.getEmpType());
            boolQuery.must(empTypeQuery);
        }

        if (dto.getResignationStatus() != null) {
            var resignationStatusQuery = QueryBuilders.matchQuery("resignationStatus", dto.getResignationStatus());
            boolQuery.must(resignationStatusQuery);
        }

        handleFilterElement(boolQuery, dto);

        doDataScope(Long.valueOf(UserContext.getUserId()), boolQuery);
        PageList<EmpSearchInfoPo> page = null;
        List<EmpSearchInfoPo> list = new ArrayList<>();
        try {
            Integer pageNo = 1;
            Integer pageSize = 1000;
            PageSortHighLight pageParam = new PageSortHighLight(pageNo, pageSize);
            if (null == dto.getKeywords() || dto.getKeywords().trim().length() == 0) {
                Sort.Order order = new Sort.Order(SortOrder.DESC, "updateTime");
                Sort.Order desc = new Sort.Order(SortOrder.DESC, "empId.keyword");
                Sort sort = new Sort(order, desc);
                pageParam.setSort(sort);
            }
            do {
                page = empSearchInfoRepository.search(boolQuery, pageParam);
                list.addAll(page.getList());
                if (list.size() > 0)
                    boolQuery.must(QueryBuilders.rangeQuery("updateTime").lt(list.get(list.size() - 1).getUpdateTime()));
            } while (CollectionUtils.isNotEmpty(page.getList()));

        } catch (Exception e) {
            log.error("search by es err,{}", e.getMessage(), e);
            if (e instanceof ElasticsearchStatusException) {
                page = new PageList<>();
            } else {
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30034));
            }
        }

        return new PageResult<>(list, 1, Integer.MAX_VALUE, list.size());
    }


    /**
     * 筛选字段处理
     *
     * @param boolQuery
     * @param dto
     */
    private void handleFilterElement(BoolQueryBuilder boolQuery, EmpPageQueryDto dto) {
        if (CollectionUtils.isEmpty(dto.getFilters())) {
            return;
        }
        for (FilterElement filterElement : dto.getFilters()) {
            if ("hireDate".equals(filterElement.getProp())) {
                String[] hireDate = String.valueOf(filterElement.getValue()).split(",");
                if (hireDate.length == 2) {
                    RangeQueryBuilder postStartQuery = QueryBuilders.rangeQuery("hireDate").gte(hireDate[0]);
                    RangeQueryBuilder postEndQuery = QueryBuilders.rangeQuery("hireDate").lte(hireDate[1]);
                    boolQuery.must(postStartQuery).must(postEndQuery);
                }
            }
            if ("leaveDate".equals(filterElement.getProp())) {
                String[] leaveDate = String.valueOf(filterElement.getValue()).split(",");
                if (leaveDate.length == 2) {
                    RangeQueryBuilder postStartQuery = QueryBuilders.rangeQuery("leaveDate").gte(leaveDate[0]);
                    RangeQueryBuilder postEndQuery = QueryBuilders.rangeQuery("leaveDate").lte(leaveDate[1]);
                    boolQuery.must(postStartQuery).must(postEndQuery);
                }
            }
            if ("leaderEmpName".equals(filterElement.getProp())) {
                var empStatusQuery = QueryBuilders.wildcardQuery("leadEmpId.name.keyword", "*" + filterElement.getValue() + "*");
                boolQuery.must(empStatusQuery);
            }
            if ("jobGrade".equals(filterElement.getProp())) {
                val list = FastjsonUtil.toObject(String.valueOf(filterElement.getValue()), List.class);
                var empStatusQuery = QueryBuilders.termsQuery("jobGrade.startGrade", list);
                boolQuery.must(empStatusQuery);
            }
            if ("benchPostTxt".equals(filterElement.getProp())) {
                var empStatusQuery = QueryBuilders.wildcardQuery("benchPostTxt.keyword", "*" + filterElement.getValue() + "*");
                boolQuery.must(empStatusQuery);
            }
            if ("empStatus".equals(filterElement.getProp())) {
                String[] empStatus = String.valueOf(filterElement.getValue()).split(",");
                var empStatusQuery = QueryBuilders.termsQuery("empStatusValue", empStatus);
                boolQuery.must(empStatusQuery);
            }
            if ("posts".equals(filterElement.getProp())) {
                String[] posts = String.valueOf(filterElement.getValue()).split(",");
                var empStatusQuery = QueryBuilders.termsQuery("post", posts);
                boolQuery.must(empStatusQuery);
            }
            if ("jobs".equals(filterElement.getProp())) {
                String[] jobs = String.valueOf(filterElement.getValue()).split(",");
                var empStatusQuery = QueryBuilders.termsQuery("job", jobs);
                boolQuery.must(empStatusQuery);
            }
            if ("empTypeValues".equals(filterElement.getProp())) {
                String[] empTypeValues = String.valueOf(filterElement.getValue()).split(",");
                var empStatusQuery = QueryBuilders.termsQuery("empTypeValue", empTypeValues);
                boolQuery.must(empStatusQuery);
            }
            if ("companys".equals(filterElement.getProp())) {
                String[] companys = String.valueOf(filterElement.getValue()).split(",");
                var empStatusQuery = QueryBuilders.termsQuery("company", companys);
                boolQuery.must(empStatusQuery);
            }
            if ("workplaces".equals(filterElement.getProp())) {
                String[] workplaces = String.valueOf(filterElement.getValue()).split(",");
                var empStatusQuery = QueryBuilders.termsQuery("workplace", workplaces);
                boolQuery.must(empStatusQuery);
            }
            if ("tags".equals(filterElement.getProp())) {
                List<String> tags = (List<String>) filterElement.getValue();
                var empStatusQuery = QueryBuilders.termsQuery("empTags.value", tags);
                boolQuery.must(empStatusQuery);
            }

        }
    }


    private void doDataScope(Long userId, BoolQueryBuilder boolQuery) {
        if (!dataFilter) {
            return;
        }
        List<AuthRoleScopeFilterDetail> dataList = dataScopeService.getScopeList("entity.hr.EmpWorkInfo", userId, "");
        Map<AuthRoleScopeRestriction, EsScopeQuery> queryMap = loadEsScopeQueryMap();
        dataScopeService.getScopeQuery(dataList, boolQuery, queryMap);
    }

    private Map<AuthRoleScopeRestriction, EsScopeQuery> loadEsScopeQueryMap() {
        Map<AuthRoleScopeRestriction, EsScopeQuery> queryMap = new HashMap<>(30);
        // 指定用工类型
        queryMap.put(AuthRoleScopeRestriction.SELECTED_EMP_TYPE, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empTypeValue.keyword"));
        queryMap.put(AuthRoleScopeRestriction.CREATED_BY_MYSELF, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("createBy.keyword"));
        // 指定合同公司
        queryMap.put(AuthRoleScopeRestriction.SELECTED_COMPANY, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("company.keyword"));
        // 查看本组织及下级组织
        queryMap.put(AuthRoleScopeRestriction.MY_ORG_AND_BELONGINGS, new EsScopeQuery().setOp(EsOperate.WILDCARD).setProperty("organizePath.keyword"));
        // 查看指定组织
        queryMap.put(AuthRoleScopeRestriction.SELECTED_ORG, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize.keyword"));
        // 查看指定组织及下级组织
        queryMap.put(AuthRoleScopeRestriction.SELECTED_ORG_AND_BELONGINGS, new EsScopeQuery().setOp(EsOperate.WILDCARD).setProperty("organizePath.keyword"));
        // 查看所属兼岗组织
        queryMap.put(AuthRoleScopeRestriction.MY_CONCURRENT_ORG, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize.keyword"));
        // 查看所属兼岗组织及下级组织
        queryMap.put(AuthRoleScopeRestriction.MY_CONCURRENT_ORG_AND_BELONGINGS, new EsScopeQuery().setOp(EsOperate.WILDCARD).setProperty("organizePath.keyword"));
        // 查看本组织
        queryMap.put(AuthRoleScopeRestriction.MY_ORG, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize.keyword"));
        // 按HRBP 查看（选择所属组织的对应HRBP等于**的员工）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_HRBP, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize.keyword"));
        // 按部门负责人查看
        queryMap.put(AuthRoleScopeRestriction.SELECTED_LEADER, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize.keyword"));
        // 直接下级
        queryMap.put(AuthRoleScopeRestriction.DIRECT_SUBORDINATE, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId.keyword"));
        // 按HRBP 查看（含兼岗）（选择所属组织的对应HRBP等于**的员工含兼岗）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_HRBP_WITH_CONCURRENT, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId.keyword"));
        // 按部门负责人查看（含兼岗）（选择所属组织的对应负责人等于**的员工含兼岗）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_LEADER_WITH_CONCURRENT, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId.keyword"));
        // 直接下级含兼岗
        queryMap.put(AuthRoleScopeRestriction.DIRECT_SUBORDINATE_WITH_CONCURRENT, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId.keyword"));
        // 查看指定员工（选择指定员工，支持多选）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_EMP, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId.keyword"));
        // 查看本人
        queryMap.put(AuthRoleScopeRestriction.MYSELF, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId.keyword"));
        // 指定工作地（参数：下拉多选工作地，按照员工身上的工作地圈人）
        queryMap.put(AuthRoleScopeRestriction.SELECTED_WORKPLACE, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("workplace.keyword"));
        queryMap.put(AuthRoleScopeRestriction.NO_AUTH, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId.keyword"));
        queryMap.put(AuthRoleScopeRestriction.SPECIFIED_ORG_CODE_PREFIX, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("organize.keyword"));
        queryMap.put(AuthRoleScopeRestriction.SELECTED_CONTRACT_TYPE, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("contractType.value.keyword"));
        queryMap.put(AuthRoleScopeRestriction.SELECTED_EMP_STATUS, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empStatusValue.keyword"));
        queryMap.put(AuthRoleScopeRestriction.SELECTED_POST, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId"));
        queryMap.put(AuthRoleScopeRestriction.SELECTED_BENCH_POST, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId"));
        queryMap.put(AuthRoleScopeRestriction.SELECTED_COST_CENTER, new EsScopeQuery().setOp(EsOperate.TERMS).setProperty("empId"));
        return queryMap;
    }

    public EmpStatisticsDto getEmpStatisticsByQueryDto(EmpPageQueryDto dto) {
        EmpStatisticsDto statisticsDto = new EmpStatisticsDto();
        List<StatisticsBaseDto> workStatusList = Lists.newArrayList();
        try {
            UserInfo userInfo = UserContext.preCheckUser();
            String tenantId = userInfo.getTenantId();
            Long userId = userInfo.getUserId();
            List<String> orgIds = new ArrayList<>();
            if (null != dto.getOrganize() && !"0".equals(dto.getOrganize())) {
                orgIds = getOrganizeWithChild(dto.getOrganize(), StatusEnum.ENABLED);
            }
            RangeQueryBuilder postStartQuery = QueryBuilders.rangeQuery("concurrentPost.startDate").lte(dto.getDateTime());
            RangeQueryBuilder postEndQuery = QueryBuilders.rangeQuery("concurrentPost.endDate").gte(dto.getDateTime());
            RangeQueryBuilder startQuery = QueryBuilders.rangeQuery("dataStartTime").lte(dto.getDateTime());
            RangeQueryBuilder endQuery = QueryBuilders.rangeQuery("dataEndTime").gte(dto.getDateTime());
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("tenantId", tenantId))
                    .must(startQuery).must(endQuery).must(postStartQuery).must(postEndQuery);
            if (null != dto.getOrganize() && !"0".equals(dto.getOrganize())) {
                boolQuery.must(QueryBuilders.termsQuery("organize", orgIds));
            }

            List<AuthRoleScopeFilterDetail> dataList = dataScopeService.getScopeList("entity.hr.EmpWorkInfo", userId, "");
            Map<AuthRoleScopeRestriction, EsScopeQuery> queryMap = loadEsScopeQueryMap();
            BoolQueryBuilder sqb = dataScopeService.getScopeQuery(dataList, queryMap);
            if (null != sqb) {
                boolQuery.must(sqb);
            }
            long concurrentPostCount = empSearchInfoRepository.count(boolQuery);
            boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("tenantId", tenantId))
                    .must(startQuery).must(endQuery);
            if (null != dto.getOrganize() && !"0".equals(dto.getOrganize())) {
                boolQuery.must(QueryBuilders.termsQuery("organize", orgIds));
            }
            if (null != sqb) {
                boolQuery.must(sqb);
            }
            long majorPostCount = empSearchInfoRepository.count(boolQuery);
            // DEV-12041 总条数 = 主岗 + 兼岗
            // majorPostCount = majorPostCount - concurrentPostCount;
            // 正岗信息统计
            workStatusList.add(new StatisticsBaseDto(WorkStatusEnum.MAJOR_POST.getIndex().toString()
                    , WorkStatusEnum.MAJOR_POST.getName(), String.valueOf(majorPostCount)));
            // 兼岗信息统计
            workStatusList.add(new StatisticsBaseDto(WorkStatusEnum.CONCURRENT_POST.getIndex().toString()
                    , WorkStatusEnum.CONCURRENT_POST.getName(), String.valueOf(concurrentPostCount)));

            statisticsDto.setWorkStatus(workStatusList);
            boolQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("tenantId", tenantId))
                    .must(startQuery).must(endQuery);
            if (dto.getOrganize() != null && !"0".equals(dto.getOrganize())) {
                boolQuery.must(QueryBuilders.termsQuery("organize", orgIds));
            }
            if (null != sqb) {
                boolQuery.must(sqb);
            }
            Map map = empSearchInfoRepository.aggs("id", AggsType.count, boolQuery, "empTypeValue");
            // 员工类型
            List<StatisticsBaseDto> empTypeList = Lists.newArrayList();
            List<KeyValue> empTypeDictList = dictService.getEnableDictList("EmployType", "Employee");
            if (CollectionUtils.isNotEmpty(empTypeDictList)) {
                Map finalMap = map;
                empTypeList = empTypeDictList.stream()
                        .map(o -> new StatisticsBaseDto(o.getValue().toString(), o.getText(),
                                finalMap.get(o.getValue().toString()) == null ? "0" : finalMap.get(o.getValue().toString()).toString()))
                        .collect(Collectors.toList());
            }
            statisticsDto.setEmpType(empTypeList);
            if (null != sqb) {
                boolQuery.must(sqb);
            }
            map = empSearchInfoRepository.aggs("id", AggsType.count, boolQuery, "empStatusValue");
            // 员工状态
            List<StatisticsBaseDto> empStatusList = Lists.newArrayList();
            Map finalMap = map;
            for (EmpStatusEnum c : EmpStatusEnum.values()) {
                if (c == EmpStatusEnum.LEAVE_JOB) {
                    continue;
                }
                empStatusList.add(new StatisticsBaseDto(c.getIndex().toString(), c.getName(),
                        finalMap.get(c.getIndex().toString()) == null ? "0" : finalMap.get(c.getIndex().toString()).toString()));
            }
            statisticsDto.setEmpStatus(empStatusList);
            statisticsDto.setResignationStatus(queryResignationStatusStatistics(boolQuery));
        } catch (Exception e) {
            log.error("search es err,{}", e.getMessage(), e);
            if (e instanceof ElasticsearchStatusException) {
                workStatusList.add(new StatisticsBaseDto(WorkStatusEnum.MAJOR_POST.getIndex().toString()
                        , WorkStatusEnum.MAJOR_POST.getName(), String.valueOf(0)));
                // 兼岗信息统计
                workStatusList.add(new StatisticsBaseDto(WorkStatusEnum.CONCURRENT_POST.getIndex().toString()
                        , WorkStatusEnum.CONCURRENT_POST.getName(), String.valueOf(0)));
                statisticsDto.setWorkStatus(workStatusList);
                List<StatisticsBaseDto> empStatusList = Lists.newArrayList();
                for (EmpStatusEnum c : EmpStatusEnum.values()) {
                    empStatusList.add(new StatisticsBaseDto(c.getIndex().toString(), c.getName(), "0"));
                }
                statisticsDto.setEmpStatus(empStatusList);
                return statisticsDto;
            }
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.HR_ERROR_CODE_30033));
        }
        return statisticsDto;
    }

    /**
     * 离职状态统计
     *
     * @param boolQuery
     * @return
     */
    @SneakyThrows
    private List<StatisticsBaseDto> queryResignationStatusStatistics(BoolQueryBuilder boolQuery) {
        Map map = empSearchInfoRepository.aggs("id", AggsType.count, boolQuery, "resignationStatus");
        List<StatisticsBaseDto> statistics = new ArrayList<>();
        for (ResignationStatusEnum value : ResignationStatusEnum.values()) {
            statistics.add(new StatisticsBaseDto(value.name(), value.text, String.valueOf(map.getOrDefault(value.name(), "0"))));
        }
        return statistics;
    }
}
